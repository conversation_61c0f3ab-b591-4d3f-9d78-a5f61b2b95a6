// Use environment variable or fallback to default
const API_BASE_URL =
  typeof process !== 'undefined' && process.env && process.env.CODEx_API_BASE_URL
    ? process.env.CODEx_API_BASE_URL
    : (typeof window !== 'undefined' && window.CODEx_API_BASE_URL)
      ? window.CODEx_API_BASE_URL
      : 'http://localhost:9000/api';

// Servizio API per il Codex
export class CodexAPI {
  
  // Ottenere tutti gli elementi del codex per un progetto
  static async getCodexElements(projectId) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/${projectId}`);
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Errore nel caricamento elementi codex:', error);
      throw error;
    }
  }

  // Creare un nuovo elemento del codex
  static async createElement(element) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(element)
      });
      
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Errore nella creazione elemento codex:', error);
      throw error;
    }
  }

  // Aggiornare un elemento del codex
  static async updateElement(elementId, updates) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/${elementId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          updatedAt: new Date().toISOString()
        })
      });
      
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Errore nell\'aggiornamento elemento codex:', error);
      throw error;
    }
  }

  // Eliminare un elemento del codex
  static async deleteElement(elementId) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/${elementId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      console.error('Errore nell\'eliminazione elemento codex:', error);
      throw error;
    }
  }

  // Cercare elementi per nome
  static async searchElements(projectId, searchTerm) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/${projectId}/search?q=${encodeURIComponent(searchTerm)}`);
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Errore nella ricerca elementi codex:', error);
      throw error;
    }
  }

  // Ottenere elementi per tipo
  static async getElementsByType(projectId, type) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/${projectId}/type/${type}`);
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Errore nel caricamento elementi per tipo:', error);
      throw error;
    }
  }

  // Analizzare il testo per trovare riferimenti agli elementi del codex
  static async analyzeTextReferences(projectId, text) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/${projectId}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text })
      });
      
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Errore nell\'analisi riferimenti testo:', error);
      throw error;
    }
  }
}

// Servizio localStorage come fallback
export class CodexLocalStorage {
  static getStorageKey(projectId) {
    return `writertool_codex_${projectId}`;
  }

  static getCodexElements(projectId) {
    try {
      const data = localStorage.getItem(this.getStorageKey(projectId));
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Errore nel caricamento da localStorage:', error);
      return [];
    }
  }

  static saveCodexElements(projectId, elements) {
    try {
      localStorage.setItem(this.getStorageKey(projectId), JSON.stringify(elements));
      return true;
    } catch (error) {
      console.error('Errore nel salvataggio su localStorage:', error);
      return false;
    }
  }

  static createElement(projectId, element) {
    const elements = this.getCodexElements(projectId);
    elements.push(element);
    try {
      this.saveCodexElements(projectId, elements);
    } catch (error) {
      console.error('Errore nel salvataggio elemento su localStorage:', error);
      throw error;
    }
    return element;
  }

  static updateElement(projectId, elementId, updates) {
    const elements = this.getCodexElements(projectId);
    const index = elements.findIndex(el => el.id === elementId);
    
    if (index !== -1) {
      elements[index] = { ...elements[index], ...updates };
      this.saveCodexElements(projectId, elements);
      return elements[index];
    }
    
    throw new Error('Elemento non trovato');
  }

  static deleteElement(projectId, elementId) {
    const elements = this.getCodexElements(projectId);
    const filteredElements = elements.filter(el => el.id !== elementId);
    this.saveCodexElements(projectId, filteredElements);
    return true;
  }

  static searchElements(projectId, searchTerm) {
    const elements = this.getCodexElements(projectId);
    const term = searchTerm.toLowerCase();
    
    return elements.filter(element => {
      const aliases = Array.isArray(element.aliases) ? element.aliases : [];
      const searchableText = [
        element.name,
        ...aliases,
        element.description,
        ...(element.tags || [])
      ].join(' ').toLowerCase();
      return searchableText.includes(term);
    });
  }

  static getElementsByType(projectId, type) {
    const elements = this.getCodexElements(projectId);
    return elements.filter(element => element.type === type);
  }
}
