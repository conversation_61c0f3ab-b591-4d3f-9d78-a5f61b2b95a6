import React, { useState } from 'react';
import { Plus, Search, User, MapPin, Package, Lightbulb, Filter } from 'lucide-react';
import { CODEX_TYPES } from '../services/codexTypes';
import { useCodex } from '../hooks/useCodex';
import CodexElementModal from './CodexElementModal';

const CodexTab = ({ currentProject }) => {
  const [selectedType, setSelectedType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedElement, setSelectedElement] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createType, setCreateType] = useState(CODEX_TYPES.CHARACTER);

  const {
    elements,
    isLoading,
    error,
    createElement,
    updateElement,
    deleteElement,
    searchElements
  } = useCodex(currentProject?.id);

  // Filtra elementi per tipo e ricerca
  const filteredElements = elements.filter(element => {
    const matchesType = selectedType === 'all' || element.type === selectedType;
    const matchesSearch = !searchTerm || 
      element.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      element.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      element.aliases.some(alias => alias.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesType && matchesSearch;
  });

  // Ottieni l'icona per tipo
  const getTypeIcon = (type) => {
    const icons = {
      character: User,
      location: MapPin,
      object: Package,
      concept: Lightbulb
    };
    return icons[type] || User;
  };

  // Ottieni il colore per tipo
  const getTypeColor = (type) => {
    const colors = {
      character: 'text-green-600 bg-green-50 border-green-200',
      location: 'text-purple-600 bg-purple-50 border-purple-200',
      object: 'text-orange-600 bg-orange-50 border-orange-200',
      concept: 'text-blue-600 bg-blue-50 border-blue-200'
    };
    return colors[type] || 'text-gray-600 bg-gray-50 border-gray-200';
  };

  // Gestisce il click su un elemento
  const handleElementClick = (element) => {
    setSelectedElement(element);
    setShowModal(true);
  };

  // Gestisce la creazione di un nuovo elemento
  const handleCreateElement = async (formData) => {
    try {
      await createElement(createType, formData);
      setShowCreateForm(false);
    } catch (error) {
      console.error('Errore nella creazione:', error);
    }
  };

  // Gestisce l'eliminazione
  const handleDeleteElement = async (element) => {
    if (window.confirm(`Sei sicuro di voler eliminare "${element.name}"?`)) {
      try {
        await deleteElement(element.id);
        setShowModal(false);
      } catch (error) {
        console.error('Errore nell\'eliminazione:', error);
      }
    }
  };

  if (!currentProject) {
    return (
      <div className="text-center py-12">
        <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">Seleziona un progetto per accedere al Codex</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Codex - {currentProject.title}</h2>
          <p className="text-sm text-gray-600">
            {elements.length} elementi catalogati
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded flex items-center gap-2 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Nuovo Elemento
        </button>
      </div>

      {/* Filtri e ricerca */}
      <div className="flex gap-4 items-center">
        {/* Ricerca */}
        <div className="flex-1 relative">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Cerca elementi..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Filtro per tipo */}
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Tutti i tipi</option>
            <option value="character">Personaggi</option>
            <option value="location">Luoghi</option>
            <option value="object">Oggetti</option>
            <option value="concept">Concetti</option>
          </select>
        </div>
      </div>

      {/* Lista elementi */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {isLoading ? (
          <div className="col-span-full text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="text-gray-600 mt-2">Caricamento...</p>
          </div>
        ) : filteredElements.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">
              {searchTerm ? 'Nessun elemento trovato' : 'Nessun elemento nel codex'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => setShowCreateForm(true)}
                className="mt-4 text-blue-600 hover:text-blue-800"
              >
                Crea il primo elemento
              </button>
            )}
          </div>
        ) : (
          filteredElements.map((element) => {
            const Icon = getTypeIcon(element.type);
            const colorClass = getTypeColor(element.type);
            
            return (
              <div
                key={element.id}
                onClick={() => handleElementClick(element)}
                className={`${colorClass} p-4 rounded-lg border cursor-pointer hover:shadow-md transition-shadow`}
              >
                <div className="flex items-start gap-3">
                  <Icon className="w-5 h-5 mt-1 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium truncate">{element.name}</h3>
                    <p className="text-sm opacity-75 capitalize">{element.type}</p>
                    {element.description && (
                      <p className="text-sm mt-1 line-clamp-2">
                        {element.description.substring(0, 100)}
                        {element.description.length > 100 ? '...' : ''}
                      </p>
                    )}
                    {element.aliases.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {element.aliases.slice(0, 3).map((alias, index) => (
                          <span
                            key={index}
                            className="text-xs px-2 py-1 bg-white bg-opacity-50 rounded"
                          >
                            {alias}
                          </span>
                        ))}
                        {element.aliases.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{element.aliases.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Modale elemento */}
      <CodexElementModal
        element={selectedElement}
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onEdit={(element) => {
          // TODO: Implementare form di modifica
          console.log('Edit:', element);
        }}
        onDelete={handleDeleteElement}
      />

      {/* Form creazione (semplificato per ora) */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">Nuovo Elemento</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tipo
                </label>
                <select
                  value={createType}
                  onChange={(e) => setCreateType(e.target.value)}
                  className="w-full border border-gray-300 rounded px-3 py-2"
                >
                  <option value="character">Personaggio</option>
                  <option value="location">Luogo</option>
                  <option value="object">Oggetto</option>
                  <option value="concept">Concetto</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome
                </label>
                <input
                  type="text"
                  placeholder="Nome dell'elemento..."
                  className="w-full border border-gray-300 rounded px-3 py-2"
                />
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 rounded"
                >
                  Annulla
                </button>
                <button
                  onClick={() => {
                    // TODO: Implementare creazione
                    setShowCreateForm(false);
                  }}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 rounded"
                >
                  Crea
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CodexTab;
