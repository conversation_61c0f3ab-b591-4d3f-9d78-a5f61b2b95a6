import React, { useMemo } from 'react';

const TextWithCodexLinks = ({ 
  text, 
  references, 
  onElementClick,
  className = "",
  linkClassName = "text-blue-600 hover:text-blue-800 underline cursor-pointer bg-blue-50 px-1 rounded"
}) => {
  
  // Processa il testo per creare i link
  const processedText = useMemo(() => {
    if (!text || !references || references.length === 0) {
      return [{ type: 'text', content: text || '' }];
    }

    // Ordina i riferimenti per posizione
    const sortedReferences = [...references].sort((a, b) => a.startIndex - b.startIndex);
    
    const parts = [];
    let lastIndex = 0;

    sortedReferences.forEach((ref, index) => {
      // Aggiungi il testo prima del riferimento
      if (ref.startIndex > lastIndex) {
        const beforeText = text.substring(lastIndex, ref.startIndex);
        if (beforeText) {
          parts.push({ type: 'text', content: beforeText });
        }
      }

      // Aggiungi il riferimento come link
      const linkText = text.substring(ref.startIndex, ref.endIndex);
      parts.push({
        type: 'link',
        content: linkText,
        element: ref.element,
        reference: ref
      });

      lastIndex = ref.endIndex;
    });

    // Aggiungi il testo rimanente
    if (lastIndex < text.length) {
      const remainingText = text.substring(lastIndex);
      if (remainingText) {
        parts.push({ type: 'text', content: remainingText });
      }
    }

    return parts;
  }, [text, references]);

  // Gestisce il click su un elemento
  const handleElementClick = (element, reference, event) => {
    event.preventDefault();
    if (onElementClick) {
      onElementClick(element, reference);
    }
  };

  // Ottieni il colore per tipo di elemento
  const getElementColor = (elementType) => {
    const colors = {
      character: 'text-green-600 hover:text-green-800 bg-green-50',
      location: 'text-purple-600 hover:text-purple-800 bg-purple-50',
      object: 'text-orange-600 hover:text-orange-800 bg-orange-50',
      concept: 'text-blue-600 hover:text-blue-800 bg-blue-50'
    };
    return colors[elementType] || 'text-gray-600 hover:text-gray-800 bg-gray-50';
  };

  // Ottieni l'icona per tipo di elemento
  const getElementIcon = (elementType) => {
    const icons = {
      character: '👤',
      location: '📍',
      object: '🔧',
      concept: '💡'
    };
    return icons[elementType] || '📝';
  };

  return (
    <div className={className}>
      {processedText.map((part, index) => {
        if (part.type === 'text') {
          return <span key={index}>{part.content}</span>;
        } else if (part.type === 'link') {
          const colorClass = getElementColor(part.element.type);
          const icon = getElementIcon(part.element.type);
          
          return (
            <span
              key={index}
              className={`${colorClass} underline cursor-pointer px-1 rounded transition-colors inline-flex items-center gap-1`}
              onClick={(e) => handleElementClick(part.element, part.reference, e)}
              title={`${part.element.type}: ${part.element.name}${part.element.description ? ' - ' + part.element.description.substring(0, 100) + '...' : ''}`}
            >
              <span className="text-xs">{icon}</span>
              {part.content}
            </span>
          );
        }
        return null;
      })}
    </div>
  );
};

export default TextWithCodexLinks;
