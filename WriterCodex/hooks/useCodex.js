import { useState, useEffect, useCallback } from 'react';
import { CodexAPI, CodexLocalStorage } from '../services/codexAPI';
import { createCodexElement, validateCodexElement, findElementsByName, getAllNames } from '../services/codexTypes';

export const useCodex = (projectId) => {
  const [elements, setElements] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isConnected, setIsConnected] = useState(true);

  // Carica elementi del codex
  const loadElements = useCallback(async () => {
    if (!projectId) return;

    setIsLoading(true);
    setError(null);

    try {
      let data;
      if (isConnected) {
        data = await CodexAPI.getCodexElements(projectId);
      } else {
        data = CodexLocalStorage.getCodexElements(projectId);
      }
      setElements(data || []);
    } catch (err) {
      console.error('Errore nel caricamento elementi codex:', err);
      setError(err.message);
      
      // Fallback a localStorage se API fallisce
      if (isConnected) {
        setIsConnected(false);
        try {
          const data = CodexLocalStorage.getCodexElements(projectId);
          setElements(data || []);
        } catch (localErr) {
          console.error('Errore anche con localStorage:', localErr);
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [projectId, isConnected]);

  // Crea nuovo elemento
  const createElement = useCallback(async (type, data) => {
    if (!projectId) {
      throw new Error('ID progetto richiesto');
    }

    const element = createCodexElement(type, { ...data, projectId });
    const validation = validateCodexElement(element);
    
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    setIsLoading(true);
    setError(null);

    try {
      let createdElement;
      if (isConnected) {
        createdElement = await CodexAPI.createElement(element);
      } else {
        createdElement = CodexLocalStorage.createElement(projectId, element);
      }

      setElements(prev => [...prev, createdElement]);
      return createdElement;
    } catch (err) {
      console.error('Errore nella creazione elemento:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [projectId, isConnected]);

  // Aggiorna elemento
  const updateElement = useCallback(async (elementId, updates) => {
    setIsLoading(true);
    setError(null);

    try {
      let updatedElement;
      if (isConnected) {
        updatedElement = await CodexAPI.updateElement(elementId, updates);
      } else {
        updatedElement = CodexLocalStorage.updateElement(projectId, elementId, updates);
      }

      setElements(prev => 
        prev.map(el => el.id === elementId ? updatedElement : el)
      );
      return updatedElement;
    } catch (err) {
      console.error('Errore nell\'aggiornamento elemento:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [projectId, isConnected]);

  // Elimina elemento
  const deleteElement = useCallback(async (elementId) => {
    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        await CodexAPI.deleteElement(elementId);
      } else {
        CodexLocalStorage.deleteElement(projectId, elementId);
      }

      setElements(prev => prev.filter(el => el.id !== elementId));
      return true;
    } catch (err) {
      console.error('Errore nell\'eliminazione elemento:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [projectId, isConnected]);

  // Cerca elementi
  const searchElements = useCallback(async (searchTerm) => {
    if (!searchTerm || !projectId) return [];

    try {
      if (isConnected) {
        return await CodexAPI.searchElements(projectId, searchTerm);
      } else {
        return CodexLocalStorage.searchElements(projectId, searchTerm);
      }
    } catch (err) {
      console.error('Errore nella ricerca:', err);
      return findElementsByName(elements, searchTerm);
    }
  }, [projectId, isConnected, elements]);

  // Ottieni elementi per tipo
  const getElementsByType = useCallback((type) => {
    return elements.filter(element => element.type === type);
  }, [elements]);

  // Analizza testo per trovare riferimenti
  const analyzeTextReferences = useCallback((text) => {
    if (!text || !elements.length) return [];

    const references = [];
    const textLower = text.toLowerCase();

    elements.forEach(element => {
      const names = getAllNames(element);
      
      names.forEach(name => {
        if (name && name.trim()) {
          const nameLower = name.toLowerCase();
          let startIndex = 0;
          
          while (true) {
            const index = textLower.indexOf(nameLower, startIndex);
            if (index === -1) break;
            
            // Verifica che sia una parola completa (non parte di un'altra parola)
            const isWordBoundary = (pos) => {
              if (pos === 0 || pos === text.length) return true;
              const char = text[pos];
              return /\s|[.,!?;:()[\]{}'"«»""''—–-]/.test(char);
            };
            
            if (isWordBoundary(index) && isWordBoundary(index + name.length)) {
              references.push({
                element,
                name,
                startIndex: index,
                endIndex: index + name.length,
                context: text.substring(Math.max(0, index - 50), Math.min(text.length, index + name.length + 50))
              });
            }
            
            startIndex = index + 1;
          }
        }
      });
    });

    // Ordina per posizione nel testo
    return references.sort((a, b) => a.startIndex - b.startIndex);
  }, [elements]);

  // Carica elementi all'avvio
  useEffect(() => {
    loadElements();
  }, [loadElements]);

  return {
    elements,
    isLoading,
    error,
    isConnected,
    loadElements,
    createElement,
    updateElement,
    deleteElement,
    searchElements,
    getElementsByType,
    analyzeTextReferences
  };
};
