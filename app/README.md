# Editor Letterario Avanzato

Un'applicazione React per l'analisi avanzata di testi letterari con supporto AI per l'identificazione e profilazione dei personaggi.

## 🚀 Funzionalità

- **Analisi stilistica completa**: Leggibilità, varietà lessicale, struttura
- **Identificazione dialoghi**: Riconoscimento automatico con evidenziazione
- **Classificazione frasi**: Azione, narrativo, descrittivo, dialogo
- **Analisi personaggi AI**: Estrazione intelligente con OpenRouter
- **Metriche letterarie**: TTR, Hapax Legomena, analisi sillabica
- **Suggerimenti miglioramento**: Consigli automatici per la scrittura

## 🤖 Analisi AI Avanzata

L'applicazione supporta l'analisi AI dei personaggi tramite OpenRouter, che offre:

- Identificazione personaggi anche senza nome esplicito
- Collegamento di pronomi e riferimenti
- Estrazione caratteristiche fisiche e psicologiche
- Analisi relazioni tra personaggi
- Valutazione importanza narrativa

## ⚙️ Configurazione

### 1. Installazione

```bash
npm install
```

### 2. Configurazione API (Opzionale)

Per abilitare l'analisi AI avanzata:

1. Copia il file di esempio:
```bash
cp .env.example .env
```

2. Registrati su [OpenRouter](https://openrouter.ai/) e ottieni una API key gratuita

3. Modifica il file `.env`:
```env
REACT_APP_OPENROUTER_API_KEY=sk-or-v1-your-actual-api-key
```

4. Riavvia l'applicazione

### 3. Avvio

```bash
npm start
```

L'applicazione sarà disponibile su `http://localhost:3000`

## 📝 Utilizzo

1. **Inserisci il testo** nella scheda "Testo"
2. **Clicca "Analizza Testo"** per avviare l'analisi
3. **Esplora i risultati** nelle diverse schede:
   - **Dashboard**: Statistiche generali
   - **Evidenziato**: Testo con dialoghi evidenziati
   - **Classificazione Frasi**: Analisi tipo di frase
   - **Analisi Personaggi**: Profili dei personaggi (AI se configurata)
   - **Suggerimenti**: Consigli per migliorare

## 🔧 Tecnologie

- **React 18** con Hooks
- **Tailwind CSS** per lo styling
- **Lucide React** per le icone
- **OpenRouter API** per l'analisi AI
- **Algoritmi NLP** personalizzati

## 📊 Modelli AI Supportati

- **mistralai/mistral-small-3.2-24b-instruct:free** (gratuito)
- Fallback automatico su algoritmi locali se API non disponibile

## 🛡️ Privacy

- L'API key è memorizzata localmente nel file `.env`
- I testi vengono inviati a OpenRouter solo se l'API è configurata
- Nessun dato viene memorizzato permanentemente

## 📄 Licenza

MIT License
