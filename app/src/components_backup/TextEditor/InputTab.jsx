import React from 'react';
import { TrendingUp, Save } from 'lucide-react';

const InputTab = ({
  text,
  setText,
  onAnalyze,
  onSave,
  isAnalyzing,
  openRouterApiKey,
  currentProject,
  currentVersionId,
  projectName,
  setProjectName,
  // Props per gestione capitoli
  chapters,
  projects,
  onCreateChapter,
  onCreateSubchapter,
  onUpdateSubchapter,
  onLoadSubchapter,
  onSwitchToChaptersTab,
  // Props per selezione progetto
  selectedProjectForAnalysis,
  setSelectedProjectForAnalysis,
  // Props per display progetto attivo
  activeProject,
  setActiveProject,
  setActiveVersionId
}) => {

  return (
    <div className="space-y-4">
      {/* Stato API */}
      <div className={`p-3 rounded-lg border ${
        openRouterApiKey ? 
          'bg-green-50 border-green-200' : 
          'bg-yellow-50 border-yellow-200'
      }`}>
        <h4 className={`font-semibold mb-2 flex items-center gap-2 ${
          openRouterApiKey ? 'text-green-800' : 'text-yellow-800'
        }`}>
          🤖 Analisi AI Avanzata
        </h4>
        <p className={`text-sm ${
          openRouterApiKey ? 'text-green-700' : 'text-yellow-700'
        }`}>
          {openRouterApiKey ? 
            '✅ API OpenRouter configurata - Analisi personaggi con AI attiva' : 
            '⚠️ API non configurata - Verrà usato l\'algoritmo locale (meno preciso)'}
        </p>
        {!openRouterApiKey && (
          <p className="text-xs text-yellow-600 mt-1">
            Per attivare l'AI, aggiungi REACT_APP_OPENROUTER_API_KEY nel file .env
          </p>
        )}
      </div>



      {/* Editor di testo per analisi diretta */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Oppure scrivi testo per analisi diretta
        </label>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Scrivi qui del testo per analizzarlo direttamente (senza salvarlo nei capitoli)..."
          className="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
        />
      </div>

      {/* Selettore progetto per analisi */}
      <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
        <div className="flex items-center gap-2 mb-2">
          <Save className="w-4 h-4 text-gray-600" />
          <span className="font-medium text-gray-700">Progetto Target per Analisi</span>
        </div>

        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            Seleziona il progetto dove salvare l'analisi (opzionale - se non selezionato verrà chiesto dopo)
          </p>

          <select
            value={selectedProjectForAnalysis?.id || ''}
            onChange={(e) => {
              if (e.target.value === '') {
                setSelectedProjectForAnalysis(null);
                // Non resettare activeProject quando si deseleziona
              } else {
                const project = projects.find(p => p.id === e.target.value);
                setSelectedProjectForAnalysis(project);

                // Aggiorna anche il display del progetto attivo
                if (project && setActiveProject) {
                  setActiveProject(project);
                  // Imposta la versione più recente come attiva SOLO se non siamo in modalità lavoro
                  if (!text && project.versions && project.versions.length > 0 && setActiveVersionId) {
                    const latestVersion = project.versions[0]; // La più recente è all'indice 0
                    setActiveVersionId(latestVersion.id);
                  } else if (text && setActiveVersionId) {
                    // In modalità lavoro, resetta activeVersionId per mostrare la prossima versione
                    setActiveVersionId(null);
                  }
                }
              }
            }}
            className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">🔍 Scegli dopo l'analisi...</option>
            {projects.map(project => (
              <option key={project.id} value={project.id}>
                📁 {project.title} ({project.versions?.length || 0} versioni)
              </option>
            ))}
          </select>

          {selectedProjectForAnalysis && (
            <div className="bg-blue-50 p-2 rounded border border-blue-200">
              <div className="text-sm font-medium text-blue-800">
                ✅ Progetto selezionato: {selectedProjectForAnalysis.title}
              </div>
              <div className="text-xs text-blue-600">
                L'analisi verrà salvata direttamente in questo progetto
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-3">
        <div className="text-sm text-gray-600 text-center">
          {text.trim() ? `${text.trim().split(/\s+/).length} parole` : 'Nessun testo inserito'}
        </div>

        <div className="flex justify-center">
          <button
            onClick={onAnalyze}
            disabled={!text.trim() || isAnalyzing}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isAnalyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                Analizzando...
              </>
            ) : (
              <>
                <TrendingUp className="w-4 h-4" />
                Analizza Testo {openRouterApiKey ? '(con AI)' : '(algoritmo locale)'}
              </>
            )}
          </button>
        </div>
      </div>


    </div>
  );
};

export default InputTab;
