import React, { useState, useEffect } from 'react';
import { Save, BarChart3, FileText, ArrowLeft, Link, RefreshCw } from 'lucide-react';
import SyncPanel from '../Sync/SyncPanel';
import useSyncManager from '../../hooks/useSyncManager';

const SubchapterEditor = ({ 
  chapter,
  subchapter,
  onUpdateSubchapter,
  onAnalyzeText,
  onBackToChapters,
  onContinueFromPrevious,
  isAnalyzing 
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [text, setText] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showSyncPanel, setShowSyncPanel] = useState(false);

  // Hook per la gestione della sincronizzazione
  const syncManager = useSyncManager();

  useEffect(() => {
    if (subchapter) {
      setTitle(subchapter.title);
      setDescription(subchapter.description || '');
      setText(subchapter.liveVersion?.text || '');
      setHasUnsavedChanges(false);
    }
  }, [subchapter]);

  const handleSave = () => {
    if (chapter && subchapter) {
      onUpdateSubchapter(chapter.id, subchapter.id, {
        title: title.trim(),
        description: description.trim(),
        text: text.trim()
      });
      setHasUnsavedChanges(false);
    }
  };

  const handleAnalyze = () => {
    if (text.trim()) {
      // Prima salva, poi analizza
      handleSave();
      onAnalyzeText(text.trim());
    }
  };

  const handleContinueFromPrevious = () => {
    if (chapter && subchapter) {
      const currentIndex = chapter.subchapters.findIndex(s => s.id === subchapter.id);
      if (currentIndex > 0) {
        const previousSubchapter = chapter.subchapters[currentIndex - 1];
        const continuationText = text ? `${text}\n\n` : '';
        const newText = `${continuationText}[Continua da: ${previousSubchapter.title}]\n\n${previousSubchapter.text}`;
        setText(newText);
        setHasUnsavedChanges(true);
      }
    }
  };

  const handleSubchapterUpdated = (updatedSubchapter) => {
    if (updatedSubchapter) {
      setText(updatedSubchapter.liveVersion?.text || '');
      setTitle(updatedSubchapter.title || '');
      setDescription(updatedSubchapter.description || '');
      setHasUnsavedChanges(false);
    }
  };

  const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
  const charCount = text.length;

  if (!chapter || !subchapter) {
    return (
      <div className="text-center py-12">
        <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">Seleziona un sottocapitolo per iniziare a scrivere</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <button
              onClick={onBackToChapters}
              className="text-gray-600 hover:text-gray-800 p-1"
              title="Torna ai capitoli"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h2 className="text-lg font-semibold text-gray-800">{chapter.title}</h2>
              <p className="text-sm text-gray-600">{subchapter.title}</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Statistiche */}
            <div className="text-sm text-gray-500 mr-4">
              {wordCount} parole • {charCount} caratteri
            </div>

            {/* Pulsante sincronizzazione */}
            <button
              onClick={() => setShowSyncPanel(true)}
              className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-sm flex items-center gap-1"
              title="Sincronizza con versioni"
            >
              <RefreshCw className="w-4 h-4" />
              Sync
            </button>

            {/* Pulsante continua dal precedente */}
            {chapter.subchapters.findIndex(s => s.id === subchapter.id) > 0 && (
              <button
                onClick={handleContinueFromPrevious}
                className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-sm flex items-center gap-1"
                title="Continua dal sottocapitolo precedente"
              >
                <Link className="w-4 h-4" />
                Continua
              </button>
            )}

            {/* Pulsante salva */}
            <button
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
              className={`px-3 py-1 rounded text-sm flex items-center gap-1 ${
                hasUnsavedChanges
                  ? 'bg-green-500 hover:bg-green-600 text-white'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              <Save className="w-4 h-4" />
              Salva
            </button>

            {/* Pulsante analizza */}
            <button
              onClick={handleAnalyze}
              disabled={!text.trim() || isAnalyzing}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm flex items-center gap-1"
            >
              <BarChart3 className="w-4 h-4" />
              {isAnalyzing ? 'Analizzando...' : 'Analizza'}
            </button>
          </div>
        </div>

        {/* Campi titolo e descrizione */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Titolo</label>
            <input
              type="text"
              value={title}
              onChange={(e) => {
                setTitle(e.target.value);
                setHasUnsavedChanges(true);
              }}
              className="w-full p-2 border border-gray-300 rounded"
              placeholder="Titolo del sottocapitolo"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Descrizione</label>
            <input
              type="text"
              value={description}
              onChange={(e) => {
                setDescription(e.target.value);
                setHasUnsavedChanges(true);
              }}
              className="w-full p-2 border border-gray-300 rounded"
              placeholder="Breve descrizione (opzionale)"
            />
          </div>
        </div>
      </div>

      {/* Editor di testo */}
      <div className="flex-1 p-4">
        <div className="h-full">
          <label className="block text-sm font-medium text-gray-700 mb-2">Contenuto</label>
          <textarea
            value={text}
            onChange={(e) => {
              setText(e.target.value);
              setHasUnsavedChanges(true);
            }}
            className="w-full h-full p-4 border border-gray-300 rounded resize-none font-mono text-sm leading-relaxed"
            placeholder="Inizia a scrivere il tuo sottocapitolo qui..."
          />
        </div>
      </div>

      {/* Footer con informazioni */}
      {hasUnsavedChanges && (
        <div className="bg-yellow-50 border-t border-yellow-200 p-2 text-center">
          <p className="text-sm text-yellow-700">
            Ci sono modifiche non salvate. Clicca "Salva" per salvare le modifiche.
          </p>
        </div>
      )}

      {/* Pannello di Sincronizzazione */}
      <SyncPanel
        chapterId={chapter?.id}
        subchapterId={subchapter?.id}
        isOpen={showSyncPanel}
        onClose={() => setShowSyncPanel(false)}
        syncManager={syncManager}
        onSubchapterUpdated={handleSubchapterUpdated}
      />
    </div>
  );
};

export default SubchapterEditor;
