import React, { useState } from 'react';
import { Book, Plus, Trash2, FileTex<PERSON>, BarChart3, ChevronDown, ChevronRight, Upload, Link } from 'lucide-react';
import SyncStatusIndicator from '../Sync/SyncStatusIndicator';

const ChapterManager = ({ 
  chapters, 
  currentChapter, 
  currentSubchapter,
  onCreateChapter,
  onCreateSubchapter,
  onLoadChapter,
  onLoadSubchapter,
  onDeleteChapter,
  onDeleteSubchapter,
  onUpdateSubchapter,
  getChapterStats,
  onTransferToProject,
  onSelectVersionForSubchapter
}) => {
  const [expandedChapters, setExpandedChapters] = useState(new Set());
  const [showNewChapterForm, setShowNewChapterForm] = useState(false);
  const [showNewSubchapterForm, setShowNewSubchapterForm] = useState(null);
  const [newChapterTitle, setNewChapterTitle] = useState('');
  const [newChapterDescription, setNewChapterDescription] = useState('');
  const [newSubchapterTitle, setNewSubchapterTitle] = useState('');
  const [newSubchapterDescription, setNewSubchapterDescription] = useState('');

  const toggleChapterExpansion = (chapterId) => {
    const newExpanded = new Set(expandedChapters);
    if (newExpanded.has(chapterId)) {
      newExpanded.delete(chapterId);
    } else {
      newExpanded.add(chapterId);
    }
    setExpandedChapters(newExpanded);
  };

  const handleCreateChapter = () => {
    if (newChapterTitle.trim()) {
      onCreateChapter(newChapterTitle.trim(), newChapterDescription.trim());
      setNewChapterTitle('');
      setNewChapterDescription('');
      setShowNewChapterForm(false);
    }
  };

  const handleCreateSubchapter = (chapterId) => {
    if (newSubchapterTitle.trim()) {
      onCreateSubchapter(chapterId, newSubchapterTitle.trim(), '', newSubchapterDescription.trim());
      setNewSubchapterTitle('');
      setNewSubchapterDescription('');
      setShowNewSubchapterForm(null);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
          <Book className="w-5 h-5 text-blue-600" />
          Gestione Capitoli
        </h3>
        <button
          onClick={() => setShowNewChapterForm(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm flex items-center gap-1"
        >
          <Plus className="w-4 h-4" />
          Nuovo Capitolo
        </button>
      </div>

      {/* Form nuovo capitolo */}
      {showNewChapterForm && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-800 mb-2">Nuovo Capitolo</h4>
          <input
            type="text"
            placeholder="Titolo del capitolo"
            value={newChapterTitle}
            onChange={(e) => setNewChapterTitle(e.target.value)}
            className="w-full p-2 border border-blue-300 rounded mb-2"
          />
          <textarea
            placeholder="Descrizione (opzionale)"
            value={newChapterDescription}
            onChange={(e) => setNewChapterDescription(e.target.value)}
            className="w-full p-2 border border-blue-300 rounded mb-2 h-20"
          />
          <div className="flex gap-2">
            <button
              onClick={handleCreateChapter}
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
            >
              Crea
            </button>
            <button
              onClick={() => {
                setShowNewChapterForm(false);
                setNewChapterTitle('');
                setNewChapterDescription('');
              }}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm"
            >
              Annulla
            </button>
          </div>
        </div>
      )}

      {/* Lista capitoli */}
      {/* Riepilogo generale collegamenti */}
      {chapters.length > 0 && (
        <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
            <Link className="w-5 h-5 text-blue-600" />
            Stato Collegamenti Progetti → Capitoli
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {chapters.map(chapter => {
              const linkedCount = chapter.subchapters?.filter(sub => sub.liveVersion?.sourceLink).length || 0;
              const totalCount = chapter.subchapters?.length || 0;
              const percentage = totalCount > 0 ? Math.round((linkedCount / totalCount) * 100) : 0;

              return (
                <div key={chapter.id} className="bg-white p-3 rounded border border-gray-200">
                  <div className="font-medium text-gray-800 mb-2">{chapter.title}</div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">{linkedCount}/{totalCount} collegati</span>
                    <span className={`text-sm font-medium ${
                      percentage === 100 ? 'text-green-600' :
                      percentage > 0 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {percentage}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        percentage === 100 ? 'bg-green-500' :
                        percentage > 0 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  {linkedCount > 0 && (
                    <div className="mt-2 text-xs text-gray-500">
                      {chapter.subchapters
                        ?.filter(sub => sub.liveVersion?.sourceLink)
                        .map(sub => sub.liveVersion.sourceLink.projectTitle)
                        .filter((title, index, arr) => arr.indexOf(title) === index)
                        .join(', ')
                      }
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          <div className="mt-4 text-sm text-gray-600 text-center">
            {(() => {
              const totalSubchapters = chapters.reduce((sum, ch) => sum + (ch.subchapters?.length || 0), 0);
              const totalLinked = chapters.reduce((sum, ch) =>
                sum + (ch.subchapters?.filter(sub => sub.liveVersion?.sourceLink).length || 0), 0
              );
              return `Totale: ${totalLinked}/${totalSubchapters} sottocapitoli collegati (${totalSubchapters > 0 ? Math.round((totalLinked / totalSubchapters) * 100) : 0}%)`;
            })()}
          </div>
        </div>
      )}

      <div className="space-y-2">
        {chapters.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Book className="w-12 h-12 text-gray-300 mx-auto mb-2" />
            <p>Nessun capitolo creato</p>
            <p className="text-sm">Crea il tuo primo capitolo per iniziare</p>
          </div>
        ) : (
          chapters.map(chapter => {
            const stats = getChapterStats(chapter.id);
            const isExpanded = expandedChapters.has(chapter.id);
            const isCurrentChapter = currentChapter?.id === chapter.id;

            return (
              <div key={chapter.id} className={`border rounded-lg ${isCurrentChapter ? 'border-blue-300 bg-blue-50' : 'border-gray-200'}`}>
                {/* Header capitolo */}
                <div className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 flex-1">
                      <button
                        onClick={() => toggleChapterExpansion(chapter.id)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                      </button>
                      <button
                        onClick={() => onLoadChapter(chapter.id)}
                        className="text-left flex-1"
                      >
                        <h4 className="font-medium text-gray-800">{chapter.title}</h4>
                        {chapter.description && (
                          <p className="text-sm text-gray-600">{chapter.description}</p>
                        )}
                      </button>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <div className="text-xs text-gray-500">
                        {stats?.totalSubchapters || 0} sottocap. • {stats?.totalWords || 0} parole
                      </div>
                      {/* Indicatore collegamenti rapido */}
                      {chapter.subchapters && chapter.subchapters.length > 0 && (
                        <div className="text-xs">
                          {(() => {
                            const linkedCount = chapter.subchapters.filter(sub => sub.liveVersion?.sourceLink).length;
                            const totalCount = chapter.subchapters.length;
                            return linkedCount === totalCount ? (
                              <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                                🔗 {linkedCount}/{totalCount} collegati
                              </span>
                            ) : linkedCount > 0 ? (
                              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                🔗 {linkedCount}/{totalCount} collegati
                              </span>
                            ) : (
                              <span className="bg-red-100 text-red-800 px-2 py-1 rounded">
                                ❌ 0/{totalCount} collegati
                              </span>
                            );
                          })()}
                        </div>
                      )}
                      <button
                        onClick={() => setShowNewSubchapterForm(chapter.id)}
                        className="text-green-600 hover:text-green-700 p-1"
                        title="Aggiungi sottocapitolo"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => onDeleteChapter(chapter.id)}
                        className="text-red-600 hover:text-red-700 p-1"
                        title="Elimina capitolo"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* Form nuovo sottocapitolo */}
                  {showNewSubchapterForm === chapter.id && (
                    <div className="mt-3 p-3 bg-green-50 rounded border border-green-200">
                      <h5 className="font-medium text-green-800 mb-2">Nuovo Sottocapitolo</h5>
                      <input
                        type="text"
                        placeholder="Titolo del sottocapitolo"
                        value={newSubchapterTitle}
                        onChange={(e) => setNewSubchapterTitle(e.target.value)}
                        className="w-full p-2 border border-green-300 rounded mb-2"
                      />
                      <textarea
                        placeholder="Descrizione (opzionale)"
                        value={newSubchapterDescription}
                        onChange={(e) => setNewSubchapterDescription(e.target.value)}
                        className="w-full p-2 border border-green-300 rounded mb-2 h-16"
                      />
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleCreateSubchapter(chapter.id)}
                          className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm"
                        >
                          Crea
                        </button>
                        <button
                          onClick={() => {
                            setShowNewSubchapterForm(null);
                            setNewSubchapterTitle('');
                            setNewSubchapterDescription('');
                          }}
                          className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm"
                        >
                          Annulla
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Lista sottocapitoli */}
                {isExpanded && (
                  <div className="border-t border-gray-200 bg-gray-50">
                    {chapter.subchapters.length === 0 ? (
                      <div className="p-4 text-center text-gray-500 text-sm">
                        Nessun sottocapitolo. Clicca + per aggiungerne uno.
                      </div>
                    ) : (
                      <div className="p-2">
                        {chapter.subchapters.map((subchapter, index) => {
                          const isCurrentSubchapter = currentSubchapter?.id === subchapter.id;
                          
                          return (
                            <div
                              key={subchapter.id}
                              className={`p-2 rounded mb-1 cursor-pointer ${
                                isCurrentSubchapter 
                                  ? 'bg-blue-100 border border-blue-300' 
                                  : 'bg-white hover:bg-gray-100 border border-gray-200'
                              }`}
                              onClick={() => onLoadSubchapter(chapter.id, subchapter.id)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <FileText className="w-4 h-4 text-gray-500" />
                                    <span className="font-medium text-sm">{subchapter.title}</span>
                                    {subchapter.analysis && (
                                      <BarChart3 className="w-4 h-4 text-green-500" title="Analizzato" />
                                    )}
                                    <SyncStatusIndicator subchapter={subchapter} compact={true} />
                                  </div>
                                  {subchapter.description && (
                                    <p className="text-xs text-gray-600 ml-6">{subchapter.description}</p>
                                  )}
                                  <div className="text-xs text-gray-500 ml-6">
                                    {subchapter.liveVersion?.word_count || subchapter.liveVersion?.wordCount || 0} parole • {subchapter.backupVersions?.length || 0} backup • {new Date(subchapter.updated_at || subchapter.updatedAt).toLocaleDateString()} {new Date(subchapter.updated_at || subchapter.updatedAt).toLocaleTimeString()}
                                  </div>
                                  {/* Collegamento versione - MOLTO VISIBILE */}
                                  {subchapter.liveVersion?.sourceLink ? (
                                    <div className="ml-6 mt-2 p-2 bg-gradient-to-r from-blue-50 to-green-50 border border-blue-300 rounded-lg">
                                      <div className="flex items-center gap-2 mb-1">
                                        <Link className="w-4 h-4 text-blue-600" />
                                        <span className="font-semibold text-blue-800 text-sm">🔗 COLLEGATO</span>
                                      </div>
                                      <div className="text-xs space-y-1">
                                        <div className="flex items-center gap-1">
                                          <span className="font-medium text-blue-700">📁 Progetto:</span>
                                          <span className="text-blue-800 font-semibold">{subchapter.liveVersion.sourceLink.projectTitle}</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                          <span className="font-medium text-green-700">📄 Versione:</span>
                                          <span className="text-green-800 font-semibold">{subchapter.liveVersion.sourceLink.versionName}</span>
                                        </div>
                                        <div className="text-gray-600">
                                          🕒 Collegato il {new Date(subchapter.liveVersion.sourceLink.linkedAt).toLocaleDateString()} {new Date(subchapter.liveVersion.sourceLink.linkedAt).toLocaleTimeString()}
                                        </div>
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="ml-6 mt-2 p-2 bg-gray-50 border border-gray-300 border-dashed rounded-lg">
                                      <div className="flex items-center gap-2 text-gray-500">
                                        <Link className="w-4 h-4" />
                                        <span className="text-sm font-medium">❌ NON COLLEGATO</span>
                                      </div>
                                      <div className="text-xs text-gray-400 mt-1">
                                        Clicca il pulsante verde 🔗 per collegare una versione di progetto
                                      </div>
                                    </div>
                                  )}
                                  <div className="ml-6">
                                    <SyncStatusIndicator subchapter={subchapter} compact={false} />
                                  </div>
                                </div>
                                <div className="flex gap-1">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onSelectVersionForSubchapter && onSelectVersionForSubchapter(chapter, subchapter);
                                    }}
                                    className="text-green-600 hover:text-green-700 p-1"
                                    title="Seleziona versione collegata"
                                  >
                                    <Link className="w-3 h-3" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onTransferToProject && onTransferToProject(chapter, subchapter);
                                    }}
                                    className="text-blue-600 hover:text-blue-700 p-1"
                                    title="Esporta a progetto"
                                  >
                                    <Upload className="w-3 h-3" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onDeleteSubchapter(chapter.id, subchapter.id);
                                    }}
                                    className="text-red-600 hover:text-red-700 p-1"
                                    title="Elimina sottocapitolo"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default ChapterManager;
