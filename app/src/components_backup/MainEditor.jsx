import { useState, useEffect } from 'react';
import { FileText, BarChart3, Eye, MessageSquare, Target, Brain, BookOpen, Zap, Layers, Film, PieChart, Users, History, Save, Plus } from 'lucide-react';
import toast from 'react-hot-toast';

// Importazioni componenti modulari
import DashboardTab from './Analysis/DashboardTab';
import CharactersTab from './Analysis/CharactersTab';
import PlotEventsTab from './PlotEvents/PlotEventsTab';
import ProjectsTab from './Projects/ProjectsTab';
import InputTab from './TextEditor/InputTab';
import HighlightedTab from './Analysis/HighlightedTab';
import ClassificationTab from './Analysis/ClassificationTab';
import SuggestionsTab from './Analysis/SuggestionsTab';
import NarrativeTab from './Analysis/NarrativeTab';
import AdvancedStyleTab from './Analysis/AdvancedStyleTab';
import SemanticTab from './Analysis/SemanticTab';
import LiteraryTab from './Analysis/LiteraryTab';
import StructureTab from './Analysis/StructureTab';
import ChapterManager from './Chapters/ChapterManager';
import SubchapterEditor from './Chapters/SubchapterEditor';
import TransferToChapterModal from './Transfer/TransferToChapterModal';
import TransferToProjectModal from './Transfer/TransferToProjectModal';
import VersionLinkModal from './Chapters/VersionLinkModal';
import LinkToChapterModal from './Projects/LinkToChapterModal';
// Importazioni hooks e utilities
import { useTextAnalysis } from '../hooks/useTextAnalysisNew';
import { useUnifiedProjectManager } from '../hooks/useUnifiedProjectManager';
import { useUnifiedChapterManager } from '../hooks/useUnifiedChapterManager';
import { usePlotEvents } from '../hooks/usePlotEvents';
import { useDebounce } from '../hooks/useDebounce';

const MainEditor = () => {
  const [text, setText] = useState('');
  const [activeTab, setActiveTab] = useState('input');

  // Debounce del testo per evitare analisi ad ogni keystroke
  const debouncedText = useDebounce(text, 1000); // 1 secondo di delay
  const [showProjectSelector, setShowProjectSelector] = useState(false);
  const [pendingText, setPendingText] = useState('');
  const [selectedProjectForAnalysis, setSelectedProjectForAnalysis] = useState(null);
  const [showVersionPrompt, setShowVersionPrompt] = useState(false);
  const [newVersionData, setNewVersionData] = useState(null);
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [showChapterSelector, setShowChapterSelector] = useState(false);
  const [selectedChapter, setSelectedChapter] = useState('');
  const [selectedSubchapter, setSelectedSubchapter] = useState('');
  const [newChapterName, setNewChapterName] = useState('');
  const [newSubchapterName, setNewSubchapterName] = useState('');

  // Stati separati per il display del progetto/versione attivo
  const [activeProject, setActiveProject] = useState(null);
  const [activeVersionId, setActiveVersionId] = useState(null);

  // Helper per gestione errori standardizzata
  const handleError = (error, context = 'Operazione', showToast = true) => {
    const errorMessage = error?.message || error || 'Errore sconosciuto';
    console.error(`❌ Errore ${context}:`, error);

    if (showToast) {
      toast.error(`❌ Errore durante ${context.toLowerCase()}:\n\n${errorMessage}`, {
        duration: 5000
      });
    }
  };

  // Helper per successo standardizzato
  const handleSuccess = (message, context = 'Operazione', duration = 3000) => {
    console.log(`✅ ${context}:`, message);
    toast.success(message, { duration });
  };

  // Custom hooks per la logica - useUnifiedProjectManager (sostituisce useProjectManagerServer)
  const {
    projects,
    currentProject,
    currentVersionId,
    projectName,
    setProjectName,
    isLoading: projectsLoading,
    error: projectsError,
    saveProject: saveProjectHook,
    loadProject,
    deleteProject,
    exportProject,
    importProject,
    getCurrentVersion,
    loadProjects,
    createVersion,
    isConnected
  } = useUnifiedProjectManager();

  const {
    analysis,
    setAnalysis,
    isAnalyzing,
    analyzeText,
    highlightText,
    classifySentence
  } = useTextAnalysis(debouncedText, currentProject, getCurrentVersion()); // Usa testo debouncato per performance

  const {
    plotEvents,
    showEventForm,
    setShowEventForm,
    editingEvent,
    addPlotEvent,
    deletePlotEvent,
    resolveMysterySeed,
    startEditingEvent,
    cancelEditing
  } = usePlotEvents();

  // Hook per gestione capitoli (unificato)
  const {
    chapters,
    currentChapter,
    currentSubchapter,
    isLoading: chaptersLoading,
    error: chaptersError,
    createChapter,
    createSubchapter,
    updateSubchapter,
    deleteChapter,
    deleteSubchapter,
    loadChapter,
    loadSubchapter,
    getCombinedChapterText,
    getChapterStats,
    linkVersionToSubchapter,
    unlinkVersionFromSubchapter,
    loadChapters
  } = useUnifiedChapterManager();

  // Hook per connessione server (ora incluso in useUnifiedProjectManager)
  // const { isConnected, isChecking, error: serverError, serverInfo } = useServerConnection();

  // Variabili temporanee per compatibilità (da rimuovere dopo test)
  const isChecking = projectsLoading;
  const serverError = projectsError;
  const serverInfo = isConnected ? { status: 'OK' } : null;

  // Stati per i modal di trasferimento
  const [showTransferToChapterModal, setShowTransferToChapterModal] = useState(false);
  const [showTransferToProjectModal, setShowTransferToProjectModal] = useState(false);
  const [showVersionLinkModal, setShowVersionLinkModal] = useState(false);
  const [showLinkToChapterModal, setShowLinkToChapterModal] = useState(false);
  const [transferSource, setTransferSource] = useState(null);

  // Funzioni di trasferimento
  const handleTransferToChapter = (project, version) => {
    setTransferSource({ project, version });
    setShowTransferToChapterModal(true);
  };

  const handleTransferToProject = (chapter, subchapter) => {
    setTransferSource({ chapter, subchapter });
    setShowTransferToProjectModal(true);
  };

  const handleSelectVersionForSubchapter = (chapter, subchapter) => {
    setTransferSource({ chapter, subchapter });
    setShowVersionLinkModal(true);
  };

  const handleLinkVersion = (chapterId, subchapterId, projectId, versionId, versionData) => {
    linkVersionToSubchapter(chapterId, subchapterId, projectId, versionId, versionData);
  };

  const handleUnlinkVersion = async (chapterId, subchapterId) => {
    try {
      toast.loading('Scollegamento versione in corso...', { id: 'unlink-version' });

      await unlinkVersionFromSubchapter(chapterId, subchapterId);

      // Ricarica i dati per aggiornare l'UI
      console.log('🔄 Ricaricamento dati per aggiornare UI...');
      await Promise.all([
        loadProjects(), // Ricarica progetti per mostrare collegamenti
        loadChapters()  // Ricarica capitoli per mostrare collegamenti
      ]);

      toast.success('✅ Versione scollegata con successo!', { id: 'unlink-version' });
    } catch (error) {
      console.error('❌ Errore nello scollegamento:', error);
      toast.error(`❌ Errore: ${error.message}`, { id: 'unlink-version' });
    }
  };

  const handleLinkToChapter = (project, version) => {
    console.log('🔗 MainEditor: handleLinkToChapter chiamata con:', { project, version });
    setTransferSource({ project, version });
    setShowLinkToChapterModal(true);
    console.log('🔗 MainEditor: Modal di collegamento aperto');
  };

  const handleLinkToSubchapter = async (chapter, subchapter, projectId, versionId, versionData) => {
    try {
      toast.loading('Collegamento versione in corso...', { id: 'link-version' });

      await linkVersionToSubchapter(chapter.id, subchapter.id, projectId, versionId, versionData);

      // Ricarica i dati per aggiornare l'UI
      console.log('🔄 Ricaricamento dati per aggiornare UI...');
      await Promise.all([
        loadProjects(), // Ricarica progetti per mostrare collegamenti
        loadChapters()  // Ricarica capitoli per mostrare collegamenti
      ]);

      toast.success('✅ Versione collegata con successo!', { id: 'link-version' });
      setShowLinkToChapterModal(false);
    } catch (error) {
      console.error('❌ Errore nel collegamento:', error);
      toast.error(`❌ Errore: ${error.message}`, { id: 'link-version' });
    }
  };

  const handleTransferFromProjectToChapter = (transferInfo) => {
    const { sourceProject, sourceVersion, chapterId, subchapterId, newSubchapterTitle, versionMode } = transferInfo;

    if (subchapterId) {
      // Aggiorna sottocapitolo esistente
      const saveAsBackup = versionMode === 'backup';
      updateSubchapter(chapterId, subchapterId, {
        text: sourceVersion.text
      }, saveAsBackup);
    } else if (newSubchapterTitle) {
      // Crea nuovo sottocapitolo
      createSubchapter(chapterId, newSubchapterTitle, sourceVersion.text, transferInfo.newSubchapterDescription || '');
    }

    setActiveTab('chapters');
  };

  const handleCreateNewChapterFromProject = (chapterInfo) => {
    // Crea nuovo capitolo
    const newChapter = createChapter(chapterInfo.chapterTitle, chapterInfo.chapterDescription);

    if (newChapter) {
      // Crea sottocapitolo nel nuovo capitolo
      createSubchapter(
        newChapter.id,
        chapterInfo.subchapterTitle,
        chapterInfo.sourceVersion.text,
        chapterInfo.subchapterDescription
      );
    }

    setActiveTab('chapters');
  };

  const handleTransferFromChapterToProject = (transferInfo) => {
    const { projectId, versionName, sourceChapter, sourceSubchapter } = transferInfo;

    // Crea nuova versione nel progetto esistente
    const versionData = {
      name: versionName,
      text: sourceSubchapter.liveVersion?.text || '',
      wordCount: sourceSubchapter.liveVersion?.wordCount || 0,
      timestamp: new Date().toISOString(),
      analysis: sourceSubchapter.liveVersion?.analysis
    };

    // Qui dovresti chiamare una funzione per aggiungere la versione al progetto
    // Per ora simuliamo il successo
    console.log('Trasferimento a progetto:', { projectId, versionData });

    setActiveTab('projects');
  };

  const handleCreateNewProjectFromChapter = (projectInfo) => {
    const { projectTitle, projectDescription, versionName, sourceChapter, sourceSubchapter } = projectInfo;

    // Crea nuovo progetto con la prima versione
    const newProjectData = {
      title: projectTitle,
      description: projectDescription,
      text: sourceSubchapter.liveVersion?.text || '',
      wordCount: sourceSubchapter.liveVersion?.wordCount || 0,
      analysis: sourceSubchapter.liveVersion?.analysis
    };

    // Salva come nuovo progetto
    saveProjectHook(newProjectData.analysis || {}, newProjectData.text, newProjectData.title);

    setActiveTab('projects');
  };

  // Wrapper per saveProject che include l'analisi
  const saveProject = () => {
    saveProjectHook(text, analysis);
  };

  // Sincronizza testo con versione corrente quando cambia
  const currentVersion = getCurrentVersion();

  // Effetto per sincronizzare il testo e l'analisi quando cambia la versione
  useEffect(() => {
    // Non eseguire se siamo nel tab capitoli (i capitoli sono indipendenti dal progetto corrente)
    if (activeTab === 'chapters') return;

    if (currentVersion) {
      // Aggiorna il testo se diverso
      if (currentVersion.text !== text) {
        console.log('📝 Sincronizzazione testo da versione:', currentVersionId);
        setText(currentVersion.text);
      }

      // Carica l'analisi salvata se disponibile
      if (currentVersion.analysis) {
        console.log('📊 Caricamento analisi salvata per versione:', currentVersionId);
        setAnalysis(currentVersion.analysis);
      } else {
        // Se non c'è analisi salvata, resetta l'analisi corrente
        console.log('🔄 Nessuna analisi salvata per versione:', currentVersionId);
        setAnalysis(null);
      }
    }
  }, [currentVersionId, currentVersion, activeTab, setAnalysis]); // RIMOSSO text dalle dipendenze per evitare loop

  // Effetto per aggiungere automaticamente eventi estratti dall'analisi
  useEffect(() => {
    // Non eseguire se siamo nel tab capitoli (i capitoli sono indipendenti dal progetto corrente)
    if (activeTab === 'chapters') return;

    if (analysis?.plotEvents && analysis.plotEvents.length > 0) {
      // Aggiungi eventi estratti automaticamente se non esistono già
      analysis.plotEvents.forEach(event => {
        const exists = plotEvents.some(existing =>
          existing.description === event.description && existing.type === event.type
        );

        if (!exists) {
          addPlotEvent({
            ...event,
            autoExtracted: true,
            projectId: currentProject?.id || null
          });
        }
      });
    }
  }, [analysis?.plotEvents, plotEvents, addPlotEvent, currentProject?.id, activeTab]);

  // Nota: L'analisi ora viene salvata automaticamente nel server tramite saveProjectHook
  // Non c'è più salvataggio diretto in localStorage

  // RIMOSSO: Vecchia logica di selezione progetto post-analisi

  // Nuova logica: Gestione del flusso corretto
  // 1. Quando l'utente vuole analizzare, prima chiede il progetto
  const handleStartAnalysis = async () => {
    if (!text.trim()) {
      toast.error('Inserisci del testo da analizzare');
      return;
    }

    // Salva il testo in attesa
    const textToAnalyze = text.trim();
    setPendingText(textToAnalyze);

    // Se c'è un progetto pre-selezionato, usalo direttamente
    if (selectedProjectForAnalysis) {
      console.log('🎯 Progetto pre-selezionato:', selectedProjectForAnalysis.title);
      toast.success(`📁 Usando progetto pre-selezionato: ${selectedProjectForAnalysis.title}`);

      // Avvia l'analisi direttamente
      console.log('🚀 Avvio analisi per progetto pre-selezionato:', selectedProjectForAnalysis.title);
      toast.loading('Avvio analisi del testo...', { id: 'analyze-text' });
      await performAnalysisAndCreateVersion(selectedProjectForAnalysis, textToAnalyze);
    } else {
      // Altrimenti mostra il selettore di progetto
      setShowProjectSelector(true);
    }
  };

  // 2. Gestione selezione progetto PRE-analisi
  const handleProjectSelection = async (selectedProjectId) => {
    // Se non c'è pendingText, è un cambio progetto senza analisi
    const isChangingProject = !pendingText;

    try {
      if (selectedProjectId === 'new') {
        // Mostra il modal per inserire il nome del nuovo progetto
        setShowProjectSelector(false);
        setShowNewProjectModal(true);

        // Genera un nome suggerito basato sul testo (se presente)
        if (pendingText) {
          const firstWords = pendingText.split(/\s+/).slice(0, 5).join(' ');
          const suggestedName = firstWords.length > 30 ? firstWords.substring(0, 30) + '...' : firstWords;
          setNewProjectName(suggestedName || `Progetto ${new Date().toLocaleDateString()}`);
        } else {
          setNewProjectName(`Progetto ${new Date().toLocaleDateString()}`);
        }

      } else {
        // Prima aggiorna immediatamente il display del progetto attivo
        const selectedProject = projects.find(p => p.id === selectedProjectId);

        if (selectedProject) {
          setActiveProject(selectedProject);
          // Imposta la versione più recente come attiva
          if (selectedProject.versions && selectedProject.versions.length > 0) {
            const latestVersion = selectedProject.versions[0]; // La più recente è all'indice 0
            setActiveVersionId(latestVersion.id);
          }
          toast.success(`📂 Progetto "${selectedProject.title}" selezionato!`, { id: 'load-project' });
        }

        // Poi carica il progetto completo per l'analisi (se necessario)
        if (pendingText) {
          toast.loading('Caricamento progetto per analisi...', { id: 'load-project-analysis' });
          const targetProject = await loadProject(selectedProjectId);
          setSelectedProjectForAnalysis(targetProject);
          toast.success(`✅ Progetto pronto per l\'analisi!`, { id: 'load-project-analysis' });
          console.log('✅ Progetto selezionato, pronto per l\'analisi');
        } else {
          // Cambio progetto senza analisi - resetta il progetto selezionato per analisi
          setSelectedProjectForAnalysis(null);
          console.log('✅ Progetto cambiato:', selectedProject.title);
        }

        setShowProjectSelector(false);
      }

    } catch (error) {
      handleError(error, 'selezione del progetto');
    }
  };

  const handleCancelProjectSelection = () => {
    setShowProjectSelector(false);
    setPendingText('');
    setSelectedProjectForAnalysis(null);
  };

  // 2c. Gestione cambio progetto dal pulsante
  const handleChangeProject = () => {
    // Se c'è del testo, salvalo come pendingText per l'analisi
    if (text.trim()) {
      setPendingText(text.trim());
    }
    setShowProjectSelector(true);
  };

  // 2b. Gestione creazione nuovo progetto con nome personalizzato
  const handleCreateNewProject = async () => {
    if (!newProjectName.trim()) {
      toast.error('Inserisci un nome per il progetto');
      return;
    }

    try {
      // Crea il nuovo progetto con il nome inserito
      toast.loading('Creazione nuovo progetto...', { id: 'create-project' });
      const targetProject = await saveProjectHook(pendingText || '', null, newProjectName.trim());
      handleSuccess(`📝 Progetto "${targetProject.title}" creato con successo!`, 'Creazione progetto');

      // Aggiorna immediatamente il display del progetto attivo
      setActiveProject(targetProject);
      if (targetProject.versions && targetProject.versions.length > 0) {
        const latestVersion = targetProject.versions[0]; // La più recente è all'indice 0
        setActiveVersionId(latestVersion.id);
      }

      setShowNewProjectModal(false);
      setNewProjectName('');

      if (pendingText) {
        // Se c'è testo, salva il progetto per l'analisi e avvia l'analisi con creazione versione
        setSelectedProjectForAnalysis(targetProject);
        console.log('🚀 Avvio analisi per nuovo progetto:', targetProject?.title || targetProject?.id);
        toast.loading('Avvio analisi del testo...', { id: 'analyze-text' });
        await performAnalysisAndCreateVersion(targetProject);
        // Il reset del testo e selectedProjectForAnalysis viene fatto in performAnalysisAndCreateVersion
      } else {
        // Se non c'è testo, è solo un cambio progetto
        console.log('✅ Nuovo progetto creato:', targetProject.title);
        // Il selettore globale è già aggiornato da saveProjectHook()
      }

    } catch (error) {
      handleError(error, 'creazione del progetto');
    }
  };

  const handleCancelNewProject = () => {
    setShowNewProjectModal(false);
    setNewProjectName('');
    // Torna al selettore di progetto
    setShowProjectSelector(true);
  };

  // 3. Gestione completamento analisi - RIMOSSO per evitare doppia creazione versioni
  // Il flusso ora è gestito solo da performAnalysisAndCreateVersion

  // 4. Reset selezione sotto-capitolo quando cambia il capitolo
  useEffect(() => {
    // Quando cambia il capitolo selezionato, resetta la selezione del sotto-capitolo
    if (selectedChapter) {
      setSelectedSubchapter('');
    }
  }, [selectedChapter]);

  // 4b. Sincronizza activeProject con currentProject all'avvio
  useEffect(() => {
    if (currentProject && !activeProject) {
      setActiveProject(currentProject);
      if (currentVersionId) {
        setActiveVersionId(currentVersionId);
      }
    }
  }, [currentProject, currentVersionId, activeProject]);





  // 4b. Funzione per eseguire analisi e creare versione
  const performAnalysisAndCreateVersion = async (targetProject, textToAnalyze = null) => {
    const actualText = textToAnalyze || pendingText;
    try {
      // 1. Esegui l'analisi e ottieni i risultati
      console.log('🔬 Eseguendo analisi del testo...');
      console.log('📝 Testo da analizzare:', {
        actualTextLength: actualText?.length || 0,
        actualTextPreview: actualText?.substring(0, 100) || 'NESSUN TESTO',
        actualTextType: typeof actualText,
        actualTextValue: actualText
      });
      const analysisResult = await analyzeText(actualText, targetProject, null); // null perché la versione non esiste ancora
      toast.success('✅ Analisi completata!', { id: 'analyze-text' });

      // 2. Crea una nuova versione con i risultati dell'analisi
      console.log('📦 Creando nuova versione per progetto:', targetProject.title);
      console.log('📝 Dati per createVersion:', {
        projectId: targetProject.id,
        actualTextLength: actualText?.length || 0,
        actualTextPreview: actualText?.substring(0, 50) || 'NESSUN TESTO',
        actualTextType: typeof actualText,
        actualTextValue: actualText,
        hasAnalysisResult: !!analysisResult
      });

      toast.loading('Creazione nuova versione...', { id: 'create-version' });

      const newVersion = await createVersion(targetProject.id, {
        name: `Versione ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
        text: actualText,
        analysis: analysisResult
      });

      toast.success(`📦 Versione creata con successo!`, { id: 'create-version' });
      console.log('✅ Nuova versione creata:', newVersion.id);

      // 3. Aggiorna il progetto attivo con la nuova versione (ma mantieni la versione attiva precedente)
      if (activeProject && activeProject.id === targetProject.id) {
        const updatedProject = { ...activeProject };
        if (!updatedProject.versions) updatedProject.versions = [];
        // Aggiungi la nuova versione all'inizio (più recente)
        updatedProject.versions.unshift(newVersion);
        setActiveProject(updatedProject);
      }

      // 4. Salva i dati della nuova versione e chiedi se renderla live
      console.log('🔍 DEBUG newVersionData:', {
        pendingText: pendingText,
        pendingTextLength: pendingText.length,
        newVersionText: newVersion.text,
        newVersionTextLength: newVersion.text?.length || 0
      });

      setNewVersionData({
        project: targetProject,
        version: newVersion,
        text: newVersion.text || pendingText, // Usa il testo della versione se disponibile
        analysis: analysisResult
      });

      // 5. Imposta la versione appena creata come attiva
      setActiveVersionId(newVersion.id);

      // 6. Reset del testo in attesa
      setPendingText('');
      setSelectedProjectForAnalysis(null);

      // 7. Mostra il prompt per rendere la versione live
      setShowVersionPrompt(true);

      return newVersion;
    } catch (error) {
      console.error('❌ Errore durante analisi e creazione versione:', error);
      toast.error(`❌ Errore durante l'analisi:\n\n${error.message}`, { id: 'create-version' });
      throw error;
    }
  };



  // 5. Gestione prompt versione live
  const handleMakeVersionLive = async (makeLive) => {
    if (!newVersionData) return;

    try {
      if (makeLive) {
        // Mostra il selettore di capitolo per collegare la versione
        setShowVersionPrompt(false);
        setShowChapterSelector(true);
        console.log('📖 Mostrando selettore capitolo per collegare la versione');
        console.log('📖 newVersionData:', newVersionData);
        console.log('📖 chapters disponibili:', chapters?.length || 0);
      } else {
        console.log('📝 Versione salvata come backup');

        // Chiudi il prompt e pulisci lo stato
        setShowVersionPrompt(false);
        setNewVersionData(null);

        // Feedback all'utente
        alert('Versione salvata come backup!');
      }

    } catch (error) {
      console.error('Errore gestione versione:', error);
      alert('Errore durante la gestione della versione');
    }
  };

  const handleCancelVersionPrompt = () => {
    setShowVersionPrompt(false);
    setNewVersionData(null);
  };

  // 6. Gestione selettore capitolo per collegare la versione
  const handleChapterSelection = async (chapterOption, subchapterOption) => {
    if (!newVersionData) return;

    try {
      let targetChapterId = chapterOption;
      let targetSubchapterId = subchapterOption;
      let chapterTitle = '';
      let subchapterTitle = '';

      // Se è un nuovo capitolo, crealo
      if (chapterOption === 'new' && newChapterName.trim()) {
        console.log('📖 Creazione nuovo capitolo:', newChapterName.trim());
        toast.loading('Creazione capitolo...', { id: 'create-chapter' });
        const newChapter = await createChapter(newChapterName.trim(), '');
        targetChapterId = newChapter.id;
        chapterTitle = newChapter.title;
        toast.success(`📖 Capitolo "${chapterTitle}" creato con successo!`, { id: 'create-chapter' });
      } else if (chapterOption !== 'new') {
        // Trova il titolo del capitolo esistente
        const existingChapter = chapters.find(c => c.id === chapterOption);
        chapterTitle = existingChapter?.title || chapterOption;
      }

      // Se è un nuovo sotto-capitolo, crealo
      if (subchapterOption === 'new' && newSubchapterName.trim()) {
        console.log('📄 Creazione nuovo sotto-capitolo:', newSubchapterName.trim());
        toast.loading('Creazione sotto-capitolo...', { id: 'create-subchapter' });
        const newSubchapter = await createSubchapter(
          targetChapterId,
          newSubchapterName.trim(),
          newVersionData.text,
          ''
        );
        targetSubchapterId = newSubchapter.id;
        subchapterTitle = newSubchapter.title;
        toast.success(`📄 Sotto-capitolo "${subchapterTitle}" creato con successo!`, { id: 'create-subchapter' });
      } else if (subchapterOption === 'main') {
        // Crea un sotto-capitolo principale
        console.log('📄 Creazione sotto-capitolo principale');
        toast.loading('Creazione sotto-capitolo principale...', { id: 'create-subchapter' });
        const newSubchapter = await createSubchapter(
          targetChapterId,
          'Capitolo principale',
          newVersionData.text,
          'Contenuto principale del capitolo'
        );
        targetSubchapterId = newSubchapter.id;
        subchapterTitle = newSubchapter.title;
        toast.success(`📄 Sotto-capitolo principale creato con successo!`, { id: 'create-subchapter' });
      } else {
        // Trova il titolo del sotto-capitolo esistente
        const existingChapter = chapters.find(c => c.id === targetChapterId);
        const existingSubchapter = existingChapter?.subchapters?.find(s => s.id === subchapterOption);
        subchapterTitle = existingSubchapter?.title || subchapterOption;
      }

      // Collega la versione al sotto-capitolo
      console.log('🔗 Collegamento versione al sotto-capitolo:', {
        project: newVersionData.project.title,
        version: newVersionData.version.id,
        chapter: chapterTitle,
        subchapter: subchapterTitle
      });

      console.log('🔗 Tentativo collegamento versione:', {
        targetChapterId,
        targetSubchapterId,
        projectId: newVersionData.project.id,
        versionId: newVersionData.version.id
      });

      toast.loading('Collegamento versione al capitolo...', { id: 'link-version' });
      await linkVersionToSubchapter(
        targetChapterId,
        targetSubchapterId,
        newVersionData.project.id,
        newVersionData.version.id,
        {
          projectTitle: newVersionData.project.title,
          versionName: `Versione ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
          text: newVersionData.text,
          wordCount: newVersionData.text.split(/\s+/).filter(w => w.length > 0).length,
          analysis: newVersionData.analysis
        }
      );
      console.log('✅ Collegamento completato con successo');
      toast.success(`🔗 Versione collegata con successo al capitolo!`, { id: 'link-version' });

      // Ricarica i dati per aggiornare l'UI
      console.log('🔄 Ricaricamento dati per aggiornare UI...');
      await Promise.all([
        loadProjects(), // Ricarica progetti per mostrare collegamenti
        loadChapters()  // Ricarica capitoli per mostrare collegamenti
      ]);

      // Carica il progetto e imposta questa versione come corrente
      await loadProject(newVersionData.project.id, newVersionData.version.id);

      // Chiudi tutti i modal e pulisci lo stato
      setShowChapterSelector(false);
      setNewVersionData(null);
      setSelectedChapter('');
      setSelectedSubchapter('');
      setNewChapterName('');
      setNewSubchapterName('');

      // Feedback finale all'utente
      toast.success(`🎉 Operazione completata!\n\nCapitolo: "${chapterTitle}"\nSotto-capitolo: "${subchapterTitle}"\n\nLa versione è ora live e collegata.`, {
        duration: 5000,
        style: {
          maxWidth: '500px',
        }
      });

    } catch (error) {
      console.error('Errore collegamento capitolo:', error);
      toast.error(`❌ Errore durante il collegamento al capitolo:\n\n${error.message}`, {
        duration: 6000,
        style: {
          maxWidth: '500px',
        }
      });
    }
  };

  const handleCancelChapterSelection = () => {
    setShowChapterSelector(false);
    // Torna al prompt versione
    setShowVersionPrompt(true);
    // Reset selezioni
    setSelectedChapter('');
    setSelectedSubchapter('');
    setNewChapterName('');
    setNewSubchapterName('');
  };

  // API key dalle variabili d'ambiente
  const openRouterApiKey = process.env.REACT_APP_OPENROUTER_API_KEY || '';
  const siteName = process.env.REACT_APP_SITE_NAME || 'Editor Letterario Avanzato';

  // Nota: Il testo ora viene caricato solo dai progetti/capitoli del server
  // Non c'è più caricamento automatico da localStorage

  // Configurazione tab
  const tabs = [
    { id: 'input', label: 'Testo', icon: FileText },
    { id: 'chapters', label: 'Capitoli', icon: BookOpen },
    { id: 'projects', label: 'Progetti', icon: History },
    { id: 'plot-events', label: 'Eventi Trama', icon: Target },
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'highlighted', label: 'Evidenziato', icon: Eye },
    { id: 'classification', label: 'Classificazione Frasi', icon: PieChart },
    { id: 'characters', label: 'Analisi Personaggi', icon: Users },
    { id: 'suggestions', label: 'Suggerimenti', icon: MessageSquare },
    { id: 'narrative', label: 'Struttura Narrativa', icon: Film },
    { id: 'advanced-style', label: 'Stile Avanzato', icon: Brain },
    { id: 'semantic', label: 'Analisi Semantica', icon: BookOpen },
    { id: 'literary', label: 'Metriche Letterarie', icon: Zap },
    { id: 'structure', label: 'Struttura Avanzata', icon: Layers }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto px-2 py-2">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Header migliorato con stato sempre visibile */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-lg font-bold">{siteName}</h1>
                <p className="text-xs text-blue-100">Analisi letteraria avanzata con AI</p>
              </div>

              {/* Stato e controlli sempre visibili */}
              <div className="flex items-center gap-3">
                {/* Indicatore stato server */}
                <div className="text-xs">
                  <div className="text-blue-100">Server:</div>
                  <div className="font-medium flex items-center gap-1">
                    {isChecking ? (
                      <>
                        <div className="w-1.5 h-1.5 bg-yellow-300 rounded-full animate-pulse"></div>
                        <span>Connessione...</span>
                      </>
                    ) : isConnected ? (
                      <>
                        <div className="w-1.5 h-1.5 bg-green-300 rounded-full"></div>
                        <span>Locale</span>
                      </>
                    ) : (
                      <>
                        <div className="w-1.5 h-1.5 bg-red-300 rounded-full"></div>
                        <span>Offline</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Progetto attivo e controlli */}
                <div className="flex items-center gap-3">
                  {/* Display progetto attivo */}
                  <div className="text-xs">
                    <div className="text-blue-100">Progetto attivo:</div>
                    <div className="font-medium">
                      {activeProject ? (
                        <>
                          {activeProject.title}
                          {activeProject.versions && (
                            <span className="text-blue-200">
                              {pendingText ? (
                                // Modalità lavoro: mostra la prossima versione che verrà creata
                                ` - v${activeProject.versions.length + 1} (Nuova)`
                              ) : activeVersionId ? (
                                // Modalità navigazione: mostra la versione selezionata
                                ` - v${activeProject.versions.length - activeProject.versions.findIndex(v => v.id === activeVersionId)}`
                              ) : (
                                // Nessuna versione selezionata
                                ` - v${activeProject.versions.length + 1} (Nuova)`
                              )}
                            </span>
                          )}
                          <span className="text-blue-300">
                            {analysis ? ' (Analizzato)' : ' (Non analizzato)'}
                          </span>
                        </>
                      ) : (
                        <span className="text-blue-300">Nessun progetto selezionato</span>
                      )}
                    </div>

                  </div>

                  {/* Pulsante cambia progetto */}
                  <button
                    onClick={handleChangeProject}
                    className="bg-white/20 hover:bg-white/30 text-white border border-white/30 rounded px-3 py-1 text-xs transition-colors"
                  >
                    {activeProject ? 'Cambia progetto' : 'Seleziona progetto'}
                  </button>

                  {/* Selettore versione (solo se c'è un progetto) */}
                  {activeProject && activeProject.versions && activeProject.versions.length > 1 && (
                    <select
                      value={activeVersionId || ''}
                      onChange={(e) => {
                        if (e.target.value) {
                          setActiveVersionId(e.target.value);
                          // Carica anche il progetto con la versione selezionata per l'analisi
                          loadProject(activeProject, e.target.value);
                        }
                      }}
                      className="bg-white/20 text-white border border-white/30 rounded px-2 py-1 text-xs min-w-[100px]"
                    >
                      <option value="" className="text-gray-800">Seleziona versione</option>
                      {activeProject.versions.map((version, index) => (
                        <option key={version.id} value={version.id} className="text-gray-800">
                          v{activeProject.versions.length - index} - {new Date(version.timestamp).toLocaleDateString()} {new Date(version.timestamp).toLocaleTimeString()}
                        </option>
                      ))}
                    </select>
                  )}
                </div>

                {/* Pulsanti azione */}
                <div className="flex items-center gap-1">
                  {/* Pulsante Salva Versione */}
                  <button
                    onClick={(e) => {
                      if (text.trim() && analysis) {
                        saveProjectHook(text, analysis);
                        // Feedback visivo
                        const button = e.target.closest('button');
                        const originalText = button.innerHTML;
                        button.innerHTML = '<span class="text-xs">✓ Salvato!</span>';
                        setTimeout(() => {
                          button.innerHTML = originalText;
                        }, 2000);
                      } else {
                        alert('Devi avere del testo e un\'analisi per salvare una versione');
                      }
                    }}
                    disabled={!text.trim() || !analysis}
                    className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-xs flex items-center gap-1 transition-colors"
                    title="Salva versione corrente"
                  >
                    <Save className="w-3 h-3" />
                    Salva
                  </button>

                  {/* Pulsante Nuova Versione */}
                  <button
                    onClick={() => {
                      if (currentProject && text.trim()) {
                        // Crea una nuova versione senza analisi
                        setAnalysis(null);
                        // Il salvataggio avverrà quando l'utente farà una nuova analisi
                      }
                    }}
                    disabled={!currentProject || !text.trim()}
                    className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-xs flex items-center gap-1 transition-colors"
                    title="Crea nuova versione"
                  >
                    <Plus className="w-3 h-3" />
                    Nuova
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Navigazione tab compatta */}
          <div className="border-b border-gray-200 bg-gray-50">
            <nav className="flex overflow-x-auto scrollbar-hide">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-1 px-2 py-2 text-xs font-medium border-b-2 transition-colors whitespace-nowrap ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 bg-white'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-3 h-3" />
                    <span className="hidden sm:inline">{tab.label}</span>
                    <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="p-3 max-h-screen overflow-y-auto">
            {activeTab === 'input' && (
              <InputTab
                text={text}
                setText={setText}
                onAnalyze={handleStartAnalysis}
                onSave={saveProject}
                isAnalyzing={isAnalyzing}
                openRouterApiKey={openRouterApiKey}
                currentProject={currentProject}
                currentVersionId={currentVersionId}
                projectName={projectName}
                setProjectName={setProjectName}
                // Props per gestione capitoli
                chapters={chapters}
                projects={projects}
                onCreateChapter={createChapter}
                onCreateSubchapter={createSubchapter}
                onUpdateSubchapter={updateSubchapter}
                onLoadSubchapter={loadSubchapter}
                onSwitchToChaptersTab={() => setActiveTab('chapters')}
                // Props per selezione progetto
                selectedProjectForAnalysis={selectedProjectForAnalysis}
                setSelectedProjectForAnalysis={setSelectedProjectForAnalysis}
                // Props per display progetto attivo
                activeProject={activeProject}
                setActiveProject={setActiveProject}
                setActiveVersionId={setActiveVersionId}
              />
            )}

            {activeTab === 'chapters' && (
              currentSubchapter ? (
                <SubchapterEditor
                  chapter={currentChapter}
                  subchapter={currentSubchapter}
                  onUpdateSubchapter={updateSubchapter}
                  onAnalyzeText={analyzeText}
                  onBackToChapters={() => loadChapter(currentChapter.id)}
                  isAnalyzing={isAnalyzing}
                />
              ) : (
                <ChapterManager
                  chapters={chapters}
                  currentChapter={currentChapter}
                  currentSubchapter={currentSubchapter}
                  onCreateChapter={createChapter}
                  onCreateSubchapter={createSubchapter}
                  onLoadChapter={loadChapter}
                  onLoadSubchapter={loadSubchapter}
                  onDeleteChapter={deleteChapter}
                  onDeleteSubchapter={deleteSubchapter}
                  onUpdateSubchapter={updateSubchapter}
                  getChapterStats={getChapterStats}
                  onTransferToProject={handleTransferToProject}
                  onSelectVersionForSubchapter={handleSelectVersionForSubchapter}
                />
              )
            )}

            {activeTab === 'projects' && (
              <ProjectsTab
                projects={projects}
                currentProject={currentProject}
                chapters={chapters}
                loadProject={loadProject}
                deleteProject={deleteProject}
                exportProject={exportProject}
                importProject={importProject}
                onLinkToChapter={handleLinkToChapter}
              />
            )}

            {activeTab === 'plot-events' && (
              <PlotEventsTab
                plotEvents={plotEvents}
                currentProject={currentProject}
                showEventForm={showEventForm}
                setShowEventForm={setShowEventForm}
                editingEvent={editingEvent}
                addPlotEvent={addPlotEvent}
                deletePlotEvent={deletePlotEvent}
                resolveMysterySeed={resolveMysterySeed}
                startEditingEvent={startEditingEvent}
                cancelEditing={cancelEditing}
              />
            )}

            {activeTab === 'dashboard' && analysis && (
              <DashboardTab analysis={analysis} />
            )}

            {activeTab === 'highlighted' && analysis && (
              <HighlightedTab 
                text={text}
                analysis={analysis}
                highlightText={highlightText}
              />
            )}

            {activeTab === 'classification' && analysis && (
              <ClassificationTab 
                text={text}
                classifySentence={classifySentence}
              />
            )}

            {activeTab === 'characters' && analysis && (
              <CharactersTab
                analysis={analysis}
                currentProject={currentProject}
                currentVersion={getCurrentVersion()}
              />
            )}

            {activeTab === 'suggestions' && analysis && (
              <SuggestionsTab analysis={analysis} />
            )}

            {activeTab === 'narrative' && analysis && (
              <NarrativeTab text={text} />
            )}

            {activeTab === 'advanced-style' && analysis && (
              <AdvancedStyleTab text={text} analysis={analysis} />
            )}

            {activeTab === 'semantic' && analysis && (
              <SemanticTab text={text} analysis={analysis} />
            )}

            {activeTab === 'literary' && analysis && (
              <LiteraryTab text={text} analysis={analysis} />
            )}

            {activeTab === 'structure' && analysis && (
              <StructureTab text={text} analysis={analysis} />
            )}

            {/* Messaggio se non c'è analisi */}
            {!analysis && activeTab !== 'input' && activeTab !== 'projects' && activeTab !== 'plot-events' && (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Inserisci del testo nella scheda "Testo" e avvia l'analisi per vedere i risultati qui.</p>
                {(activeTab === 'advanced-style' || activeTab === 'semantic' || activeTab === 'literary' || activeTab === 'structure') && (
                  <p className="text-sm text-gray-500 mt-2">
                    ✨ Strumenti di analisi avanzata per scrittori professionali
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal Trasferimento a Capitolo */}
      <TransferToChapterModal
        isOpen={showTransferToChapterModal}
        onClose={() => setShowTransferToChapterModal(false)}
        sourceProject={transferSource?.project}
        sourceVersion={transferSource?.version}
        chapters={chapters}
        onTransfer={handleTransferFromProjectToChapter}
        onCreateNewChapter={handleCreateNewChapterFromProject}
      />

      {/* Modal Trasferimento a Progetto */}
      <TransferToProjectModal
        isOpen={showTransferToProjectModal}
        onClose={() => setShowTransferToProjectModal(false)}
        sourceChapter={transferSource?.chapter}
        sourceSubchapter={transferSource?.subchapter}
        projects={projects}
        onTransfer={handleTransferFromChapterToProject}
        onCreateNewProject={handleCreateNewProjectFromChapter}
      />

      {/* Modal Selezione Versione per Sottocapitolo */}
      <VersionLinkModal
        isOpen={showVersionLinkModal}
        onClose={() => setShowVersionLinkModal(false)}
        chapter={transferSource?.chapter}
        subchapter={transferSource?.subchapter}
        projects={projects}
        onLinkVersion={handleLinkVersion}
        onUnlinkVersion={handleUnlinkVersion}
      />

      {/* Modal Collegamento da Progetto a Capitolo */}
      <LinkToChapterModal
        isOpen={showLinkToChapterModal}
        onClose={() => setShowLinkToChapterModal(false)}
        sourceProject={transferSource?.project}
        sourceVersion={transferSource?.version}
        chapters={chapters}
        onLinkToSubchapter={handleLinkToSubchapter}
      />

      {/* Modal per la selezione del progetto PRE-analisi */}
      {showProjectSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Su quale progetto vuoi lavorare?</h3>
            <p className="text-gray-600 mb-6">
              Prima di analizzare il testo, scegli se creare un nuovo progetto o lavorare su un progetto esistente.
            </p>

            <div className="space-y-3 mb-6">
              {/* Opzione nuovo progetto */}
              <button
                onClick={() => handleProjectSelection('new')}
                className="w-full p-3 text-left border border-gray-300 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors"
              >
                <div className="font-medium text-blue-600">📝 Crea nuovo progetto</div>
                <div className="text-sm text-gray-500">Inizia un nuovo progetto con questa analisi</div>
              </button>

              {/* Lista progetti esistenti */}
              {projects && projects.length > 0 && (
                <div>
                  <div className="text-sm font-medium text-gray-700 mb-2">O aggiungi a un progetto esistente:</div>
                  <div className="max-h-40 overflow-y-auto space-y-2">
                    {projects.map((project) => (
                      <button
                        key={project.id}
                        onClick={() => handleProjectSelection(project.id)}
                        className="w-full p-3 text-left border border-gray-300 rounded-lg hover:bg-green-50 hover:border-green-300 transition-colors"
                      >
                        <div className="font-medium text-green-600">{project.title}</div>
                        <div className="text-sm text-gray-500">
                          {project.versions?.length || 0} versioni • Ultimo aggiornamento: {new Date(project.updated_at).toLocaleDateString()} {new Date(project.updated_at).toLocaleTimeString()}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleCancelProjectSelection}
                className="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Annulla
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal per inserire il nome del nuovo progetto */}
      {showNewProjectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">📝 Crea nuovo progetto</h3>
            <p className="text-gray-600 mb-4">
              Inserisci un nome per il nuovo progetto:
            </p>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome progetto
              </label>
              <input
                type="text"
                value={newProjectName}
                onChange={(e) => setNewProjectName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Inserisci il nome del progetto..."
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleCreateNewProject();
                  }
                }}
              />
              <div className="text-xs text-gray-500 mt-1">
                Suggerimento basato sul contenuto del testo
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleCreateNewProject}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                disabled={!newProjectName.trim()}
              >
                ✅ Crea progetto
              </button>
              <button
                onClick={handleCancelNewProject}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                ← Indietro
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal per selezionare capitolo e sotto-capitolo */}
      {showChapterSelector && newVersionData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">📖 Collega al capitolo</h3>
            <p className="text-gray-600 mb-4">
              Scegli a quale capitolo e sotto-capitolo collegare questa versione del progetto <strong>"{newVersionData.project.title}"</strong>.
            </p>

            {/* Selezione Capitolo */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Capitolo
              </label>
              <div className="space-y-2">
                {/* Opzione nuovo capitolo */}
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="new-chapter"
                    name="chapter"
                    value="new"
                    checked={selectedChapter === 'new'}
                    onChange={(e) => setSelectedChapter(e.target.value)}
                    className="mr-2"
                  />
                  <label htmlFor="new-chapter" className="text-sm">Crea nuovo capitolo</label>
                </div>

                {selectedChapter === 'new' && (
                  <input
                    type="text"
                    value={newChapterName}
                    onChange={(e) => setNewChapterName(e.target.value)}
                    className="ml-6 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Nome del nuovo capitolo..."
                  />
                )}

                {/* Lista capitoli esistenti */}
                {chapters && chapters.length > 0 && (
                  <div>
                    <div className="text-xs text-gray-500 mb-1">O scegli un capitolo esistente:</div>
                    {chapters.map((chapter) => (
                      <div key={chapter.id} className="flex items-center">
                        <input
                          type="radio"
                          id={`chapter-${chapter.id}`}
                          name="chapter"
                          value={chapter.id}
                          checked={selectedChapter === chapter.id}
                          onChange={(e) => setSelectedChapter(e.target.value)}
                          className="mr-2"
                        />
                        <label htmlFor={`chapter-${chapter.id}`} className="text-sm">{chapter.title}</label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Selezione Sotto-capitolo */}
            {selectedChapter && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sotto-capitolo
                </label>
                <div className="space-y-2">
                  {/* Opzione nuovo sotto-capitolo */}
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="new-subchapter"
                      name="subchapter"
                      value="new"
                      checked={selectedSubchapter === 'new'}
                      onChange={(e) => setSelectedSubchapter(e.target.value)}
                      className="mr-2"
                    />
                    <label htmlFor="new-subchapter" className="text-sm">Crea nuovo sotto-capitolo</label>
                  </div>

                  {selectedSubchapter === 'new' && (
                    <input
                      type="text"
                      value={newSubchapterName}
                      onChange={(e) => setNewSubchapterName(e.target.value)}
                      className="ml-6 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Nome del nuovo sotto-capitolo..."
                    />
                  )}

                  {/* Lista sotto-capitoli esistenti per il capitolo selezionato */}
                  {selectedChapter !== 'new' && (
                    <div>
                      <div className="text-xs text-gray-500 mb-1">O scegli un sotto-capitolo esistente:</div>

                      {/* Opzione capitolo principale */}
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="subchapter-main"
                          name="subchapter"
                          value="main"
                          checked={selectedSubchapter === 'main'}
                          onChange={(e) => setSelectedSubchapter(e.target.value)}
                          className="mr-2"
                        />
                        <label htmlFor="subchapter-main" className="text-sm">Capitolo principale</label>
                      </div>

                      {/* Sotto-capitoli esistenti del capitolo selezionato */}
                      {(() => {
                        const selectedChapterData = chapters.find(c => c.id === selectedChapter);
                        const existingSubchapters = selectedChapterData?.subchapters || [];

                        return existingSubchapters.map(subchapter => (
                          <div key={subchapter.id} className="flex items-center">
                            <input
                              type="radio"
                              id={`subchapter-${subchapter.id}`}
                              name="subchapter"
                              value={subchapter.id}
                              checked={selectedSubchapter === subchapter.id}
                              onChange={(e) => setSelectedSubchapter(e.target.value)}
                              className="mr-2"
                            />
                            <label htmlFor={`subchapter-${subchapter.id}`} className="text-sm">
                              📄 {subchapter.title}
                            </label>
                          </div>
                        ));
                      })()}
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="flex gap-3">
              <button
                onClick={() => handleChapterSelection(selectedChapter, selectedSubchapter)}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                disabled={!selectedChapter || !selectedSubchapter ||
                  (selectedChapter === 'new' && !newChapterName.trim()) ||
                  (selectedSubchapter === 'new' && !newSubchapterName.trim())}
              >
                🔗 Collega e rendi live
              </button>
              <button
                onClick={handleCancelChapterSelection}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                ← Indietro
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal per il prompt versione live */}
      {showVersionPrompt && newVersionData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">✅ Analisi completata!</h3>
            <p className="text-gray-600 mb-4">
              È stata creata una nuova versione del progetto <strong>"{newVersionData.project.title}"</strong> con la tua analisi.
            </p>
            <p className="text-gray-600 mb-6">
              Vuoi utilizzare questa versione come versione live (corrente) del progetto?
            </p>

            <div className="space-y-3 mb-6">
              <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                <div className="font-medium text-blue-800">📊 Dettagli versione:</div>
                <div className="text-sm text-blue-700 mt-1">
                  • Parole: {newVersionData.text.split(/\s+/).length}
                </div>
                <div className="text-sm text-blue-700">
                  • Analisi: {newVersionData.analysis ? 'Completata' : 'Non disponibile'}
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => handleMakeVersionLive(true)}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                📖 Collega a capitolo
              </button>
              <button
                onClick={() => handleMakeVersionLive(false)}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                📝 No, salva come backup
              </button>
              <button
                onClick={handleCancelVersionPrompt}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Annulla
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MainEditor;
