import { MessageSquare, Lightbulb } from 'lucide-react';

const SuggestionsTab = ({ analysis }) => {
  const suggestions = analysis.issues?.suggestions || [];

  const getSuggestionIcon = (type) => {
    switch (type) {
      case 'readability': return '📖';
      case 'variety': return '🎨';
      case 'structure': return '🏗️';
      case 'grammar': return '✏️';
      case 'style': return '✨';
      default: return '💡';
    }
  };

  const getSuggestionColor = (priority) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      case 'low': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getPriorityLabel = (priority) => {
    switch (priority) {
      case 'high': return { label: 'Alta Priorità', color: 'text-red-700 bg-red-100' };
      case 'medium': return { label: 'Media Priorità', color: 'text-yellow-700 bg-yellow-100' };
      case 'low': return { label: 'Bassa Priorità', color: 'text-blue-700 bg-blue-100' };
      default: return { label: 'Normale', color: 'text-gray-700 bg-gray-100' };
    }
  };

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200">
        <h3 className="text-xl font-bold text-green-800 mb-2 flex items-center gap-2">
          <MessageSquare className="w-6 h-6" />
          Suggerimenti per il Miglioramento
        </h3>
        <p className="text-green-700 mb-4 text-sm">
          Consigli personalizzati basati sull'analisi del tuo testo per migliorare stile e leggibilità
        </p>

        {suggestions.length === 0 ? (
          <div className="text-center py-8">
            <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Nessun suggerimento disponibile</p>
            <p className="text-sm text-gray-500 mt-2">Analizza un testo per ricevere consigli personalizzati</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Statistiche suggerimenti */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-6">
              <div className="bg-white p-3 rounded-lg border border-red-100 text-center">
                <div className="text-2xl font-bold text-red-600">
                  {suggestions.filter(s => s.priority === 'high').length}
                </div>
                <div className="text-xs text-gray-600">Alta Priorità</div>
              </div>
              <div className="bg-white p-3 rounded-lg border border-yellow-100 text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {suggestions.filter(s => s.priority === 'medium').length}
                </div>
                <div className="text-xs text-gray-600">Media Priorità</div>
              </div>
              <div className="bg-white p-3 rounded-lg border border-blue-100 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {suggestions.filter(s => s.priority === 'low').length}
                </div>
                <div className="text-xs text-gray-600">Bassa Priorità</div>
              </div>
            </div>

            {/* Lista suggerimenti */}
            <div className="space-y-3">
              {suggestions.map((suggestion, index) => {
                const priorityInfo = getPriorityLabel(suggestion.priority);
                return (
                  <div key={index} className={`p-4 rounded-lg border ${getSuggestionColor(suggestion.priority)}`}>
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 text-2xl">
                        {getSuggestionIcon(suggestion.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold text-gray-800">{suggestion.title}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${priorityInfo.color}`}>
                            {priorityInfo.label}
                          </span>
                        </div>
                        <p className="text-gray-700 mb-3">{suggestion.description}</p>
                        
                        {suggestion.examples && suggestion.examples.length > 0 && (
                          <div className="bg-white p-3 rounded border border-gray-200">
                            <h5 className="font-medium text-gray-800 mb-2">📝 Esempi:</h5>
                            <ul className="space-y-1">
                              {suggestion.examples.map((example, exIndex) => (
                                <li key={exIndex} className="text-sm text-gray-600 flex items-start gap-2">
                                  <span className="text-gray-400">•</span>
                                  <span>{example}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {suggestion.action && (
                          <div className="mt-3 p-2 bg-white rounded border border-gray-200">
                            <p className="text-sm font-medium text-gray-800">
                              💡 <strong>Azione consigliata:</strong> {suggestion.action}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Suggerimenti generali */}
            <div className="bg-white p-4 rounded-lg border border-green-100">
              <h4 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
                <Lightbulb className="w-5 h-5" />
                Consigli Generali per la Scrittura
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <span className="text-green-600">✓</span>
                    <span>Varia la lunghezza delle frasi per creare ritmo</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-green-600">✓</span>
                    <span>Bilancia dialoghi, azioni e descrizioni</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-green-600">✓</span>
                    <span>Usa verbi attivi invece di passivi quando possibile</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <span className="text-green-600">✓</span>
                    <span>Evita ripetizioni eccessive di parole</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-green-600">✓</span>
                    <span>Mantieni paragrafi di lunghezza ragionevole</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-green-600">✓</span>
                    <span>Usa punteggiatura varia per migliorare il flusso</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SuggestionsTab;
