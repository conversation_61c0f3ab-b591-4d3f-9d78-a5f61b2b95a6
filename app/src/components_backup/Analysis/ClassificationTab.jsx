import { PieChart } from 'lucide-react';

const ClassificationTab = ({ text, classifySentence }) => {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const classifications = sentences.map(sentence => ({
    text: sentence.trim(),
    type: classifySentence(sentence.trim())
  }));

  const stats = classifications.reduce((acc, item) => {
    acc[item.type] = (acc[item.type] || 0) + 1;
    return acc;
  }, {});

  const total = classifications.length;

  const getTypeColor = (type) => {
    switch (type) {
      case 'dialogue': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'action': return 'bg-red-100 text-red-800 border-red-200';
      case 'descriptive': return 'bg-green-100 text-green-800 border-green-200';
      case 'narrative': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'dialogue': return '💬';
      case 'action': return '🏃';
      case 'descriptive': return '🎨';
      case 'narrative': return '🧠';
      default: return '📝';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'dialogue': return 'Dialogo';
      case 'action': return 'Azione';
      case 'descriptive': return 'Descrittivo';
      case 'narrative': return 'Narrativo';
      default: return 'Altro';
    }
  };

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg border border-indigo-200">
        <h3 className="text-xl font-bold text-indigo-800 mb-2 flex items-center gap-2">
          <PieChart className="w-6 h-6" />
          Classificazione Frasi
        </h3>
        <p className="text-indigo-700 mb-4 text-sm">
          Analisi automatica del tipo di contenuto per ogni frase del testo
        </p>

        {/* Statistiche */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
          {Object.entries(stats).map(([type, count]) => (
            <div key={type} className={`p-3 rounded-lg border ${getTypeColor(type)}`}>
              <div className="text-center">
                <div className="text-2xl mb-1">{getTypeIcon(type)}</div>
                <div className="text-lg font-bold">{count}</div>
                <div className="text-xs">{getTypeLabel(type)}</div>
                <div className="text-xs opacity-75">
                  {Math.round((count / total) * 100)}%
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Grafico a barre */}
        <div className="bg-white p-4 rounded-lg border border-indigo-100">
          <h4 className="font-semibold text-indigo-800 mb-3">📊 Distribuzione Tipologie</h4>
          <div className="space-y-3">
            {Object.entries(stats).map(([type, count]) => {
              const percentage = (count / total) * 100;
              return (
                <div key={type} className="flex items-center gap-3">
                  <div className="w-20 text-sm font-medium flex items-center gap-1">
                    <span>{getTypeIcon(type)}</span>
                    <span>{getTypeLabel(type)}</span>
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                    <div
                      className={`h-4 rounded-full ${
                        type === 'dialogue' ? 'bg-blue-500' :
                        type === 'action' ? 'bg-red-500' :
                        type === 'descriptive' ? 'bg-green-500' :
                        type === 'narrative' ? 'bg-purple-500' :
                        'bg-gray-500'
                      }`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                    <span className="absolute inset-0 flex items-center justify-center text-xs font-medium text-gray-700">
                      {count} ({Math.round(percentage)}%)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Lista frasi classificate */}
      <div className="bg-white rounded-lg border border-indigo-100">
        <div className="p-4 border-b border-gray-200">
          <h4 className="font-semibold text-indigo-800">📝 Frasi Classificate ({total})</h4>
          <p className="text-sm text-indigo-600 mt-1">
            Ogni frase è automaticamente categorizzata in base al contenuto
          </p>
        </div>
        
        <div className="max-h-96 overflow-y-auto">
          {classifications.map((item, index) => (
            <div key={index} className="p-3 border-b border-gray-100 last:border-b-0">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)}`}>
                    {getTypeIcon(item.type)} {getTypeLabel(item.type)}
                  </span>
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-800 leading-relaxed">
                    {item.text}
                  </p>
                </div>
                <div className="flex-shrink-0 text-xs text-gray-500">
                  #{index + 1}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Legenda */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h5 className="font-semibold text-gray-800 mb-2">📋 Legenda Classificazione</h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">💬 Dialogo</span>
              <span className="text-gray-700">Conversazioni e discorsi diretti</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">🏃 Azione</span>
              <span className="text-gray-700">Movimenti, gesti e azioni fisiche</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">🎨 Descrittivo</span>
              <span className="text-gray-700">Descrizioni di luoghi, oggetti, aspetto</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">🧠 Narrativo</span>
              <span className="text-gray-700">Pensieri, sentimenti, riflessioni</span>
            </div>
          </div>
        </div>
        <p className="text-xs text-gray-600 mt-3">
          💡 <strong>Classificazione automatica:</strong> Ogni frase viene analizzata per determinare il tipo di contenuto predominante.
          Utile per bilanciare dialoghi, azioni e descrizioni nella narrazione.
        </p>
      </div>
    </div>
  );
};

export default ClassificationTab;
