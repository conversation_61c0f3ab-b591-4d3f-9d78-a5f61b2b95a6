import { Target, BarChart3 } from 'lucide-react';
import { useScoreUtils } from '../../hooks/useScoreUtils';

const DashboardTab = ({ analysis }) => {
  const { getScoreColor, getScoreBarColor } = useScoreUtils();

  return (
    <div className="space-y-4">
      {/* Statistiche generali compatte */}
      <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-7 gap-2">
        <div className="bg-blue-50 p-2 rounded text-center">
          <div className="text-lg font-bold text-blue-600">{analysis.stats.words}</div>
          <div className="text-xs text-gray-600">Parole</div>
        </div>
        <div className="bg-green-50 p-2 rounded text-center">
          <div className="text-lg font-bold text-green-600">{analysis.stats.sentences}</div>
          <div className="text-xs text-gray-600">Frasi</div>
        </div>
        <div className="bg-purple-50 p-2 rounded text-center">
          <div className="text-lg font-bold text-purple-600">{analysis.stats.paragraphs}</div>
          <div className="text-xs text-gray-600">Paragrafi</div>
        </div>
        <div className="bg-cyan-50 p-2 rounded text-center">
          <div className="text-lg font-bold text-cyan-600">{analysis.stats.scenes}</div>
          <div className="text-xs text-gray-600">Scene</div>
        </div>
        <div className="bg-indigo-50 p-2 rounded text-center">
          <div className="text-lg font-bold text-indigo-600">{analysis.stats.dialogues}</div>
          <div className="text-xs text-gray-600">Dialoghi</div>
        </div>
        <div className="bg-orange-50 p-2 rounded text-center">
          <div className="text-lg font-bold text-orange-600">{analysis.stats.avgWordsPerSentence}</div>
          <div className="text-xs text-gray-600">Parole/Frase</div>
        </div>
        <div className="bg-pink-50 p-2 rounded text-center">
          <div className="text-lg font-bold text-pink-600">{analysis.stats.readingTime}</div>
          <div className="text-xs text-gray-600">Min lettura</div>
        </div>
      </div>

      {/* Punteggio generale prominente */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Punteggio Generale</h2>
          <div className={`text-6xl font-bold mb-4 ${getScoreColor(analysis.scores.overall)}`}>
            {analysis.scores.overall}/100
          </div>
          <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
            <div
              className={`h-4 rounded-full ${getScoreBarColor(analysis.scores.overall)}`}
              style={{width: `${analysis.scores.overall}%`}}
            ></div>
          </div>
          <p className="text-gray-600">
            {analysis.scores.overall >= 80 ? 'Eccellente! Il tuo testo è ben strutturato e scorrevole.' :
             analysis.scores.overall >= 60 ? 'Buono! Ci sono alcuni aspetti da migliorare.' :
             'Ci sono diverse aree che potrebbero beneficiare di revisioni.'}
          </p>
        </div>
      </div>

      {/* Dettaglio punteggi */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          Analisi Dettagliata
        </h3>
        <div className="grid grid-cols-1 gap-4">
          {Object.entries(analysis.scores).filter(([key]) => key !== 'overall').map(([key, score]) => {
            const labels = {
              'readability': 'Leggibilità',
              'variety': 'Varietà Lessicale',
              'structure': 'Struttura',
              'grammar': 'Grammatica'
            };
            const explanation = analysis.explanations[key];

            return (
              <div key={key} className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">{labels[key]}</span>
                  <span className={`text-xl font-bold ${getScoreColor(score)}`}>{score}/100</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div
                    className={`h-2 rounded-full ${getScoreBarColor(score)}`}
                    style={{width: `${score}%`}}
                  ></div>
                </div>
                <p className="text-sm text-gray-600">{explanation}</p>
              </div>
            );
          })}
        </div>
      </div>

      {/* Sezione Eventi Estratti */}
      {analysis.plotEvents && analysis.plotEvents.length > 0 && (
        <div className="bg-gradient-to-r from-cyan-50 to-teal-50 p-4 rounded-lg border border-cyan-200">
          <h3 className="text-lg font-bold text-cyan-800 mb-3 flex items-center gap-2">
            <Target className="w-5 h-5" />
            Eventi di Trama Estratti Automaticamente
          </h3>
          <p className="text-cyan-700 mb-4 text-sm">
            {analysis.plotEvents.length} eventi identificati nel testo.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {analysis.plotEvents.slice(0, 6).map((event, index) => (
              <div key={index} className={`bg-white p-3 rounded-lg border-l-4 ${
                event.type === 'mystery' ? 'border-red-400' :
                event.type === 'discovery' ? 'border-blue-400' :
                event.type === 'conflict' ? 'border-orange-400' :
                'border-purple-400'
              }`}>
                <div className="flex justify-between items-start mb-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    event.type === 'mystery' ? 'bg-red-100 text-red-700' :
                    event.type === 'discovery' ? 'bg-blue-100 text-blue-700' :
                    event.type === 'conflict' ? 'bg-orange-100 text-orange-700' :
                    'bg-purple-100 text-purple-700'
                  }`}>
                    {event.type === 'mystery' ? '🔍 Mistero' :
                     event.type === 'discovery' ? '💡 Scoperta' :
                     event.type === 'conflict' ? '⚔️ Conflitto' :
                     '👤 Personaggio'}
                  </span>
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-gray-500">Importanza:</span>
                    <div className={`w-12 bg-gray-200 rounded-full h-1.5`}>
                      <div 
                        className={`h-1.5 rounded-full ${
                          event.importance >= 8 ? 'bg-red-500' :
                          event.importance >= 6 ? 'bg-orange-500' :
                          event.importance >= 4 ? 'bg-yellow-500' :
                          'bg-gray-500'
                        }`}
                        style={{ width: `${(event.importance / 10) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-xs font-medium">{event.importance}/10</span>
                  </div>
                </div>
                
                <h4 className="font-semibold text-gray-800 text-sm mb-1">{event.title}</h4>
                <p className="text-xs text-gray-600 mb-2">{event.description}</p>
                
                {event.characters && event.characters.length > 0 && (
                  <div className="text-xs">
                    <span className="font-medium text-gray-500">👥 </span>
                    <span className="text-gray-700">{event.characters.join(', ')}</span>
                  </div>
                )}
                
                {event.location && (
                  <div className="text-xs mt-1">
                    <span className="font-medium text-gray-500">📍 </span>
                    <span className="text-gray-700">{event.location}</span>
                  </div>
                )}
                
                {event.confidence && (
                  <div className="text-xs mt-1 text-gray-500">
                    Confidenza: {event.confidence}%
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {analysis.plotEvents.length > 6 && (
            <div className="mt-3 text-center">
              <p className="text-sm text-cyan-600">
                +{analysis.plotEvents.length - 6} altri eventi. Vai alla tab "Eventi Trama" per vederli tutti.
              </p>
            </div>
          )}
          
          <div className="mt-4 p-3 bg-white rounded border border-cyan-100">
            <p className="text-xs text-gray-600">
              <strong>🤖 Estrazione Automatica:</strong> Gli eventi sono identificati usando pattern linguistici avanzati. 
              La confidenza indica l'accuratezza del rilevamento. Puoi modificarli nella tab "Eventi Trama".
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardTab;
