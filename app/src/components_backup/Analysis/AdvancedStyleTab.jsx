import { Brain } from 'lucide-react';
import { useScoreUtils } from '../../hooks/useScoreUtils';

const AdvancedStyleTab = ({ text, analysis }) => {
  const { getScoreColor, getScoreBarColor } = useScoreUtils();
  // Usa l'analisi già calcolata se disponibile, altrimenti calcola
  const getAdvancedStyleData = () => {
    if (analysis?.advancedStyle) {
      return analysis.advancedStyle;
    }

    // Fallback: calcola se non disponibile
    return analyzeAdvancedStyleFallback(text);
  };

  // Analisi stile avanzata (fallback)
  const analyzeAdvancedStyleFallback = (text) => {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.toLowerCase().split(/\s+/).filter(w => w.length > 0);
    
    // Analisi varietà lessicale
    const uniqueWords = new Set(words);
    const lexicalDiversity = uniqueWords.size / words.length;
    
    // <PERSON><PERSON>i lunghezza frasi
    const sentenceLengths = sentences.map(s => s.trim().split(/\s+/).length);
    const avgSentenceLength = sentenceLengths.reduce((a, b) => a + b, 0) / sentenceLengths.length;
    const sentenceVariation = Math.sqrt(sentenceLengths.reduce((sum, len) => sum + Math.pow(len - avgSentenceLength, 2), 0) / sentenceLengths.length);
    
    // Analisi complessità sintattica
    const complexSentences = sentences.filter(s => 
      s.includes(',') || s.includes(';') || s.includes(':') || s.includes('che') || s.includes('quando')
    ).length;
    const syntacticComplexity = complexSentences / sentences.length;
    
    // Analisi registro linguistico
    const formalWords = words.filter(w => 
      ['tuttavia', 'pertanto', 'inoltre', 'dunque', 'infatti', 'ovvero', 'ossia'].includes(w)
    ).length;
    const informalWords = words.filter(w => 
      ['tipo', 'roba', 'cosa', 'boh', 'mah', 'ecco'].includes(w)
    ).length;
    const registerScore = (formalWords - informalWords) / words.length;
    
    // Analisi figure retoriche
    const metaphors = text.match(/\b(come|simile a|sembrava|pareva|era come)\b/gi) || [];
    const alliterations = findAlliterations(text);
    const repetitions = findRepetitions(sentences);
    
    // Analisi ritmo e musicalità
    const shortSentences = sentenceLengths.filter(len => len <= 8).length;
    const longSentences = sentenceLengths.filter(len => len >= 20).length;
    const rhythmScore = Math.abs(shortSentences - longSentences) / sentences.length;
    
    // Analisi emotività
    const emotionalWords = words.filter(w => 
      ['amore', 'odio', 'paura', 'gioia', 'tristezza', 'rabbia', 'felicità', 'dolore', 'passione', 'desiderio'].includes(w)
    ).length;
    const emotionalIntensity = emotionalWords / words.length;
    
    return {
      lexicalDiversity: Math.round(lexicalDiversity * 100),
      avgSentenceLength: Math.round(avgSentenceLength * 10) / 10,
      sentenceVariation: Math.round(sentenceVariation * 10) / 10,
      syntacticComplexity: Math.round(syntacticComplexity * 100),
      registerScore: Math.round((registerScore + 0.1) * 500), // Normalizzato 0-100
      metaphors: metaphors.length,
      alliterations: alliterations.length,
      repetitions: repetitions.length,
      rhythmScore: Math.round((1 - rhythmScore) * 100),
      emotionalIntensity: Math.round(emotionalIntensity * 1000),
      uniqueWords: uniqueWords.size,
      totalWords: words.length,
      sentences: sentences.length
    };
  };

  const findAlliterations = (text) => {
    const words = text.toLowerCase().match(/\b[a-z]+\b/g) || [];
    const alliterations = [];
    
    for (let i = 0; i < words.length - 2; i++) {
      if (words[i][0] === words[i + 1][0] && words[i][0] === words[i + 2][0]) {
        alliterations.push(`${words[i]} ${words[i + 1]} ${words[i + 2]}`);
      }
    }
    
    return alliterations;
  };

  const findRepetitions = (sentences) => {
    const repetitions = [];
    const patterns = [];
    
    sentences.forEach(sentence => {
      const words = sentence.toLowerCase().split(/\s+/);
      for (let i = 0; i < words.length - 1; i++) {
        const pattern = `${words[i]} ${words[i + 1]}`;
        if (patterns.includes(pattern)) {
          repetitions.push(pattern);
        } else {
          patterns.push(pattern);
        }
      }
    });
    
    return [...new Set(repetitions)];
  };

  const style = getAdvancedStyleData();

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg border border-indigo-200">
        <h3 className="text-xl font-bold text-indigo-800 mb-2 flex items-center gap-2">
          <Brain className="w-6 h-6" />
          Analisi Stile Avanzata
        </h3>
        <p className="text-indigo-700 mb-4 text-sm">
          Analisi approfondita dello stile, registro linguistico e figure retoriche
        </p>

        {/* Metriche principali */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          <div className="bg-white p-3 rounded-lg border border-indigo-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(style.lexicalDiversity)}`}>
              {style.lexicalDiversity}%
            </div>
            <div className="text-xs text-gray-600">Varietà Lessicale</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-purple-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(style.syntacticComplexity)}`}>
              {style.syntacticComplexity}%
            </div>
            <div className="text-xs text-gray-600">Complessità</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-blue-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(style.rhythmScore)}`}>
              {style.rhythmScore}%
            </div>
            <div className="text-xs text-gray-600">Ritmo</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-pink-100 text-center">
            <div className="text-2xl font-bold text-pink-600">{style.emotionalIntensity}</div>
            <div className="text-xs text-gray-600">Emotività</div>
          </div>
        </div>

        {/* Analisi dettagliata */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Varietà lessicale */}
          <div className="bg-white p-4 rounded-lg border border-indigo-100">
            <h4 className="font-semibold text-indigo-800 mb-3">📚 Varietà Lessicale</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Parole uniche:</span>
                <span className="font-medium">{style.uniqueWords}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Parole totali:</span>
                <span className="font-medium">{style.totalWords}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Diversità:</span>
                <span className={`font-medium ${getScoreColor(style.lexicalDiversity)}`}>
                  {style.lexicalDiversity}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${getScoreBarColor(style.lexicalDiversity)}`}
                  style={{ width: `${style.lexicalDiversity}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Struttura delle frasi */}
          <div className="bg-white p-4 rounded-lg border border-purple-100">
            <h4 className="font-semibold text-purple-800 mb-3">📏 Struttura Frasi</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Lunghezza media:</span>
                <span className="font-medium">{style.avgSentenceLength} parole</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Variazione:</span>
                <span className="font-medium">{style.sentenceVariation}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Complessità:</span>
                <span className={`font-medium ${getScoreColor(style.syntacticComplexity)}`}>
                  {style.syntacticComplexity}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${getScoreBarColor(style.syntacticComplexity)}`}
                  style={{ width: `${style.syntacticComplexity}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Figure retoriche */}
          <div className="bg-white p-4 rounded-lg border border-blue-100">
            <h4 className="font-semibold text-blue-800 mb-3">🎭 Figure Retoriche</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Metafore/Similitudini:</span>
                <span className="font-medium">{style.metaphors}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Allitterazioni:</span>
                <span className="font-medium">{style.alliterations}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Ripetizioni:</span>
                <span className="font-medium">{style.repetitions}</span>
              </div>
            </div>
          </div>

          {/* Registro e tono */}
          <div className="bg-white p-4 rounded-lg border border-pink-100">
            <h4 className="font-semibold text-pink-800 mb-3">🎯 Registro e Tono</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Registro:</span>
                <span className="font-medium">
                  {style.registerScore > 60 ? 'Formale' : 
                   style.registerScore > 40 ? 'Neutro' : 'Informale'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Emotività:</span>
                <span className="font-medium">{style.emotionalIntensity}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Ritmo:</span>
                <span className={`font-medium ${getScoreColor(style.rhythmScore)}`}>
                  {style.rhythmScore}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${getScoreBarColor(style.rhythmScore)}`}
                  style={{ width: `${style.rhythmScore}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Consigli stilistici */}
        <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200 mt-4">
          <h4 className="font-semibold text-indigo-800 mb-2">💡 Consigli Stilistici</h4>
          <div className="text-sm text-indigo-700 space-y-1">
            {style.lexicalDiversity < 40 && (
              <p>• Varietà lessicale bassa: prova a usare sinonimi per evitare ripetizioni</p>
            )}
            {style.syntacticComplexity < 30 && (
              <p>• Frasi molto semplici: considera di aggiungere subordinate per arricchire lo stile</p>
            )}
            {style.rhythmScore < 50 && (
              <p>• Ritmo monotono: alterna frasi brevi e lunghe per migliorare il flusso</p>
            )}
            {style.metaphors === 0 && (
              <p>• Nessuna figura retorica: aggiungi metafore o similitudini per rendere il testo più vivido</p>
            )}
            {style.emotionalIntensity === 0 && (
              <p>• Tono neutro: considera di aggiungere elementi emotivi per coinvolgere il lettore</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedStyleTab;
