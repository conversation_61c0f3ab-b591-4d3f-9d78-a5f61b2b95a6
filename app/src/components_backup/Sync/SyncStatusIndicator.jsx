import React from 'react';
import { RefreshCw, ArrowR<PERSON>, Clock, FileText } from 'lucide-react';

const SyncStatusIndicator = ({ subchapter, compact = false }) => {
  if (!subchapter?.liveVersion?.contentOrigin?.sourceProject) {
    return null;
  }

  const { contentOrigin } = subchapter.liveVersion;
  const syncDate = new Date(contentOrigin.syncedAt);
  const isRecent = (Date.now() - syncDate.getTime()) < (24 * 60 * 60 * 1000); // Ultimo giorno

  if (compact) {
    return (
      <div className="flex items-center gap-1 text-xs">
        <RefreshCw className={`w-3 h-3 ${isRecent ? 'text-green-500' : 'text-blue-500'}`} />
        <span className="text-gray-600">Sync</span>
      </div>
    );
  }

  return (
    <div className="mt-2 p-2 bg-blue-50 rounded border border-blue-200">
      <div className="flex items-center gap-2 mb-1">
        <RefreshCw className={`w-3 h-3 ${isRecent ? 'text-green-600' : 'text-blue-600'}`} />
        <span className="text-xs font-medium text-blue-800">
          Sincronizzato da progetto
        </span>
        {isRecent && (
          <span className="text-xs bg-green-100 text-green-700 px-1 rounded">
            Recente
          </span>
        )}
      </div>
      
      <div className="text-xs text-gray-700 space-y-1">
        <div className="flex items-center gap-2">
          <FileText className="w-3 h-3 text-gray-500" />
          <span>Progetto: <span className="font-medium">{contentOrigin.sourceProjectTitle || 'Sconosciuto'}</span></span>
        </div>
        
        <div className="flex items-center gap-2">
          <ArrowRight className="w-3 h-3 text-gray-500" />
          <span>Modalità: <span className="font-medium capitalize">{contentOrigin.syncMode}</span></span>
        </div>
        
        <div className="flex items-center gap-2">
          <Clock className="w-3 h-3 text-gray-500" />
          <span>Sincronizzato: <span className="font-medium">{syncDate.toLocaleString()}</span></span>
        </div>
      </div>
    </div>
  );
};

export default SyncStatusIndicator;
