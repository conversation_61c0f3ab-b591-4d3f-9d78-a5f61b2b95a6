import React, { useState, useEffect } from 'react';
import { 
  RefreshCw, 
  Download, 
  Upload, 
  Clock, 
  FileText, 
  AlertCircle, 
  CheckCircle,
  ArrowRight,
  X
} from 'lucide-react';

const SyncPanel = ({ 
  chapterId, 
  subchapterId, 
  isOpen, 
  onClose,
  syncManager,
  onSubchapterUpdated 
}) => {
  const [syncOptions, setSyncOptions] = useState(null);
  const [selectedSource, setSelectedSource] = useState('');
  const [selectedTarget, setSelectedTarget] = useState('');
  const [syncMode, setSyncMode] = useState('replace');
  const [activeTab, setActiveTab] = useState('import'); // import | export | history

  const {
    isProcessing,
    syncHistory,
    updateSubchapterFromVersion,
    exportSubchapterToVersion,
    getSyncOptions
  } = syncManager;

  // Carica opzioni di sincronizzazione quando si apre il pannello
  useEffect(() => {
    if (isOpen && chapterId && subchapterId) {
      const options = getSyncOptions(chapterId, subchapterId);
      setSyncOptions(options);
    }
  }, [isOpen, chapterId, subchapterId, getSyncOptions]);

  const handleImportFromVersion = async () => {
    if (!selectedSource || !syncOptions?.currentSubchapter) return;

    const [projectId, versionId, sourceSubchapterId] = selectedSource.split('|');
    
    const result = await updateSubchapterFromVersion(
      chapterId,
      subchapterId,
      projectId,
      versionId,
      sourceSubchapterId,
      syncMode
    );

    if (result.success) {
      onSubchapterUpdated?.(result.updatedSubchapter);
      setSelectedSource('');
    }
  };

  const handleExportToVersion = async () => {
    if (!selectedTarget) return;

    const [projectId, versionId] = selectedTarget.split('|');
    
    const result = await exportSubchapterToVersion(
      chapterId,
      subchapterId,
      projectId,
      versionId
    );

    if (result.success) {
      setSelectedTarget('');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <RefreshCw className="w-6 h-6 text-blue-600" />
            Sincronizzazione Sottocapitolo
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('import')}
            className={`px-6 py-3 font-medium ${
              activeTab === 'import'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Download className="w-4 h-4 inline mr-2" />
            Importa da Versione
          </button>
          <button
            onClick={() => setActiveTab('export')}
            className={`px-6 py-3 font-medium ${
              activeTab === 'export'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Upload className="w-4 h-4 inline mr-2" />
            Esporta verso Versione
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`px-6 py-3 font-medium ${
              activeTab === 'history'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Clock className="w-4 h-4 inline mr-2" />
            Cronologia
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {/* Tab Importa */}
          {activeTab === 'import' && (
            <div className="space-y-6">
              {/* Sottocapitolo corrente */}
              {syncOptions?.currentSubchapter && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h3 className="font-medium text-blue-800 mb-2">Sottocapitolo Corrente</h3>
                  <div className="text-sm text-blue-700">
                    <p><strong>Titolo:</strong> {syncOptions.currentSubchapter.title}</p>
                    <p><strong>Parole:</strong> {syncOptions.currentSubchapter.wordCount || 0}</p>
                    <p><strong>Ultima modifica:</strong> {new Date(syncOptions.currentSubchapter.updatedAt).toLocaleString()}</p>
                  </div>
                </div>
              )}

              {/* Modalità di sincronizzazione */}
              <div>
                <h3 className="font-medium text-gray-800 mb-3">Modalità di Importazione</h3>
                <div className="grid grid-cols-3 gap-3">
                  <label className={`p-3 border rounded-lg cursor-pointer ${
                    syncMode === 'replace' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}>
                    <input
                      type="radio"
                      name="syncMode"
                      value="replace"
                      checked={syncMode === 'replace'}
                      onChange={(e) => setSyncMode(e.target.value)}
                      className="sr-only"
                    />
                    <div className="text-sm font-medium">Sostituisci</div>
                    <div className="text-xs text-gray-600">Rimpiazza completamente il testo</div>
                  </label>
                  <label className={`p-3 border rounded-lg cursor-pointer ${
                    syncMode === 'append' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}>
                    <input
                      type="radio"
                      name="syncMode"
                      value="append"
                      checked={syncMode === 'append'}
                      onChange={(e) => setSyncMode(e.target.value)}
                      className="sr-only"
                    />
                    <div className="text-sm font-medium">Aggiungi</div>
                    <div className="text-xs text-gray-600">Aggiunge alla fine</div>
                  </label>
                  <label className={`p-3 border rounded-lg cursor-pointer ${
                    syncMode === 'prepend' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}>
                    <input
                      type="radio"
                      name="syncMode"
                      value="prepend"
                      checked={syncMode === 'prepend'}
                      onChange={(e) => setSyncMode(e.target.value)}
                      className="sr-only"
                    />
                    <div className="text-sm font-medium">Anteponi</div>
                    <div className="text-xs text-gray-600">Aggiunge all'inizio</div>
                  </label>
                </div>
              </div>

              {/* Selezione versione sorgente */}
              <div>
                <h3 className="font-medium text-gray-800 mb-3">Seleziona Versione Sorgente</h3>
                {syncOptions?.availableVersions?.length > 0 ? (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {syncOptions.availableVersions.map((version) => (
                      <label
                        key={`${version.projectId}|${version.versionId}|${version.subchapterId}`}
                        className={`flex items-center p-3 border rounded-lg cursor-pointer ${
                          selectedSource === `${version.projectId}|${version.versionId}|${version.subchapterId}`
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:bg-gray-50'
                        }`}
                      >
                        <input
                          type="radio"
                          name="sourceVersion"
                          value={`${version.projectId}|${version.versionId}|${version.subchapterId}`}
                          checked={selectedSource === `${version.projectId}|${version.versionId}|${version.subchapterId}`}
                          onChange={(e) => setSelectedSource(e.target.value)}
                          className="mr-3"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-gray-800">
                            {version.projectTitle} → {version.versionName}
                          </div>
                          <div className="text-sm text-gray-600">
                            {version.chapterTitle} → {version.subchapterTitle}
                          </div>
                          <div className="text-xs text-gray-500">
                            {version.wordCount} parole • {new Date(version.updatedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                    <p>Nessuna versione disponibile per l'importazione</p>
                  </div>
                )}
              </div>

              {/* Pulsante importa */}
              <div className="flex justify-end">
                <button
                  onClick={handleImportFromVersion}
                  disabled={!selectedSource || isProcessing}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg flex items-center gap-2"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      Importando...
                    </>
                  ) : (
                    <>
                      <Download className="w-4 h-4" />
                      Importa Selezionata
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Tab Esporta */}
          {activeTab === 'export' && (
            <div className="space-y-6">
              {/* Sottocapitolo corrente */}
              {syncOptions?.currentSubchapter && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <h3 className="font-medium text-green-800 mb-2">Sottocapitolo da Esportare</h3>
                  <div className="text-sm text-green-700">
                    <p><strong>Titolo:</strong> {syncOptions.currentSubchapter.title}</p>
                    <p><strong>Parole:</strong> {syncOptions.currentSubchapter.wordCount || 0}</p>
                    <p><strong>Ultima modifica:</strong> {new Date(syncOptions.currentSubchapter.updatedAt).toLocaleString()}</p>
                  </div>
                </div>
              )}

              {/* Selezione progetto/versione destinazione */}
              <div>
                <h3 className="font-medium text-gray-800 mb-3">Seleziona Versione Destinazione</h3>
                {syncOptions?.availableProjects?.length > 0 ? (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {syncOptions.availableProjects.map((project) => (
                      <label
                        key={`${project.projectId}|${project.versionId}`}
                        className={`flex items-center p-3 border rounded-lg cursor-pointer ${
                          selectedTarget === `${project.projectId}|${project.versionId}`
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-200 hover:bg-gray-50'
                        }`}
                      >
                        <input
                          type="radio"
                          name="targetProject"
                          value={`${project.projectId}|${project.versionId}`}
                          checked={selectedTarget === `${project.projectId}|${project.versionId}`}
                          onChange={(e) => setSelectedTarget(e.target.value)}
                          className="mr-3"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-gray-800">
                            {project.projectTitle}
                          </div>
                          <div className="text-sm text-gray-600">
                            Versione: {project.versionName}
                          </div>
                          <div className="text-xs text-gray-500">
                            Aggiornato: {new Date(project.updatedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                    <p>Nessun progetto disponibile per l'esportazione</p>
                  </div>
                )}
              </div>

              {/* Pulsante esporta */}
              <div className="flex justify-end">
                <button
                  onClick={handleExportToVersion}
                  disabled={!selectedTarget || isProcessing}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg flex items-center gap-2"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      Esportando...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                      Esporta verso Selezionata
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Tab Cronologia */}
          {activeTab === 'history' && (
            <div className="space-y-4">
              <h3 className="font-medium text-gray-800">Cronologia Sincronizzazioni</h3>
              {syncHistory.length > 0 ? (
                <div className="space-y-3">
                  {syncHistory.map((record) => (
                    <div
                      key={record.id}
                      className={`p-4 rounded-lg border ${
                        record.success
                          ? 'border-green-200 bg-green-50'
                          : 'border-red-200 bg-red-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {record.success ? (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          ) : (
                            <AlertCircle className="w-4 h-4 text-red-600" />
                          )}
                          <span className="font-medium text-gray-800">
                            {record.action === 'updateFromVersion' ? 'Importazione' : 'Esportazione'}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {new Date(record.timestamp).toLocaleString()}
                        </span>
                      </div>
                      
                      {record.success ? (
                        <div className="text-sm text-gray-600">
                          {record.action === 'updateFromVersion' ? (
                            <div className="flex items-center gap-2">
                              <span>{record.source?.projectTitle} → {record.source?.subchapterTitle}</span>
                              <ArrowRight className="w-3 h-3" />
                              <span>{record.target?.subchapterTitle}</span>
                              <span className="text-xs bg-gray-200 px-2 py-1 rounded">
                                {record.mode}
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <span>{record.source?.subchapterTitle}</span>
                              <ArrowRight className="w-3 h-3" />
                              <span>{record.target?.projectTitle} → {record.target?.versionName}</span>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-sm text-red-600">
                          Errore: {record.error}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                  <p>Nessuna sincronizzazione effettuata</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SyncPanel;
