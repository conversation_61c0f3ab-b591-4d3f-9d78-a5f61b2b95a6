import { Target } from 'lucide-react';

const PlotEventsTab = ({ 
  plotEvents, 
  currentProject, 
  showEventForm, 
  setShowEventForm, 
  addPlotEvent, 
  deletePlotEvent, 
  resolveMysterySeed 
}) => {
  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-cyan-50 to-teal-50 p-4 rounded-lg border border-cyan-200">
        <h3 className="text-xl font-bold text-cyan-800 mb-2 flex items-center gap-2">
          <Target className="w-6 h-6" />
          Tracciamento Eventi di Trama
        </h3>
        <p className="text-cyan-700 mb-4 text-sm">
          G<PERSON><PERSON>ci misteri, scoperte e eventi narrativi per mantenere la coerenza della trama
        </p>

        {/* Controlli principali */}
        <div className="flex gap-2 mb-4">
          <button
            onClick={() => setShowEventForm(true)}
            className="bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700 flex items-center gap-2"
          >
            <Target className="w-4 h-4" />
            Aggiungi Evento
          </button>
        </div>

        {/* Statistiche rapide */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          <div className="bg-white p-3 rounded-lg border border-cyan-100 text-center">
            <div className="text-2xl font-bold text-cyan-600">
              {plotEvents.filter(e => e.projectId === currentProject?.id).length}
            </div>
            <div className="text-xs text-gray-600">Eventi Totali</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-red-100 text-center">
            <div className="text-2xl font-bold text-red-600">
              {plotEvents.filter(e => e.projectId === currentProject?.id && e.type === 'mystery' && e.status === 'open').length}
            </div>
            <div className="text-xs text-gray-600">Misteri Aperti</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-green-100 text-center">
            <div className="text-2xl font-bold text-green-600">
              {plotEvents.filter(e => e.projectId === currentProject?.id && e.type === 'discovery').length}
            </div>
            <div className="text-xs text-gray-600">Scoperte</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-purple-100 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {plotEvents.filter(e => e.projectId === currentProject?.id && e.type === 'mystery' && e.status === 'resolved').length}
            </div>
            <div className="text-xs text-gray-600">Misteri Risolti</div>
          </div>
        </div>

        {/* Form per aggiungere eventi */}
        {showEventForm && (
          <div className="bg-white p-4 rounded-lg border border-cyan-200 mb-4">
            <h4 className="font-semibold text-cyan-800 mb-3">➕ Nuovo Evento di Trama</h4>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target);
              addPlotEvent({
                type: formData.get('type'),
                title: formData.get('title'),
                description: formData.get('description'),
                characters: formData.get('characters').split(',').map(c => c.trim()).filter(c => c),
                location: formData.get('location'),
                importance: parseInt(formData.get('importance')),
                status: formData.get('type') === 'mystery' ? 'open' : 'completed',
                tags: formData.get('tags').split(',').map(t => t.trim()).filter(t => t)
              });
            }} className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tipo Evento</label>
                  <select name="type" required className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-cyan-500">
                    <option value="mystery">🔍 Mistero/Seme</option>
                    <option value="discovery">💡 Scoperta/Rivelazione</option>
                    <option value="conflict">⚔️ Conflitto</option>
                    <option value="resolution">✅ Risoluzione</option>
                    <option value="character">👤 Sviluppo Personaggio</option>
                    <option value="plot">📖 Evento di Trama</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Importanza (1-10)</label>
                  <input type="number" name="importance" min="1" max="10" defaultValue="5" required 
                         className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-cyan-500" />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Titolo</label>
                <input type="text" name="title" required placeholder="Es: Il mistero della chiave scomparsa"
                       className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-cyan-500" />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Descrizione</label>
                <textarea name="description" rows="3" required placeholder="Descrivi l'evento in dettaglio..."
                          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-cyan-500"></textarea>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Personaggi Coinvolti</label>
                  <input type="text" name="characters" placeholder="Marco, Lisa, Il detective (separati da virgola)"
                         className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-cyan-500" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Luogo</label>
                  <input type="text" name="location" placeholder="Es: La biblioteca, Casa di Marco"
                         className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-cyan-500" />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tag</label>
                <input type="text" name="tags" placeholder="suspense, famiglia, segreto (separati da virgola)"
                       className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-cyan-500" />
              </div>
              
              <div className="flex gap-2">
                <button type="submit" className="bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700">
                  Salva Evento
                </button>
                <button type="button" onClick={() => setShowEventForm(false)} 
                        className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                  Annulla
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Lista eventi */}
        {currentProject ? (
          <div className="space-y-4">
            {plotEvents.filter(event => event.projectId === currentProject.id).length === 0 ? (
              <div className="text-center py-8">
                <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Nessun evento registrato per questo progetto</p>
                <p className="text-sm text-gray-500 mt-2">Aggiungi eventi per tracciare la trama</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {plotEvents
                  .filter(event => event.projectId === currentProject.id)
                  .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                  .map((event) => (
                    <div key={event.id} className={`bg-white p-4 rounded-lg border-l-4 ${
                      event.type === 'mystery' && event.status === 'open' ? 'border-red-400' :
                      event.type === 'mystery' && event.status === 'resolved' ? 'border-green-400' :
                      event.type === 'discovery' ? 'border-blue-400' :
                      event.type === 'conflict' ? 'border-orange-400' :
                      'border-gray-400'
                    }`}>
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            event.type === 'mystery' ? 'bg-red-100 text-red-700' :
                            event.type === 'discovery' ? 'bg-blue-100 text-blue-700' :
                            event.type === 'conflict' ? 'bg-orange-100 text-orange-700' :
                            event.type === 'resolution' ? 'bg-green-100 text-green-700' :
                            event.type === 'character' ? 'bg-purple-100 text-purple-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {event.type === 'mystery' ? '🔍 Mistero' :
                             event.type === 'discovery' ? '💡 Scoperta' :
                             event.type === 'conflict' ? '⚔️ Conflitto' :
                             event.type === 'resolution' ? '✅ Risoluzione' :
                             event.type === 'character' ? '👤 Personaggio' :
                             '📖 Trama'}
                          </span>
                          {event.type === 'mystery' && (
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              event.status === 'open' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'
                            }`}>
                              {event.status === 'open' ? 'Aperto' : 'Risolto'}
                            </span>
                          )}
                        </div>
                        <div className="flex gap-1">
                          <button
                            onClick={() => deletePlotEvent(event.id)}
                            className="text-red-600 hover:text-red-800 text-xs"
                          >
                            Elimina
                          </button>
                        </div>
                      </div>
                      
                      <h4 className="font-semibold text-gray-800 mb-2">{event.title}</h4>
                      <p className="text-sm text-gray-600 mb-3">{event.description}</p>
                      
                      <div className="space-y-2 text-xs">
                        {event.characters && event.characters.length > 0 && (
                          <div>
                            <span className="font-medium text-gray-500">👥 Personaggi: </span>
                            <span className="text-gray-700">{event.characters.join(', ')}</span>
                          </div>
                        )}
                        {event.location && (
                          <div>
                            <span className="font-medium text-gray-500">📍 Luogo: </span>
                            <span className="text-gray-700">{event.location}</span>
                          </div>
                        )}
                        {event.tags && event.tags.length > 0 && (
                          <div>
                            <span className="font-medium text-gray-500">🏷️ Tag: </span>
                            {event.tags.map(tag => (
                              <span key={tag} className="bg-gray-100 px-2 py-1 rounded text-xs mr-1">{tag}</span>
                            ))}
                          </div>
                        )}
                        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                          <span className="text-gray-500">
                            📅 {new Date(event.timestamp).toLocaleDateString()}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="text-gray-500">Importanza:</span>
                            <div className={`w-16 bg-gray-200 rounded-full h-2`}>
                              <div 
                                className={`h-2 rounded-full ${
                                  event.importance >= 8 ? 'bg-red-500' :
                                  event.importance >= 6 ? 'bg-orange-500' :
                                  event.importance >= 4 ? 'bg-yellow-500' :
                                  'bg-gray-500'
                                }`}
                                style={{ width: `${(event.importance / 10) * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-xs font-medium">{event.importance}/10</span>
                          </div>
                        </div>
                        
                        {event.type === 'mystery' && event.status === 'open' && (
                          <div className="pt-2">
                            <button
                              onClick={() => {
                                const resolution = prompt('Come viene risolto questo mistero?');
                                if (resolution) {
                                  resolveMysterySeed(event.id, resolution);
                                }
                              }}
                              className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                            >
                              ✅ Risolvi Mistero
                            </button>
                          </div>
                        )}
                        
                        {event.resolution && (
                          <div className="pt-2 bg-green-50 p-2 rounded">
                            <span className="font-medium text-green-700">✅ Risoluzione: </span>
                            <span className="text-green-600">{event.resolution}</span>
                            <div className="text-xs text-green-500 mt-1">
                              Risolto il {new Date(event.resolvedAt).toLocaleDateString()}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Seleziona un progetto per vedere gli eventi di trama</p>
            <p className="text-sm text-gray-500 mt-2">Usa il selettore nell'header per scegliere un progetto</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlotEventsTab;
