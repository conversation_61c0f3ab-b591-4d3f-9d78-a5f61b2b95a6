import React, { useState, useEffect } from 'react';
import { X, User, MapPin, Package, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { apiService } from '../../services/api';

const ExtractCodexModal = ({ isOpen, onClose, text, onElementsCreated }) => {
  const [extractedElements, setExtractedElements] = useState([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [elementDecisions, setElementDecisions] = useState({});
  const [currentElementIndex, setCurrentElementIndex] = useState(0);

  // Estrai elementi quando si apre la modale
  useEffect(() => {
    if (isOpen && text) {
      extractElements();
    }
  }, [isOpen, text]);

  const extractElements = async () => {
    setIsExtracting(true);
    try {
      const response = await apiService.extractCodexElements(text);
      setExtractedElements(response.elements || []);
      setElementDecisions({});
      setCurrentElementIndex(0);
    } catch (error) {
      console.error('Errore estrazione elementi:', error);
      alert('Errore durante l\'estrazione degli elementi');
    } finally {
      setIsExtracting(false);
    }
  };

  const handleElementDecision = (elementIndex, decision) => {
    setElementDecisions(prev => ({
      ...prev,
      [elementIndex]: decision
    }));
  };

  const createSelectedElements = async () => {
    setIsCreating(true);
    const createdElements = [];

    try {
      for (const [index, element] of extractedElements.entries()) {
        if (elementDecisions[index] === 'create') {
          const elementData = {
            id: `${element.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: element.name,
            type: element.type,
            aliases: [],
            description: element.suggestedDescription || '',
            importance: element.suggestedImportance || 'secondary',
            category: element.suggestedCategory || 'general',
            function: element.suggestedFunction || '',
            first_appearance: `Estratto automaticamente dal testo`,
            notes: `Estratto automaticamente. Contesto: "${element.context}"`,
            tags: ['auto-extracted'],
            relationships: [],
            connected_locations: [],
            related_concepts: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          const createdElement = await apiService.createCodexElement(elementData);
          createdElements.push(createdElement);
        }
      }

      if (onElementsCreated) {
        onElementsCreated(createdElements);
      }

      alert(`${createdElements.length} elementi creati con successo!`);
      onClose();
    } catch (error) {
      console.error('Errore creazione elementi:', error);
      alert('Errore durante la creazione degli elementi');
    } finally {
      setIsCreating(false);
    }
  };

  const getElementIcon = (type) => {
    switch (type) {
      case 'character': return <User className="w-5 h-5 text-blue-500" />;
      case 'location': return <MapPin className="w-5 h-5 text-green-500" />;
      case 'object': return <Package className="w-5 h-5 text-purple-500" />;
      default: return <Package className="w-5 h-5 text-gray-500" />;
    }
  };

  const getElementTypeLabel = (type) => {
    switch (type) {
      case 'character': return 'Personaggio';
      case 'location': return 'Luogo';
      case 'object': return 'Oggetto';
      default: return 'Elemento';
    }
  };

  const selectedCount = Object.values(elementDecisions).filter(d => d === 'create').length;
  const rejectedCount = Object.values(elementDecisions).filter(d => d === 'reject').length;
  const pendingCount = extractedElements.length - selectedCount - rejectedCount;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Estrazione Automatica Codex</h2>
            <p className="text-sm text-gray-600 mt-1">
              Elementi trovati nel testo. Seleziona quali aggiungere al Codex.
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {isExtracting ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
              <span className="ml-3 text-gray-600">Estrazione elementi in corso...</span>
            </div>
          ) : extractedElements.length === 0 ? (
            <div className="text-center py-12">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Nessun nuovo elemento trovato nel testo.</p>
            </div>
          ) : (
            <>
              {/* Stats */}
              <div className="flex items-center gap-6 mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{extractedElements.length}</div>
                  <div className="text-sm text-gray-600">Trovati</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{selectedCount}</div>
                  <div className="text-sm text-gray-600">Selezionati</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{rejectedCount}</div>
                  <div className="text-sm text-gray-600">Rifiutati</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{pendingCount}</div>
                  <div className="text-sm text-gray-600">In attesa</div>
                </div>
              </div>

              {/* Elements List */}
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {extractedElements.map((element, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg p-4 transition-all ${
                      elementDecisions[index] === 'create' 
                        ? 'border-green-300 bg-green-50' 
                        : elementDecisions[index] === 'reject'
                        ? 'border-red-300 bg-red-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        {getElementIcon(element.type)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-gray-900">{element.name}</h3>
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                              {getElementTypeLabel(element.type)}
                            </span>
                            <span className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">
                              {Math.round(element.confidence * 100)}% sicurezza
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            <strong>Contesto:</strong> "{element.context}..."
                          </p>
                          {element.suggestedDescription && (
                            <p className="text-sm text-gray-500">
                              <strong>Descrizione suggerita:</strong> {element.suggestedDescription}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Decision Buttons */}
                      <div className="flex items-center gap-2 ml-4">
                        <button
                          onClick={() => handleElementDecision(index, 'create')}
                          className={`flex items-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
                            elementDecisions[index] === 'create'
                              ? 'bg-green-500 text-white'
                              : 'bg-gray-100 text-gray-600 hover:bg-green-100 hover:text-green-600'
                          }`}
                        >
                          <CheckCircle className="w-4 h-4" />
                          Crea
                        </button>
                        <button
                          onClick={() => handleElementDecision(index, 'reject')}
                          className={`flex items-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
                            elementDecisions[index] === 'reject'
                              ? 'bg-red-500 text-white'
                              : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                          }`}
                        >
                          <XCircle className="w-4 h-4" />
                          Ignora
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        {!isExtracting && extractedElements.length > 0 && (
          <div className="flex items-center justify-between p-6 border-t bg-gray-50">
            <div className="text-sm text-gray-600">
              {selectedCount > 0 ? `${selectedCount} elementi selezionati per la creazione` : 'Nessun elemento selezionato'}
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Annulla
              </button>
              <button
                onClick={createSelectedElements}
                disabled={selectedCount === 0 || isCreating}
                className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
              >
                {isCreating && <Loader2 className="w-4 h-4 animate-spin" />}
                Crea {selectedCount > 0 ? `${selectedCount} ` : ''}Elementi
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExtractCodexModal;
