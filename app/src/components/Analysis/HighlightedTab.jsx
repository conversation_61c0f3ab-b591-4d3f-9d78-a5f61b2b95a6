const HighlightedTab = ({ text, analysis, highlightText }) => {
  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
        <h3 className="font-semibold text-yellow-800 mb-3">📝 Legenda Formattazione Avanzata:</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="bg-yellow-200 px-2 py-1 rounded text-xs font-medium">Evidenziato</span>
              <span className="text-gray-700">Parole significative ripetute (&gt;5 volte)</span>
            </div>
            <div className="bg-blue-100 text-blue-900 px-2 py-1 rounded italic text-xs">
              💬 Dialoghi (con virgolette)
            </div>
            <div className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">
              🏃 Azioni (movimento, gesti)
            </div>
          </div>
          <div className="space-y-3">
            <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
              🎨 Descrizioni (aspetto, ambiente)
            </div>
            <div className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
              🧠 Narrativo (pensieri, sentimenti)
            </div>
            <div className="text-center py-2">
              <span className="text-gray-400 font-bold">• • •</span>
              <span className="text-gray-700 text-xs ml-2">Separatori di scene</span>
            </div>
          </div>
        </div>
        <div className="mt-4 p-3 bg-white rounded border border-yellow-100">
          <p className="text-xs text-gray-600">
            <strong>✨ Analisi frase per frase:</strong> Paragrafi preservati da Pages/Word, evidenziazione dialoghi a livello di frase (non paragrafo),
            rilevamento scene, soglie appropriate per letteratura (&gt;50 parole per frase)
          </p>
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div
          className="prose max-w-none"
          dangerouslySetInnerHTML={{ __html: highlightText() }}
        />
      </div>
    </div>
  );
};

export default HighlightedTab;
