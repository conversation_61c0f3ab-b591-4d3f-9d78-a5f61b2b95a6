import { Layers } from 'lucide-react';
import { useScoreUtils } from '../../hooks/useScoreUtils';

const StructureTab = ({ text, analysis }) => {
  const { getScoreColor, getScoreBarColor } = useScoreUtils();
  // Usa l'analisi già calcolata se disponibile
  const getStructuralData = () => {
    if (analysis?.structuralAnalysis) {
      return analysis.structuralAnalysis;
    }

    // Fallback: calcola se non disponibile
    return analyzeAdvancedStructureFallback(text);
  };

  // Analisi struttura avanzata (fallback)
  const analyzeAdvancedStructureFallback = (text) => {
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    
    // Analisi distribuzione contenuto
    const distribution = analyzeContentDistribution(paragraphs);
    
    // Analisi coesione testuale
    const cohesion = analyzeCohesion(sentences);
    
    // Analisi progressione narrativa
    const progression = analyzeProgression(paragraphs);
    
    // Analisi equilibrio strutturale
    const balance = analyzeBalance(paragraphs);
    
    // Analisi transizioni
    const transitions = analyzeTransitions(sentences);
    
    return {
      distribution,
      cohesion,
      progression,
      balance,
      transitions,
      totalParagraphs: paragraphs.length,
      totalSentences: sentences.length,
      totalWords: words.length
    };
  };

  const analyzeContentDistribution = (paragraphs) => {
    const types = paragraphs.map(paragraph => {
      const text = paragraph.toLowerCase();
      const dialogueRatio = (text.match(/"/g) || []).length / text.length;
      const actionWords = (text.match(/\b(corse|saltò|gridò|afferrò|spinse|tirò|colpì|mosse|alzò)\b/gi) || []).length;
      const descriptiveWords = (text.match(/\b(bello|grande|piccolo|rosso|blu|alto|basso|luminoso|scuro|verde)\b/gi) || []).length;
      const narrativeWords = (text.match(/\b(pensò|sentì|ricordò|sapeva|credeva|immaginò|sognò)\b/gi) || []).length;
      
      if (dialogueRatio > 0.1) return 'dialogue';
      if (actionWords > 2) return 'action';
      if (descriptiveWords > 2) return 'descriptive';
      if (narrativeWords > 1) return 'narrative';
      return 'mixed';
    });
    
    const counts = types.reduce((acc, type) => {
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});
    
    const total = types.length;
    const distribution = Object.entries(counts).map(([type, count]) => ({
      type,
      count,
      percentage: Math.round((count / total) * 100)
    }));
    
    return { types, distribution, balance: calculateDistributionBalance(distribution) };
  };

  const calculateDistributionBalance = (distribution) => {
    const percentages = distribution.map(d => d.percentage);
    const ideal = 100 / distribution.length;
    const variance = percentages.reduce((sum, p) => sum + Math.pow(p - ideal, 2), 0) / distribution.length;
    return Math.max(0, Math.round(100 - variance));
  };

  const analyzeCohesion = (sentences) => {
    let cohesionScore = 0;
    const connectors = ['inoltre', 'tuttavia', 'quindi', 'infatti', 'perciò', 'dunque', 'così', 'allora', 'poi', 'infine'];
    const references = ['questo', 'quello', 'questi', 'quelli', 'tale', 'suddetto', 'anzidetto'];
    
    sentences.forEach((sentence, index) => {
      const lowerSentence = sentence.toLowerCase();
      
      // Presenza di connettori
      if (connectors.some(conn => lowerSentence.includes(conn))) {
        cohesionScore += 2;
      }
      
      // Riferimenti anaforici
      if (references.some(ref => lowerSentence.includes(ref))) {
        cohesionScore += 1;
      }
      
      // Ripetizione lessicale con frase precedente
      if (index > 0) {
        const prevWords = new Set(sentences[index - 1].toLowerCase().split(/\s+/));
        const currWords = sentence.toLowerCase().split(/\s+/);
        const commonWords = currWords.filter(word => prevWords.has(word) && word.length > 3);
        if (commonWords.length > 0) {
          cohesionScore += commonWords.length;
        }
      }
    });
    
    return Math.min(100, Math.round((cohesionScore / sentences.length) * 10));
  };

  const analyzeProgression = (paragraphs) => {
    const progression = paragraphs.map((paragraph, index) => {
      const wordCount = paragraph.split(/\s+/).length;
      const complexity = (paragraph.match(/[,;:]/g) || []).length / wordCount;
      const tension = (paragraph.match(/[!?]/g) || []).length / wordCount;
      
      return {
        index: index + 1,
        wordCount,
        complexity: Math.round(complexity * 100),
        tension: Math.round(tension * 100),
        position: index / (paragraphs.length - 1) // 0 = inizio, 1 = fine
      };
    });
    
    // Analisi curva di tensione
    const tensionCurve = progression.map(p => p.tension);
    const avgTension = tensionCurve.reduce((a, b) => a + b, 0) / tensionCurve.length;
    
    // Verifica crescendo narrativo
    const crescendo = analyzeNarrativeCrescendo(tensionCurve);
    
    return { progression, avgTension, crescendo };
  };

  const analyzeNarrativeCrescendo = (tensionCurve) => {
    if (tensionCurve.length < 3) return { present: false, score: 0 };
    
    const third = Math.floor(tensionCurve.length / 3);
    const beginning = tensionCurve.slice(0, third);
    const middle = tensionCurve.slice(third, third * 2);
    const end = tensionCurve.slice(third * 2);
    
    const avgBeginning = beginning.reduce((a, b) => a + b, 0) / beginning.length;
    const avgMiddle = middle.reduce((a, b) => a + b, 0) / middle.length;
    const avgEnd = end.reduce((a, b) => a + b, 0) / end.length;
    
    const crescendoScore = Math.round(((avgMiddle - avgBeginning) + (avgEnd - avgMiddle)) / 2);
    
    return {
      present: crescendoScore > 5,
      score: Math.max(0, crescendoScore),
      phases: { beginning: Math.round(avgBeginning), middle: Math.round(avgMiddle), end: Math.round(avgEnd) }
    };
  };

  const analyzeBalance = (paragraphs) => {
    const lengths = paragraphs.map(p => p.split(/\s+/).length);
    const avgLength = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    const variation = Math.sqrt(lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length);
    
    const shortParagraphs = lengths.filter(len => len < avgLength * 0.7).length;
    const longParagraphs = lengths.filter(len => len > avgLength * 1.3).length;
    const mediumParagraphs = lengths.length - shortParagraphs - longParagraphs;
    
    const balanceScore = Math.round(100 - (variation / avgLength) * 50);
    
    return {
      avgLength: Math.round(avgLength),
      variation: Math.round(variation),
      distribution: { short: shortParagraphs, medium: mediumParagraphs, long: longParagraphs },
      score: Math.max(0, balanceScore)
    };
  };

  const analyzeTransitions = (sentences) => {
    const transitionWords = {
      temporal: ['poi', 'dopo', 'prima', 'mentre', 'quando', 'infine', 'allora'],
      causal: ['perché', 'poiché', 'quindi', 'perciò', 'così', 'dunque'],
      adversative: ['ma', 'però', 'tuttavia', 'invece', 'nonostante'],
      additive: ['inoltre', 'anche', 'pure', 'ancora', 'perfino']
    };
    
    const transitions = {
      temporal: 0,
      causal: 0,
      adversative: 0,
      additive: 0,
      total: 0
    };
    
    sentences.forEach(sentence => {
      const lowerSentence = sentence.toLowerCase();
      Object.entries(transitionWords).forEach(([type, words]) => {
        if (words.some(word => lowerSentence.includes(word))) {
          transitions[type]++;
          transitions.total++;
        }
      });
    });
    
    const density = Math.round((transitions.total / sentences.length) * 100);
    
    return { ...transitions, density };
  };

  const structure = getStructuralData();

  const getTypeColor = (type) => {
    const colors = {
      dialogue: 'bg-blue-100 text-blue-800',
      action: 'bg-red-100 text-red-800',
      descriptive: 'bg-green-100 text-green-800',
      narrative: 'bg-purple-100 text-purple-800',
      mixed: 'bg-gray-100 text-gray-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-4 rounded-lg border border-slate-200">
        <h3 className="text-xl font-bold text-slate-800 mb-2 flex items-center gap-2">
          <Layers className="w-6 h-6" />
          Analisi Struttura Avanzata
        </h3>
        <p className="text-slate-700 mb-4 text-sm">
          Analisi approfondita di coesione, progressione narrativa e equilibrio strutturale
        </p>

        {/* Metriche principali */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          <div className="bg-white p-3 rounded-lg border border-slate-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(structure.cohesion)}`}>
              {structure.cohesion}%
            </div>
            <div className="text-xs text-gray-600">Coesione</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-blue-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(structure.distribution.balance)}`}>
              {structure.distribution.balance}%
            </div>
            <div className="text-xs text-gray-600">Equilibrio</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-purple-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(structure.balance.score)}`}>
              {structure.balance.score}%
            </div>
            <div className="text-xs text-gray-600">Bilanciamento</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-green-100 text-center">
            <div className="text-2xl font-bold text-green-600">{structure.transitions.density}%</div>
            <div className="text-xs text-gray-600">Transizioni</div>
          </div>
        </div>

        {/* Distribuzione contenuto */}
        <div className="bg-white p-4 rounded-lg border border-slate-100 mb-4">
          <h4 className="font-semibold text-slate-800 mb-3">📊 Distribuzione Contenuto</h4>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3 mb-4">
            {structure.distribution.distribution.map(item => (
              <div key={item.type} className={`p-3 rounded-lg text-center ${getTypeColor(item.type)}`}>
                <div className="text-lg font-bold">{item.count}</div>
                <div className="text-xs">{item.type}</div>
                <div className="text-xs opacity-75">{item.percentage}%</div>
              </div>
            ))}
          </div>
          <div className="text-sm text-slate-600">
            Equilibrio distribuzione: <span className={`font-medium ${getScoreColor(structure.distribution.balance)}`}>
              {structure.distribution.balance}%
            </span>
          </div>
        </div>

        {/* Progressione narrativa */}
        <div className="bg-white p-4 rounded-lg border border-slate-100 mb-4">
          <h4 className="font-semibold text-slate-800 mb-3">📈 Progressione Narrativa</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h5 className="font-medium text-slate-700 mb-2">Curva di Tensione</h5>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Inizio:</span>
                  <span className="font-medium">{structure.progression.crescendo.phases?.beginning || 0}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Sviluppo:</span>
                  <span className="font-medium">{structure.progression.crescendo.phases?.middle || 0}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Climax:</span>
                  <span className="font-medium">{structure.progression.crescendo.phases?.end || 0}%</span>
                </div>
              </div>
            </div>
            <div>
              <h5 className="font-medium text-slate-700 mb-2">Crescendo Narrativo</h5>
              <div className="text-center">
                <div className={`text-3xl mb-2 ${structure.progression.crescendo.present ? 'text-green-600' : 'text-red-600'}`}>
                  {structure.progression.crescendo.present ? '✓' : '✗'}
                </div>
                <div className="text-sm text-slate-600">
                  {structure.progression.crescendo.present ? 'Presente' : 'Assente'}
                </div>
                <div className="text-xs text-slate-500 mt-1">
                  Punteggio: {structure.progression.crescendo.score}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Analisi transizioni */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg border border-slate-100">
            <h4 className="font-semibold text-slate-800 mb-3">🔗 Coesione Testuale</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Punteggio coesione:</span>
                <span className={`font-medium ${getScoreColor(structure.cohesion)}`}>
                  {structure.cohesion}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${getScoreBarColor(structure.cohesion)}`}
                  style={{ width: `${structure.cohesion}%` }}
                ></div>
              </div>
              <p className="text-xs text-slate-600 mt-2">
                {structure.cohesion >= 70 ? 'Ottima coesione tra le parti' :
                 structure.cohesion >= 50 ? 'Coesione accettabile' :
                 'Migliorare i collegamenti tra frasi'}
              </p>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-slate-100">
            <h4 className="font-semibold text-slate-800 mb-3">➡️ Connettori Testuali</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Temporali:</span>
                <span className="font-medium">{structure.transitions.temporal}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Causali:</span>
                <span className="font-medium">{structure.transitions.causal}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Avversativi:</span>
                <span className="font-medium">{structure.transitions.adversative}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Additivi:</span>
                <span className="font-medium">{structure.transitions.additive}</span>
              </div>
              <div className="text-xs text-slate-600 mt-2">
                Densità: {structure.transitions.density}%
              </div>
            </div>
          </div>
        </div>

        {/* Consigli strutturali */}
        <div className="bg-slate-50 p-4 rounded-lg border border-slate-200 mt-4">
          <h4 className="font-semibold text-slate-800 mb-2">💡 Consigli Strutturali</h4>
          <div className="text-sm text-slate-700 space-y-1">
            {structure.cohesion < 50 && (
              <p>• Coesione bassa: aggiungi più connettori e riferimenti tra le frasi</p>
            )}
            {structure.distribution.balance < 60 && (
              <p>• Distribuzione sbilanciata: equilibra meglio dialoghi, azioni e descrizioni</p>
            )}
            {!structure.progression.crescendo.present && (
              <p>• Manca crescendo narrativo: costruisci una progressione di tensione</p>
            )}
            {structure.transitions.density < 20 && (
              <p>• Poche transizioni: usa più connettori per migliorare il flusso</p>
            )}
            {structure.balance.score < 50 && (
              <p>• Paragrafi sbilanciati: varia la lunghezza per migliorare il ritmo</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StructureTab;
