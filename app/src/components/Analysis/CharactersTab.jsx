import { useState, useEffect } from 'react';
import { Users, RefreshCw } from 'lucide-react';
import { useAnalysisStorage } from '../../hooks/useAnalysisStorage';
import { useAsyncOperation } from '../../hooks/useAsyncOperation';
import { useServerConnection } from '../../hooks/useServerConnection';

const CharactersTab = ({ analysis, currentProject, currentVersion }) => {
  const [characters, setCharacters] = useState([]);

  const { isConnected } = useServerConnection();
  const { loadCharactersFromServer } = useAnalysisStorage();
  const { isLoading, error, execute } = useAsyncOperation();

  // Carica personaggi dal server
  useEffect(() => {
    if (isConnected && currentProject) {
      loadCharacters();
    }
  }, [isConnected, currentProject]);

  // Ricarica personaggi quando cambia l'analisi
  useEffect(() => {
    if (analysis && analysis.characters && isConnected && currentProject) {
      console.log('🔄 Analisi aggiornata, ricarico personaggi...');
      loadCharacters();
    }
  }, [analysis, isConnected, currentProject]);

  const loadCharacters = async () => {
    try {
      await execute(
        () => loadCharactersFromServer(currentProject?.id, currentVersion?.id),
        {
          onSuccess: (data) => {
            console.log('🔍 Personaggi caricati dal server:', data);
            setCharacters(data);
          },
          showConsoleLog: true
        }
      );
    } catch (err) {
      console.error('Errore caricamento personaggi:', err);
    }
  };

  // Fallback ai personaggi dall'analisi locale se server non disponibile
  const characterAnalysis = analysis?.characters || { characters: [], source: 'no-api', totalMentions: 0 };
  const displayCharacters = isConnected ? characters : characterAnalysis.characters;





  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-xl font-bold text-blue-800 flex items-center gap-2">
            <Users className="w-6 h-6" />
            Personaggi
            {isConnected ? (
              <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">🗄️ Server</span>
            ) : characterAnalysis.source === 'ai' ? (
              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">🤖 AI</span>
            ) : (
              <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">📱 Locale</span>
            )}
          </h3>
          {isConnected && currentProject && (
            <button
              onClick={loadCharacters}
              disabled={isLoading}
              className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
              title="Ricarica personaggi dal server"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Ricarica
            </button>
          )}
        </div>
        <p className="text-blue-700 mb-4 text-sm">
          {isConnected ?
            'Personaggi salvati nel database del server - Gestione professionale' :
            characterAnalysis.source === 'ai' ?
              'Estrazione avanzata con intelligenza artificiale - Visualizzazione a colonne per analisi professionale' :
              'Estrazione automatica e profilazione dei personaggi - Layout ottimizzato per scrittori'}
        </p>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-blue-600">Caricamento personaggi...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <Users className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <p className="text-red-600 font-medium">Errore caricamento personaggi</p>
            <p className="text-sm text-gray-600 mt-2">{error}</p>
            <button
              onClick={loadCharacters}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Riprova
            </button>
          </div>
        ) : (!displayCharacters || displayCharacters.length === 0) ? (
          <div className="text-center py-8">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">
              {isConnected ? 'Nessun personaggio salvato nel database' : 'Nessun personaggio identificato nel testo'}
            </p>
            <p className="text-sm text-gray-500 mt-2">
              {isConnected ?
                'I personaggi verranno aggiunti automaticamente quando esegui un\'analisi del testo' :
                'Prova ad aggiungere nomi propri o ruoli specifici'}
            </p>
            {!isConnected && (
              <div className="mt-4 p-3 bg-orange-50 rounded-lg border border-orange-200">
                <p className="text-xs text-orange-700">
                  💡 L'analisi personaggi utilizza intelligenza artificiale per identificare e profilare automaticamente i personaggi del tuo testo.
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {/* Statistiche Generali */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="bg-white p-3 rounded-lg border border-blue-100 text-center">
                <div className="text-2xl font-bold text-blue-600">{displayCharacters.length}</div>
                <div className="text-xs text-gray-600">Personaggi</div>
              </div>
              <div className="bg-white p-3 rounded-lg border border-green-100 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {isConnected ?
                    displayCharacters.reduce((sum, char) => {
                      const mentions = char.plot_impact ?
                        (typeof char.plot_impact === 'string' ?
                          JSON.parse(char.plot_impact).mentions :
                          char.plot_impact.mentions) || 0 : 0;
                      return sum + mentions;
                    }, 0) :
                    characterAnalysis.totalMentions || 0}
                </div>
                <div className="text-xs text-gray-600">Menzioni totali</div>
              </div>
              <div className="bg-white p-3 rounded-lg border border-purple-100 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {displayCharacters.length > 0 ?
                    Math.round((isConnected ?
                      displayCharacters.reduce((sum, char) => {
                        const mentions = char.plot_impact ?
                          (typeof char.plot_impact === 'string' ?
                            JSON.parse(char.plot_impact).mentions :
                            char.plot_impact.mentions) || 0 : 0;
                        return sum + mentions;
                      }, 0) :
                      characterAnalysis.totalMentions || 0) / displayCharacters.length) : 0}
                </div>
                <div className="text-xs text-gray-600">Media menzioni</div>
              </div>
              <div className="bg-white p-3 rounded-lg border border-orange-100 text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {displayCharacters.length > 0 ?
                    Math.max(...displayCharacters.map(c => {
                      const mentions = c.plot_impact ?
                        (typeof c.plot_impact === 'string' ?
                          JSON.parse(c.plot_impact).mentions :
                          c.plot_impact.mentions) || 0 : 0;
                      return mentions;
                    })) : 0}
                </div>
                <div className="text-xs text-gray-600">Max menzioni</div>
              </div>
            </div>

            {/* Layout a Colonne Professionale */}
            <div className="bg-white rounded-lg border border-blue-100 overflow-hidden">
              <div className="p-4 border-b border-gray-200 bg-blue-50">
                <h4 className="font-semibold text-blue-800">👥 Analisi Dettagliata Personaggi - 8 Colonne</h4>
                <p className="text-xs text-blue-600 mt-1">Layout ottimizzato per analisi letteraria professionale</p>
              </div>
              
              {/* Tabella a colonne */}
              <div className="overflow-x-auto">
                <table className="w-full min-w-[1200px]">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                        👤 Nome
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                        🎨 Tratti Fisici
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                        🧠 Personalità
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                        ⚡ Azioni
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-48">
                        💬 Dialoghi
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                        🤝 Relazioni
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                        📊 Importanza
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                        📈 Menzioni
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {displayCharacters.map((character, index) => {
                      // Adatta i dati del server al formato dell'analisi locale
                      const adaptedCharacter = isConnected ? {
                        name: character.name,
                        type: 'nome_proprio', // Default per dati server
                        physicalDescription: character.description || '',
                        physicalTraits: character.physical_traits ?
                          (typeof character.physical_traits === 'string' ?
                            JSON.parse(character.physical_traits) :
                            character.physical_traits) : [],
                        personalityTraits: character.personality_traits ?
                          (typeof character.personality_traits === 'string' ?
                            JSON.parse(character.personality_traits) :
                            character.personality_traits) : [],
                        plotImpact: character.plot_impact ?
                          (typeof character.plot_impact === 'string' ?
                            JSON.parse(character.plot_impact) :
                            character.plot_impact) : {},
                        // Estrai mentions e importance da plot_impact
                        mentions: character.plot_impact ?
                          (typeof character.plot_impact === 'string' ?
                            JSON.parse(character.plot_impact).mentions :
                            character.plot_impact.mentions) || 0 : 0,
                        importance: character.plot_impact ?
                          (typeof character.plot_impact === 'string' ?
                            JSON.parse(character.plot_impact).importance :
                            character.plot_impact.importance) || 1 : 1,
                        // Estrai altri dati da plot_impact
                        actions: character.plot_impact ?
                          (typeof character.plot_impact === 'string' ?
                            JSON.parse(character.plot_impact).actions :
                            character.plot_impact.actions) || [] : [],
                        dialogues: character.plot_impact ?
                          (typeof character.plot_impact === 'string' ?
                            JSON.parse(character.plot_impact).dialogues :
                            character.plot_impact.dialogues) || [] : [],
                        relationships: character.plot_impact ?
                          (typeof character.plot_impact === 'string' ?
                            JSON.parse(character.plot_impact).relationships :
                            character.plot_impact.relationships) || [] : []
                      } : character;

                      return (
                      <tr key={index} className="hover:bg-gray-50">
                        {/* Nome */}
                        <td className="px-3 py-3 whitespace-nowrap">
                          <div className="flex flex-col">
                            <div className="text-sm font-medium text-gray-900">{adaptedCharacter.name}</div>
                            <div className="flex gap-1 mt-1">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                adaptedCharacter.type === 'nome_proprio'
                                  ? 'bg-blue-100 text-blue-700'
                                  : 'bg-green-100 text-green-700'
                              }`}>
                                {adaptedCharacter.type === 'nome_proprio' ? 'Nome' : 'Ruolo'}
                              </span>
                            </div>
                            {adaptedCharacter.aliases && adaptedCharacter.aliases.length > 0 && (
                              <div className="text-xs text-gray-500 mt-1">
                                Alias: {adaptedCharacter.aliases.slice(0, 2).join(', ')}
                              </div>
                            )}
                          </div>
                        </td>

                        {/* Tratti Fisici */}
                        <td className="px-3 py-3">
                          <div className="text-sm text-gray-700">
                            {adaptedCharacter.physicalTraits && adaptedCharacter.physicalTraits.length > 0 ? (
                              <div className="space-y-1">
                                {adaptedCharacter.physicalTraits.slice(0, 3).map((trait, i) => (
                                  <div key={i} className="bg-pink-50 px-2 py-1 rounded text-xs">
                                    {trait}
                                  </div>
                                ))}
                                {adaptedCharacter.physicalTraits.length > 3 && (
                                  <div className="text-xs text-gray-500">+{adaptedCharacter.physicalTraits.length - 3} altri</div>
                                )}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">Non specificati</span>
                            )}
                          </div>
                        </td>

                        {/* Personalità */}
                        <td className="px-3 py-3">
                          <div className="text-sm text-gray-700">
                            {adaptedCharacter.personalityTraits && adaptedCharacter.personalityTraits.length > 0 ? (
                              <div className="space-y-1">
                                {adaptedCharacter.personalityTraits.slice(0, 3).map((trait, i) => (
                                  <div key={i} className="bg-purple-50 px-2 py-1 rounded text-xs">
                                    {trait}
                                  </div>
                                ))}
                                {adaptedCharacter.personalityTraits.length > 3 && (
                                  <div className="text-xs text-gray-500">+{adaptedCharacter.personalityTraits.length - 3} altri</div>
                                )}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">Non specificati</span>
                            )}
                          </div>
                        </td>

                        {/* Azioni */}
                        <td className="px-3 py-3">
                          <div className="text-sm text-gray-700">
                            {character.actions && character.actions.length > 0 ? (
                              <div className="space-y-1">
                                {character.actions.slice(0, 3).map((action, i) => (
                                  <div key={i} className="bg-orange-50 px-2 py-1 rounded text-xs">
                                    {action.length > 30 ? action.substring(0, 30) + '...' : action}
                                  </div>
                                ))}
                                {character.actions.length > 3 && (
                                  <div className="text-xs text-gray-500">+{character.actions.length - 3} altre</div>
                                )}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">Nessuna azione</span>
                            )}
                          </div>
                        </td>

                        {/* Dialoghi */}
                        <td className="px-3 py-3">
                          <div className="text-sm text-gray-700">
                            {character.dialogues && character.dialogues.length > 0 ? (
                              <div className="space-y-1">
                                {character.dialogues.slice(0, 2).map((dialogue, i) => (
                                  <div key={i} className="bg-blue-50 px-2 py-1 rounded text-xs italic">
                                    "{dialogue.length > 40 ? dialogue.substring(0, 40) + '...' : dialogue}"
                                  </div>
                                ))}
                                {character.dialogues.length > 2 && (
                                  <div className="text-xs text-gray-500">+{character.dialogues.length - 2} altri</div>
                                )}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">Nessun dialogo</span>
                            )}
                          </div>
                        </td>

                        {/* Relazioni */}
                        <td className="px-3 py-3">
                          <div className="text-sm text-gray-700">
                            {character.relationships && character.relationships.length > 0 ? (
                              <div className="space-y-1">
                                {character.relationships.slice(0, 3).map((rel, i) => (
                                  <div key={i} className="bg-green-50 px-2 py-1 rounded text-xs">
                                    {rel}
                                  </div>
                                ))}
                                {character.relationships.length > 3 && (
                                  <div className="text-xs text-gray-500">+{character.relationships.length - 3} altre</div>
                                )}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">Non specificate</span>
                            )}
                          </div>
                        </td>

                        {/* Importanza */}
                        <td className="px-3 py-3 whitespace-nowrap text-center">
                          <div className="flex flex-col items-center">
                            <div className={`text-lg font-bold ${
                              (character.importance || 0) >= 8 ? 'text-red-600' :
                              (character.importance || 0) >= 6 ? 'text-orange-600' :
                              (character.importance || 0) >= 4 ? 'text-yellow-600' :
                              'text-gray-600'
                            }`}>
                              {character.importance || Math.min(10, character.mentions || 1)}
                            </div>
                            <div className="text-xs text-gray-500">/10</div>
                            <div className={`w-full bg-gray-200 rounded-full h-1.5 mt-1`}>
                              <div 
                                className={`h-1.5 rounded-full ${
                                  (character.importance || 0) >= 8 ? 'bg-red-500' :
                                  (character.importance || 0) >= 6 ? 'bg-orange-500' :
                                  (character.importance || 0) >= 4 ? 'bg-yellow-500' :
                                  'bg-gray-500'
                                }`}
                                style={{ width: `${((character.importance || Math.min(10, character.mentions || 1)) / 10) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </td>

                        {/* Menzioni */}
                        <td className="px-3 py-3 whitespace-nowrap text-center">
                          <div className="text-lg font-bold text-blue-600">
                            {adaptedCharacter.mentions || adaptedCharacter.importance || 1}
                          </div>
                          <div className="text-xs text-gray-500">volte</div>
                        </td>
                      </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Legenda */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h5 className="font-semibold text-gray-800 mb-2">📋 Legenda Layout Professionale</h5>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-pink-100 rounded"></div>
                  <span>Tratti fisici</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-purple-100 rounded"></div>
                  <span>Personalità</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-orange-100 rounded"></div>
                  <span>Azioni</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-100 rounded"></div>
                  <span>Dialoghi</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-100 rounded"></div>
                  <span>Relazioni</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span>Importanza alta (8-10)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-orange-500 rounded"></div>
                  <span>Importanza media (6-7)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                  <span>Importanza bassa (4-5)</span>
                </div>
              </div>
              <p className="text-xs text-gray-600 mt-3">
                💡 <strong>Layout ottimizzato per scrittori:</strong> Visualizzazione a colonne per analisi rapida e confronto personaggi. 
                Scorri orizzontalmente per vedere tutte le colonne su schermi piccoli.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CharactersTab;
