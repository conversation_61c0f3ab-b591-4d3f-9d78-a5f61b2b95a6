import { BookOpen } from 'lucide-react';

const SemanticTab = ({ text, analysis }) => {
  // Usa l'analisi già calcolata se disponibile
  const getSemanticData = () => {
    if (analysis?.semanticAnalysis) {
      // Se l'analisi dal hook ha una struttura diversa, adattala
      const hookData = analysis.semanticAnalysis;
      if (hookData.semanticFields) {
        // Adatta la struttura del hook alla struttura attesa dal componente
        return {
          categories: hookData.semanticFields,
          themes: [],
          coherence: 75, // Valore di default
          conceptDensity: 60, // Valore di default
          dominantField: hookData.dominantField || 'neutral',
          totalWords: 0,
          sentences: 0
        };
      }
      return hookData;
    }

    // Fallback: calcola se non disponibile
    return analyzeSemanticsFallback(text);
  };

  // Analisi semantica avanzata (fallback)
  const analyzeSemanticsFallback = (text) => {
    const words = text.toLowerCase().split(/\s+/).filter(w => w.length > 2);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    // Categorie semantiche
    const categories = {
      emotions: ['amore', 'odio', 'paura', 'gioia', 'tristezza', 'rabbia', 'felicità', 'dolore', 'passione', 'desiderio', 'speranza', 'disperazione'],
      actions: ['correre', 'saltare', 'camminare', 'volare', 'cadere', 'alzare', 'prendere', 'dare', 'guardare', 'sentire', 'toccare'],
      nature: ['sole', 'luna', 'stelle', 'mare', 'montagna', 'fiume', 'albero', 'fiore', 'vento', 'pioggia', 'neve', 'fuoco'],
      time: ['ieri', 'oggi', 'domani', 'mattina', 'sera', 'notte', 'giorno', 'ora', 'minuto', 'secondo', 'anno', 'mese'],
      space: ['qui', 'là', 'sopra', 'sotto', 'dentro', 'fuori', 'vicino', 'lontano', 'davanti', 'dietro', 'centro', 'bordo'],
      people: ['uomo', 'donna', 'bambino', 'ragazzo', 'ragazza', 'madre', 'padre', 'figlio', 'figlia', 'amico', 'nemico'],
      senses: ['vedere', 'sentire', 'toccare', 'gustare', 'odorare', 'ascoltare', 'guardare', 'osservare', 'annusare']
    };
    
    // Conta parole per categoria
    const semanticCounts = {};
    Object.keys(categories).forEach(category => {
      semanticCounts[category] = words.filter(word => 
        categories[category].some(catWord => word.includes(catWord))
      ).length;
    });
    
    // Analisi temi principali
    const themes = extractThemes(text);
    
    // Analisi coerenza semantica
    const coherence = calculateSemanticCoherence(sentences);
    
    // Analisi densità concettuale
    const conceptDensity = calculateConceptDensity(words);
    
    // Analisi campo semantico dominante
    const dominantField = Object.entries(semanticCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    return {
      categories: semanticCounts,
      themes,
      coherence,
      conceptDensity,
      dominantField: dominantField ? dominantField[0] : 'neutral',
      totalWords: words.length,
      sentences: sentences.length
    };
  };

  const extractThemes = (text) => {
    const themes = [];
    const themePatterns = {
      'Amore e Relazioni': /\b(amore|amare|cuore|bacio|abbracciare|insieme|coppia|matrimonio|fidanzato|fidanzata)\b/gi,
      'Natura e Ambiente': /\b(natura|albero|fiore|mare|montagna|cielo|terra|verde|blu|naturale)\b/gi,
      'Tempo e Memoria': /\b(tempo|ricordo|passato|futuro|memoria|nostalgia|storia|epoca|momento)\b/gi,
      'Morte e Vita': /\b(morte|morire|vita|vivere|nascita|fine|eternità|destino|esistenza)\b/gi,
      'Guerra e Pace': /\b(guerra|pace|battaglia|conflitto|soldato|arma|vittoria|sconfitta|nemico)\b/gi,
      'Famiglia': /\b(famiglia|madre|padre|figlio|figlia|fratello|sorella|nonno|nonna|casa)\b/gi,
      'Viaggio e Movimento': /\b(viaggio|strada|cammino|partire|arrivare|muovere|andare|venire|meta)\b/gi,
      'Conoscenza e Saggezza': /\b(sapere|conoscenza|saggezza|imparare|studiare|libro|scuola|maestro)\b/gi
    };
    
    Object.entries(themePatterns).forEach(([theme, pattern]) => {
      const matches = text.match(pattern) || [];
      if (matches.length > 0) {
        themes.push({
          name: theme,
          frequency: matches.length,
          examples: [...new Set(matches.slice(0, 3))]
        });
      }
    });
    
    return themes.sort((a, b) => b.frequency - a.frequency);
  };

  const calculateSemanticCoherence = (sentences) => {
    if (sentences.length < 2) return 100;
    
    let coherenceScore = 0;
    const keywordSets = sentences.map(sentence => 
      new Set(sentence.toLowerCase().split(/\s+/).filter(w => w.length > 3))
    );
    
    for (let i = 0; i < keywordSets.length - 1; i++) {
      const intersection = new Set([...keywordSets[i]].filter(x => keywordSets[i + 1].has(x)));
      const union = new Set([...keywordSets[i], ...keywordSets[i + 1]]);
      coherenceScore += intersection.size / union.size;
    }
    
    return Math.round((coherenceScore / (sentences.length - 1)) * 100);
  };

  const calculateConceptDensity = (words) => {
    const concepts = words.filter(word => 
      word.length > 4 && 
      !['molto', 'tanto', 'poco', 'sempre', 'mai', 'ancora', 'già', 'proprio'].includes(word)
    );
    return Math.round((concepts.length / words.length) * 100);
  };

  const semantics = getSemanticData();

  // Controlli di sicurezza per evitare errori
  const safeSemantics = {
    categories: semantics.categories || {},
    themes: semantics.themes || [],
    coherence: semantics.coherence || 0,
    conceptDensity: semantics.conceptDensity || 0,
    dominantField: semantics.dominantField || 'neutral',
    totalWords: semantics.totalWords || 0,
    sentences: semantics.sentences || 0
  };

  const getCategoryColor = (category) => {
    const colors = {
      emotions: 'bg-red-100 text-red-800',
      actions: 'bg-blue-100 text-blue-800',
      nature: 'bg-green-100 text-green-800',
      time: 'bg-purple-100 text-purple-800',
      space: 'bg-yellow-100 text-yellow-800',
      people: 'bg-pink-100 text-pink-800',
      senses: 'bg-indigo-100 text-indigo-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryIcon = (category) => {
    const icons = {
      emotions: '❤️',
      actions: '⚡',
      nature: '🌿',
      time: '⏰',
      space: '📍',
      people: '👥',
      senses: '👁️'
    };
    return icons[category] || '📝';
  };

  const getCategoryLabel = (category) => {
    const labels = {
      emotions: 'Emozioni',
      actions: 'Azioni',
      nature: 'Natura',
      time: 'Tempo',
      space: 'Spazio',
      people: 'Persone',
      senses: 'Sensi'
    };
    return labels[category] || category;
  };

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200">
        <h3 className="text-xl font-bold text-emerald-800 mb-2 flex items-center gap-2">
          <BookOpen className="w-6 h-6" />
          Analisi Semantica Avanzata
        </h3>
        <p className="text-emerald-700 mb-4 text-sm">
          Analisi dei significati, temi e coerenza concettuale del testo
        </p>

        {/* Metriche principali */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          <div className="bg-white p-3 rounded-lg border border-emerald-100 text-center">
            <div className="text-2xl font-bold text-emerald-600">{safeSemantics.coherence}%</div>
            <div className="text-xs text-gray-600">Coerenza</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-teal-100 text-center">
            <div className="text-2xl font-bold text-teal-600">{safeSemantics.conceptDensity}%</div>
            <div className="text-xs text-gray-600">Densità Concetti</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-blue-100 text-center">
            <div className="text-2xl font-bold text-blue-600">{safeSemantics.themes.length}</div>
            <div className="text-xs text-gray-600">Temi Identificati</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-purple-100 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {getCategoryIcon(safeSemantics.dominantField)}
            </div>
            <div className="text-xs text-gray-600">Campo Dominante</div>
          </div>
        </div>

        {/* Categorie semantiche */}
        <div className="bg-white p-4 rounded-lg border border-emerald-100 mb-4">
          <h4 className="font-semibold text-emerald-800 mb-3">🏷️ Categorie Semantiche</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {Object.entries(safeSemantics.categories).map(([category, count]) => (
              <div key={category} className={`p-3 rounded-lg ${getCategoryColor(category)}`}>
                <div className="text-center">
                  <div className="text-2xl mb-1">{getCategoryIcon(category)}</div>
                  <div className="text-lg font-bold">{count}</div>
                  <div className="text-xs">{getCategoryLabel(category)}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Temi principali */}
        {safeSemantics.themes.length > 0 && (
          <div className="bg-white p-4 rounded-lg border border-emerald-100 mb-4">
            <h4 className="font-semibold text-emerald-800 mb-3">🎯 Temi Principali</h4>
            <div className="space-y-3">
              {safeSemantics.themes.slice(0, 5).map((theme, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-emerald-50 rounded-lg">
                  <div className="flex-1">
                    <h5 className="font-medium text-emerald-800">{theme.name}</h5>
                    <div className="text-sm text-emerald-600 mt-1">
                      Esempi: {theme.examples.join(', ')}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-emerald-600">{theme.frequency}</div>
                    <div className="text-xs text-gray-600">occorrenze</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Analisi coerenza */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg border border-emerald-100">
            <h4 className="font-semibold text-emerald-800 mb-3">🔗 Coerenza Semantica</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Livello coerenza:</span>
                <span className={`font-medium ${
                  safeSemantics.coherence >= 70 ? 'text-green-600' :
                  safeSemantics.coherence >= 50 ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {safeSemantics.coherence}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    safeSemantics.coherence >= 70 ? 'bg-green-500' :
                    safeSemantics.coherence >= 50 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                  style={{ width: `${safeSemantics.coherence}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-600">
                {safeSemantics.coherence >= 70 ? 'Ottima coerenza tematica' :
                 safeSemantics.coherence >= 50 ? 'Coerenza accettabile' :
                 'Migliorare la coerenza tra le frasi'}
              </p>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-emerald-100">
            <h4 className="font-semibold text-emerald-800 mb-3">💎 Densità Concettuale</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Densità concetti:</span>
                <span className={`font-medium ${
                  safeSemantics.conceptDensity >= 60 ? 'text-green-600' :
                  safeSemantics.conceptDensity >= 40 ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {safeSemantics.conceptDensity}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    safeSemantics.conceptDensity >= 60 ? 'bg-green-500' :
                    safeSemantics.conceptDensity >= 40 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                  style={{ width: `${safeSemantics.conceptDensity}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-600">
                {safeSemantics.conceptDensity >= 60 ? 'Ricco di concetti significativi' :
                 safeSemantics.conceptDensity >= 40 ? 'Buon equilibrio concettuale' :
                 'Considerare più concetti specifici'}
              </p>
            </div>
          </div>
        </div>

        {/* Consigli semantici */}
        <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-200 mt-4">
          <h4 className="font-semibold text-emerald-800 mb-2">💡 Consigli Semantici</h4>
          <div className="text-sm text-emerald-700 space-y-1">
            {safeSemantics.coherence < 50 && (
              <p>• Coerenza bassa: collega meglio i concetti tra le frasi consecutive</p>
            )}
            {safeSemantics.conceptDensity < 40 && (
              <p>• Densità concettuale bassa: aggiungi termini più specifici e significativi</p>
            )}
            {safeSemantics.themes.length === 0 && (
              <p>• Nessun tema identificato: sviluppa tematiche più chiare e ricorrenti</p>
            )}
            {safeSemantics.categories.emotions === 0 && (
              <p>• Mancano elementi emotivi: considera di aggiungere sfumature emotive</p>
            )}
            {Object.values(safeSemantics.categories).every(count => count < 2) && (
              <p>• Varietà semantica limitata: esplora diverse categorie concettuali</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SemanticTab;
