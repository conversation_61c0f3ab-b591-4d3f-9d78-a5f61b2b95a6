import { Zap } from 'lucide-react';
import { useScoreUtils } from '../../hooks/useScoreUtils';

const LiteraryTab = ({ text, analysis }) => {
  const { getScoreColor, getScoreBarColor } = useScoreUtils();
  // Usa l'analisi già calcolata se disponibile
  const getLiteraryData = () => {
    if (analysis?.literaryMetrics) {
      return analysis.literaryMetrics;
    }

    // Fallback: calcola se non disponibile
    return analyzeLiteraryMetricsFallback(text);
  };

  // Analisi metriche letterarie avanzate (fallback)
  const analyzeLiteraryMetricsFallback = (text) => {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.toLowerCase().split(/\s+/).filter(w => w.length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    
    // Analisi ritmo prosodico
    const rhythm = analyzeRhythm(sentences);
    
    // Analisi figure retoriche
    const rhetoric = analyzeRhetoric(text);
    
    // Analisi registro stilistico
    const register = analyzeRegister(words);
    
    // Analisi complessità narrativa
    const complexity = analyzeComplexity(sentences, words);
    
    // Analisi musicalità
    const musicality = analyzeMusicality(text);
    
    // Analisi originalità
    const originality = analyzeOriginality(words);
    
    return {
      rhythm,
      rhetoric,
      register,
      complexity,
      musicality,
      originality,
      totalWords: words.length,
      totalSentences: sentences.length,
      totalParagraphs: paragraphs.length
    };
  };

  const analyzeRhythm = (sentences) => {
    const lengths = sentences.map(s => s.trim().split(/\s+/).length);
    const avgLength = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    const variation = Math.sqrt(lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length);
    
    // Analisi pattern ritmici
    const shortSentences = lengths.filter(len => len <= 8).length;
    const mediumSentences = lengths.filter(len => len > 8 && len <= 20).length;
    const longSentences = lengths.filter(len => len > 20).length;
    
    const rhythmScore = Math.min(100, variation * 3); // Più variazione = ritmo migliore
    
    return {
      avgLength: Math.round(avgLength * 10) / 10,
      variation: Math.round(variation * 10) / 10,
      score: Math.round(rhythmScore),
      distribution: { short: shortSentences, medium: mediumSentences, long: longSentences }
    };
  };

  const analyzeRhetoric = (text) => {
    const figures = {
      metaphors: (text.match(/\b(come|simile a|sembrava|pareva|era come|quale|così come)\b/gi) || []).length,
      alliterations: findAlliterations(text),
      anaphora: findAnaphora(text),
      chiasmus: findChiasmus(text),
      hyperbole: (text.match(/\b(infinito|eterno|immenso|gigantesco|minuscolo|mai|sempre|tutto|niente)\b/gi) || []).length,
      irony: (text.match(/\b(ovviamente|naturalmente|chiaramente|evidentemente)\b/gi) || []).length
    };
    
    const totalFigures = Object.values(figures).reduce((sum, count) => sum + count, 0);
    const density = Math.round((totalFigures / text.split(/\s+/).length) * 1000); // Per mille parole
    
    return { ...figures, total: totalFigures, density };
  };

  const findAlliterations = (text) => {
    const words = text.toLowerCase().match(/\b[a-z]+\b/g) || [];
    let count = 0;
    
    for (let i = 0; i < words.length - 2; i++) {
      if (words[i][0] === words[i + 1][0] && words[i][0] === words[i + 2][0]) {
        count++;
      }
    }
    
    return count;
  };

  const findAnaphora = (text) => {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    let count = 0;
    
    for (let i = 0; i < sentences.length - 1; i++) {
      const words1 = sentences[i].trim().split(/\s+/);
      const words2 = sentences[i + 1].trim().split(/\s+/);
      
      if (words1.length > 0 && words2.length > 0 && 
          words1[0].toLowerCase() === words2[0].toLowerCase()) {
        count++;
      }
    }
    
    return count;
  };

  const findChiasmus = (text) => {
    // Semplificata: cerca pattern ABBA nelle parole
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    let count = 0;
    
    sentences.forEach(sentence => {
      const words = sentence.toLowerCase().split(/\s+/).filter(w => w.length > 3);
      for (let i = 0; i < words.length - 3; i++) {
        if (words[i] === words[i + 3] && words[i + 1] === words[i + 2]) {
          count++;
        }
      }
    });
    
    return count;
  };

  const analyzeRegister = (words) => {
    const formal = words.filter(w => 
      ['tuttavia', 'pertanto', 'inoltre', 'dunque', 'infatti', 'ovvero', 'ossia', 'altresì', 'nondimeno'].includes(w)
    ).length;
    
    const literary = words.filter(w => 
      ['anima', 'spirito', 'destino', 'eternità', 'infinito', 'sublime', 'ineffabile', 'struggente'].includes(w)
    ).length;
    
    const colloquial = words.filter(w => 
      ['tipo', 'roba', 'cosa', 'boh', 'mah', 'ecco', 'allora', 'insomma'].includes(w)
    ).length;
    
    const archaic = words.filter(w => 
      ['ove', 'onde', 'siccome', 'perocché', 'conciosiacché', 'dappoiché'].includes(w)
    ).length;
    
    const total = formal + literary + colloquial + archaic;
    
    return {
      formal: Math.round((formal / words.length) * 1000),
      literary: Math.round((literary / words.length) * 1000),
      colloquial: Math.round((colloquial / words.length) * 1000),
      archaic: Math.round((archaic / words.length) * 1000),
      score: Math.round(((formal + literary + archaic) / Math.max(1, total)) * 100)
    };
  };

  const analyzeComplexity = (sentences, words) => {
    const avgWordsPerSentence = words.length / sentences.length;
    const complexSentences = sentences.filter(s => 
      s.includes(',') || s.includes(';') || s.includes(':') || 
      s.includes('che') || s.includes('quando') || s.includes('mentre')
    ).length;
    
    const subordinateRatio = complexSentences / sentences.length;
    const lexicalComplexity = words.filter(w => w.length > 6).length / words.length;
    
    const complexityScore = Math.round(
      (avgWordsPerSentence / 20 * 30) + 
      (subordinateRatio * 40) + 
      (lexicalComplexity * 30)
    );
    
    return {
      avgWordsPerSentence: Math.round(avgWordsPerSentence * 10) / 10,
      subordinateRatio: Math.round(subordinateRatio * 100),
      lexicalComplexity: Math.round(lexicalComplexity * 100),
      score: Math.min(100, complexityScore)
    };
  };

  const analyzeMusicality = (text) => {
    const vowels = (text.match(/[aeiouàèéìíîòóù]/gi) || []).length;
    const consonants = (text.match(/[bcdfghjklmnpqrstvwxyz]/gi) || []).length;
    const vowelRatio = vowels / (vowels + consonants);
    
    // Analisi rime e assonanze (semplificata)
    const words = text.toLowerCase().match(/\b[a-z]+\b/g) || [];
    const endings = words.map(w => w.slice(-2));
    const rhymes = endings.filter((ending, index) => 
      endings.indexOf(ending) !== index
    ).length;
    
    const musicalityScore = Math.round(
      (vowelRatio * 50) + 
      (rhymes / words.length * 500)
    );
    
    return {
      vowelRatio: Math.round(vowelRatio * 100),
      rhymes,
      score: Math.min(100, musicalityScore)
    };
  };

  const analyzeOriginality = (words) => {
    const commonWords = ['il', 'la', 'di', 'che', 'e', 'a', 'un', 'per', 'in', 'con', 'non', 'una', 'su', 'le', 'da', 'si', 'come', 'più', 'ma', 'se'];
    const uncommonWords = words.filter(w => !commonWords.includes(w) && w.length > 4);
    const uniqueWords = new Set(uncommonWords);
    
    const originalityScore = Math.round((uniqueWords.size / words.length) * 100);
    
    return {
      uncommonWords: uncommonWords.length,
      uniqueWords: uniqueWords.size,
      score: originalityScore
    };
  };

  const metrics = getLiteraryData();

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-amber-50 to-orange-50 p-4 rounded-lg border border-amber-200">
        <h3 className="text-xl font-bold text-amber-800 mb-2 flex items-center gap-2">
          <Zap className="w-6 h-6" />
          Metriche Letterarie Avanzate
        </h3>
        <p className="text-amber-700 mb-4 text-sm">
          Analisi approfondita di ritmo, figure retoriche, registro e complessità stilistica
        </p>

        {/* Punteggi principali */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          <div className="bg-white p-3 rounded-lg border border-amber-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(metrics.rhythm.score)}`}>
              {metrics.rhythm.score}%
            </div>
            <div className="text-xs text-gray-600">Ritmo</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-orange-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(metrics.complexity.score)}`}>
              {metrics.complexity.score}%
            </div>
            <div className="text-xs text-gray-600">Complessità</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-yellow-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(metrics.musicality.score)}`}>
              {metrics.musicality.score}%
            </div>
            <div className="text-xs text-gray-600">Musicalità</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-red-100 text-center">
            <div className={`text-2xl font-bold ${getScoreColor(metrics.originality.score)}`}>
              {metrics.originality.score}%
            </div>
            <div className="text-xs text-gray-600">Originalità</div>
          </div>
        </div>

        {/* Analisi dettagliata */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Ritmo prosodico */}
          <div className="bg-white p-4 rounded-lg border border-amber-100">
            <h4 className="font-semibold text-amber-800 mb-3">🎵 Ritmo Prosodico</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Lunghezza media:</span>
                <span className="font-medium">{metrics.rhythm.avgLength} parole</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Variazione:</span>
                <span className="font-medium">{metrics.rhythm.variation}</span>
              </div>
              <div className="text-xs text-gray-600 mt-2">
                Brevi: {metrics.rhythm.distribution.short} • 
                Medie: {metrics.rhythm.distribution.medium} • 
                Lunghe: {metrics.rhythm.distribution.long}
              </div>
            </div>
          </div>

          {/* Figure retoriche */}
          <div className="bg-white p-4 rounded-lg border border-orange-100">
            <h4 className="font-semibold text-orange-800 mb-3">🎭 Figure Retoriche</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Metafore:</span>
                <span className="font-medium">{metrics.rhetoric.metaphors}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Allitterazioni:</span>
                <span className="font-medium">{metrics.rhetoric.alliterations}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Anafore:</span>
                <span className="font-medium">{metrics.rhetoric.anaphora}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Densità:</span>
                <span className="font-medium">{metrics.rhetoric.density}‰</span>
              </div>
            </div>
          </div>

          {/* Registro stilistico */}
          <div className="bg-white p-4 rounded-lg border border-yellow-100">
            <h4 className="font-semibold text-yellow-800 mb-3">📚 Registro Stilistico</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Formale:</span>
                <span className="font-medium">{metrics.register.formal}‰</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Letterario:</span>
                <span className="font-medium">{metrics.register.literary}‰</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Colloquiale:</span>
                <span className="font-medium">{metrics.register.colloquial}‰</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Punteggio:</span>
                <span className={`font-medium ${getScoreColor(metrics.register.score)}`}>
                  {metrics.register.score}%
                </span>
              </div>
            </div>
          </div>

          {/* Complessità narrativa */}
          <div className="bg-white p-4 rounded-lg border border-red-100">
            <h4 className="font-semibold text-red-800 mb-3">🧩 Complessità Narrativa</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Subordinate:</span>
                <span className="font-medium">{metrics.complexity.subordinateRatio}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Lessico complesso:</span>
                <span className="font-medium">{metrics.complexity.lexicalComplexity}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className={`h-2 rounded-full ${getScoreBarColor(metrics.complexity.score)}`}
                  style={{ width: `${metrics.complexity.score}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Analisi musicalità e originalità */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div className="bg-white p-4 rounded-lg border border-purple-100">
            <h4 className="font-semibold text-purple-800 mb-3">🎼 Musicalità</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Rapporto vocali:</span>
                <span className="font-medium">{metrics.musicality.vowelRatio}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Rime/Assonanze:</span>
                <span className="font-medium">{metrics.musicality.rhymes}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className={`h-2 rounded-full ${getScoreBarColor(metrics.musicality.score)}`}
                  style={{ width: `${metrics.musicality.score}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-indigo-100">
            <h4 className="font-semibold text-indigo-800 mb-3">✨ Originalità</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Parole uniche:</span>
                <span className="font-medium">{metrics.originality.uniqueWords}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Parole ricercate:</span>
                <span className="font-medium">{metrics.originality.uncommonWords}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className={`h-2 rounded-full ${getScoreBarColor(metrics.originality.score)}`}
                  style={{ width: `${metrics.originality.score}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Consigli letterari */}
        <div className="bg-amber-50 p-4 rounded-lg border border-amber-200 mt-4">
          <h4 className="font-semibold text-amber-800 mb-2">💡 Consigli Letterari</h4>
          <div className="text-sm text-amber-700 space-y-1">
            {metrics.rhythm.score < 50 && (
              <p>• Ritmo monotono: varia la lunghezza delle frasi per creare dinamismo</p>
            )}
            {metrics.rhetoric.total === 0 && (
              <p>• Nessuna figura retorica: aggiungi metafore o similitudini per arricchire il testo</p>
            )}
            {metrics.complexity.score < 40 && (
              <p>• Complessità bassa: considera frasi più articolate e lessico più ricercato</p>
            )}
            {metrics.musicality.score < 30 && (
              <p>• Musicalità limitata: lavora sul suono delle parole e il ritmo</p>
            )}
            {metrics.originality.score < 30 && (
              <p>• Originalità bassa: usa termini meno comuni e costruzioni più creative</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiteraryTab;
