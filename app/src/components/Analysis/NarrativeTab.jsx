import { Film } from 'lucide-react';

const NarrativeTab = ({ text }) => {
  // Analisi struttura narrativa
  const analyzeNarrativeStructure = (text) => {
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    // Identificazione scene (paragrafi con separatori o cambio significativo)
    const scenes = [];
    let currentScene = { start: 0, paragraphs: [], type: 'narrative' };
    
    paragraphs.forEach((paragraph, index) => {
      const trimmed = paragraph.trim();
      
      // Separatori di scena
      if (trimmed.match(/^[•\-*]{3,}$/) || trimmed.match(/^\s*\*\s*\*\s*\*\s*$/)) {
        if (currentScene.paragraphs.length > 0) {
          scenes.push({ ...currentScene, end: index });
          currentScene = { start: index + 1, paragraphs: [], type: 'narrative' };
        }
      } else {
        // Analisi tipo di contenuto del paragrafo
        const dialogueRatio = (trimmed.match(/"/g) || []).length / trimmed.length;
        const actionWords = trimmed.match(/\b(corse|saltò|gridò|afferrò|spinse|tirò|colpì)\b/gi) || [];
        const descriptiveWords = trimmed.match(/\b(bello|grande|piccolo|rosso|blu|alto|basso|luminoso|scuro)\b/gi) || [];
        
        let sceneType = 'narrative';
        if (dialogueRatio > 0.1) sceneType = 'dialogue';
        else if (actionWords.length > 2) sceneType = 'action';
        else if (descriptiveWords.length > 3) sceneType = 'descriptive';
        
        currentScene.paragraphs.push({
          text: trimmed,
          type: sceneType,
          wordCount: trimmed.split(/\s+/).length
        });
        currentScene.type = sceneType;
      }
    });
    
    if (currentScene.paragraphs.length > 0) {
      scenes.push({ ...currentScene, end: paragraphs.length });
    }
    
    // Analisi ritmo narrativo
    const rhythm = sentences.map(sentence => {
      const words = sentence.trim().split(/\s+/).length;
      return {
        text: sentence.trim(),
        length: words,
        pace: words < 8 ? 'fast' : words > 20 ? 'slow' : 'medium'
      };
    });
    
    // Analisi tensione (basata su parole chiave e punteggiatura)
    const tensionWords = ['improvvisamente', 'all\'improvviso', 'gridò', 'urlò', 'corse', 'paura', 'terrore', 'pericolo'];
    const tension = sentences.map(sentence => {
      const tensionCount = tensionWords.filter(word => 
        sentence.toLowerCase().includes(word.toLowerCase())
      ).length;
      const exclamations = (sentence.match(/!/g) || []).length;
      const questions = (sentence.match(/\?/g) || []).length;
      
      return {
        text: sentence.trim(),
        level: tensionCount + exclamations + questions,
        type: tensionCount > 0 ? 'high' : exclamations > 0 ? 'medium' : 'low'
      };
    });
    
    return { scenes, rhythm, tension, paragraphs: paragraphs.length, sentences: sentences.length };
  };

  const structure = analyzeNarrativeStructure(text);

  const getSceneColor = (type) => {
    switch (type) {
      case 'dialogue': return 'bg-blue-100 border-blue-300';
      case 'action': return 'bg-red-100 border-red-300';
      case 'descriptive': return 'bg-green-100 border-green-300';
      default: return 'bg-gray-100 border-gray-300';
    }
  };

  const getPaceColor = (pace) => {
    switch (pace) {
      case 'fast': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'slow': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getTensionColor = (type) => {
    switch (type) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-orange-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200">
        <h3 className="text-xl font-bold text-purple-800 mb-2 flex items-center gap-2">
          <Film className="w-6 h-6" />
          Analisi Struttura Narrativa
        </h3>
        <p className="text-purple-700 mb-4 text-sm">
          Analisi del ritmo, delle scene e della tensione narrativa del tuo testo
        </p>

        {/* Statistiche generali */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          <div className="bg-white p-3 rounded-lg border border-purple-100 text-center">
            <div className="text-2xl font-bold text-purple-600">{structure.scenes.length}</div>
            <div className="text-xs text-gray-600">Scene</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-blue-100 text-center">
            <div className="text-2xl font-bold text-blue-600">{structure.paragraphs}</div>
            <div className="text-xs text-gray-600">Paragrafi</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-green-100 text-center">
            <div className="text-2xl font-bold text-green-600">{structure.sentences}</div>
            <div className="text-xs text-gray-600">Frasi</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-orange-100 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {Math.round(structure.sentences / structure.scenes.length) || 0}
            </div>
            <div className="text-xs text-gray-600">Frasi/Scena</div>
          </div>
        </div>

        {/* Analisi scene */}
        <div className="bg-white p-4 rounded-lg border border-purple-100 mb-4">
          <h4 className="font-semibold text-purple-800 mb-3">🎬 Struttura Scene</h4>
          <div className="space-y-3">
            {structure.scenes.map((scene, index) => (
              <div key={index} className={`p-3 rounded-lg border-l-4 ${getSceneColor(scene.type)}`}>
                <div className="flex justify-between items-start mb-2">
                  <h5 className="font-medium">Scena {index + 1}</h5>
                  <span className="text-xs bg-white px-2 py-1 rounded border">
                    {scene.type === 'dialogue' ? '💬 Dialogo' :
                     scene.type === 'action' ? '🏃 Azione' :
                     scene.type === 'descriptive' ? '🎨 Descrittiva' :
                     '📝 Narrativa'}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {scene.paragraphs.length} paragrafi • {scene.paragraphs.reduce((sum, p) => sum + p.wordCount, 0)} parole
                </div>
                {scene.paragraphs.length > 0 && (
                  <div className="mt-2 text-xs text-gray-500">
                    "{scene.paragraphs[0].text.substring(0, 100)}..."
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Analisi ritmo */}
        <div className="bg-white p-4 rounded-lg border border-purple-100 mb-4">
          <h4 className="font-semibold text-purple-800 mb-3">⚡ Ritmo Narrativo</h4>
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <span>Veloce (1-7 parole):</span>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-red-500 h-2 rounded-full"
                  style={{ width: `${(structure.rhythm.filter(r => r.pace === 'fast').length / structure.rhythm.length) * 100}%` }}
                ></div>
              </div>
              <span className="text-xs">{structure.rhythm.filter(r => r.pace === 'fast').length}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span>Medio (8-20 parole):</span>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-yellow-500 h-2 rounded-full"
                  style={{ width: `${(structure.rhythm.filter(r => r.pace === 'medium').length / structure.rhythm.length) * 100}%` }}
                ></div>
              </div>
              <span className="text-xs">{structure.rhythm.filter(r => r.pace === 'medium').length}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span>Lento (21+ parole):</span>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full"
                  style={{ width: `${(structure.rhythm.filter(r => r.pace === 'slow').length / structure.rhythm.length) * 100}%` }}
                ></div>
              </div>
              <span className="text-xs">{structure.rhythm.filter(r => r.pace === 'slow').length}</span>
            </div>
          </div>
        </div>

        {/* Analisi tensione */}
        <div className="bg-white p-4 rounded-lg border border-purple-100">
          <h4 className="font-semibold text-purple-800 mb-3">🔥 Livelli di Tensione</h4>
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <span>Alta tensione:</span>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-red-500 h-2 rounded-full"
                  style={{ width: `${(structure.tension.filter(t => t.type === 'high').length / structure.tension.length) * 100}%` }}
                ></div>
              </div>
              <span className="text-xs">{structure.tension.filter(t => t.type === 'high').length}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span>Media tensione:</span>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-orange-500 h-2 rounded-full"
                  style={{ width: `${(structure.tension.filter(t => t.type === 'medium').length / structure.tension.length) * 100}%` }}
                ></div>
              </div>
              <span className="text-xs">{structure.tension.filter(t => t.type === 'medium').length}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span>Bassa tensione:</span>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${(structure.tension.filter(t => t.type === 'low').length / structure.tension.length) * 100}%` }}
                ></div>
              </div>
              <span className="text-xs">{structure.tension.filter(t => t.type === 'low').length}</span>
            </div>
          </div>
        </div>

        {/* Consigli strutturali */}
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200 mt-4">
          <h4 className="font-semibold text-purple-800 mb-2">💡 Consigli Strutturali</h4>
          <div className="text-sm text-purple-700 space-y-1">
            {structure.scenes.length < 3 && (
              <p>• Considera di dividere il testo in più scene per migliorare il ritmo</p>
            )}
            {structure.rhythm.filter(r => r.pace === 'slow').length > structure.rhythm.length * 0.7 && (
              <p>• Molte frasi lunghe: prova ad alternare con frasi più brevi per variare il ritmo</p>
            )}
            {structure.tension.filter(t => t.type === 'high').length === 0 && (
              <p>• Considera di aggiungere momenti di maggiore tensione per coinvolgere il lettore</p>
            )}
            {structure.scenes.filter(s => s.type === 'dialogue').length === 0 && (
              <p>• Aggiungi dialoghi per rendere la narrazione più dinamica</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NarrativeTab;
