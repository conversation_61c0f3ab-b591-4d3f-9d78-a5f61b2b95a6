import React, { useState, useEffect } from 'react';
import { X, Save, Trash2 } from 'lucide-react';

const EditChapterModal = ({ 
  chapter, 
  isOpen, 
  onClose, 
  onSave, 
  onDelete 
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    position: 0
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (chapter) {
      setFormData({
        title: chapter.title || '',
        description: chapter.description || '',
        position: chapter.position || 0
      });
    }
  }, [chapter]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      await onSave(chapter.id, formData);
      onClose();
    } catch (error) {
      console.error('Errore nel salvataggio:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm(`Sei sicuro di voler eliminare il capitolo "${chapter.title}"?\n\nQuesta azione eliminerà anche tutti i sottocapitoli e non può essere annullata.`)) {
      setIsLoading(true);
      
      try {
        await onDelete(chapter.id);
        onClose();
      } catch (error) {
        console.error('Errore nell\'eliminazione:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (!isOpen || !chapter) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Modifica Capitolo
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Titolo */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Titolo *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
              disabled={isLoading}
            />
          </div>

          {/* Descrizione */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Descrizione
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />
          </div>

          {/* Posizione */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Posizione
            </label>
            <input
              type="number"
              value={formData.position}
              onChange={(e) => setFormData({ ...formData, position: parseInt(e.target.value) || 0 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />
          </div>

          {/* Buttons */}
          <div className={`flex ${onDelete ? 'justify-between' : 'justify-end'} pt-4`}>
            {/* Delete Button - Solo se onDelete è fornito */}
            {onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                disabled={isLoading}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded-md flex items-center gap-2 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                Elimina
              </button>
            )}

            {/* Save Button */}
            <div className="flex gap-2">
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="px-4 py-2 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 rounded-md transition-colors"
              >
                Annulla
              </button>
              <button
                type="submit"
                disabled={isLoading || !formData.title.trim()}
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-md flex items-center gap-2 transition-colors"
              >
                <Save className="w-4 h-4" />
                {isLoading ? 'Salvando...' : 'Salva'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditChapterModal;
