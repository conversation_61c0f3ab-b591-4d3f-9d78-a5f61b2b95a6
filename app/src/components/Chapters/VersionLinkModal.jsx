import React, { useState } from 'react';
import { Link, X, FileText, Calendar, BarChart3, Unlink } from 'lucide-react';

const VersionLinkModal = ({ 
  isOpen, 
  onClose, 
  chapter,
  subchapter,
  projects,
  onLinkVersion,
  onUnlinkVersion
}) => {
  const [selectedVersion, setSelectedVersion] = useState('');

  if (!isOpen || !chapter || !subchapter) return null;

  // Estrai tutte le versioni disponibili dai progetti
  const availableVersions = [];

  projects.forEach(project => {
    project.versions?.forEach(version => {
      availableVersions.push({
        id: `${project.id}|${version.id}`,
        projectId: project.id,
        projectTitle: project.title || project.name, // Supporta entrambi i formati
        versionId: version.id,
        versionName: version.name || `Versione ${new Date(version.timestamp).toLocaleDateString()}`,
        text: version.text || '',
        wordCount: version.word_count || version.wordCount || 0,
        timestamp: version.timestamp,
        scores: version.scores
      });
    });
  });

  const selectedVersionData = availableVersions.find(v => v.id === selectedVersion);
  const currentLink = subchapter.liveVersion?.sourceLink;

  const handleConfirm = () => {
    if (selectedVersionData) {
      onLinkVersion(
        chapter.id,
        subchapter.id,
        selectedVersionData.projectId,
        selectedVersionData.versionId,
        selectedVersionData
      );
    }
    onClose();
    setSelectedVersion('');
  };

  const handleUnlink = () => {
    onUnlinkVersion(chapter.id, subchapter.id);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <Link className="w-6 h-6 text-blue-600" />
            Collega Versione a Sottocapitolo
          </h2>
          <button onClick={() => { onClose(); setSelectedVersion(''); }} className="text-gray-400 hover:text-gray-600">
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Sottocapitolo di destinazione */}
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-medium text-blue-800 mb-2">Sottocapitolo di Destinazione</h3>
            <div className="text-sm text-blue-700">
              <div><strong>Capitolo:</strong> {chapter.title}</div>
              <div><strong>Sottocapitolo:</strong> {subchapter.title}</div>
              <div><strong>Parole attuali:</strong> {subchapter.liveVersion?.wordCount || 0}</div>
            </div>
          </div>

          {/* Collegamento attuale */}
          {currentLink && (
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-green-800 mb-2">Collegamento Attuale</h3>
                  <div className="text-sm text-green-700">
                    <div><strong>Progetto:</strong> {currentLink.projectTitle}</div>
                    <div><strong>Versione:</strong> {currentLink.versionName}</div>
                    <div><strong>Collegato il:</strong> {new Date(currentLink.linkedAt).toLocaleDateString()}</div>
                  </div>
                </div>
                <button
                  onClick={handleUnlink}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded flex items-center gap-2"
                >
                  <Unlink className="w-4 h-4" />
                  Scollega
                </button>
              </div>
            </div>
          )}

          {/* Selezione nuova versione */}
          <div>
            <h3 className="font-medium text-gray-800 mb-3">
              {currentLink ? 'Cambia Collegamento' : 'Seleziona Versione da Collegare'}
            </h3>
            {availableVersions.length > 0 ? (
              <div className="space-y-2 max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                {availableVersions.map((version) => {
                  const isCurrentlyLinked = currentLink && 
                    currentLink.projectId === version.projectId && 
                    currentLink.versionId === version.versionId;
                  
                  return (
                    <label
                      key={version.id}
                      className={`flex items-center p-3 cursor-pointer border-b border-gray-100 last:border-b-0 ${
                        selectedVersion === version.id 
                          ? 'bg-blue-50 border-blue-200' 
                          : isCurrentlyLinked
                          ? 'bg-green-50 border-green-200'
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      <input
                        type="radio"
                        name="version"
                        value={version.id}
                        checked={selectedVersion === version.id}
                        onChange={(e) => setSelectedVersion(e.target.value)}
                        className="mr-3"
                        disabled={isCurrentlyLinked}
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <div className="font-medium text-gray-800">{version.projectTitle}</div>
                          {isCurrentlyLinked && (
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                              Attualmente collegato
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">{version.versionName}</div>
                        <div className="text-xs text-gray-500 flex items-center gap-4 mt-1">
                          <span className="flex items-center gap-1">
                            <FileText className="w-3 h-3" />
                            {version.wordCount} parole
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(version.timestamp).toLocaleDateString()}
                          </span>
                          {version.scores && (
                            <span className="flex items-center gap-1">
                              <BarChart3 className="w-3 h-3" />
                              {version.scores.overall}/100
                            </span>
                          )}
                        </div>
                      </div>
                    </label>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
                <FileText className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                <p>Nessuna versione disponibile</p>
                <p className="text-sm">Crea prima alcuni progetti con versioni</p>
              </div>
            )}
          </div>

          {/* Anteprima versione selezionata */}
          {selectedVersionData && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-800 mb-2">Anteprima Versione Selezionata</h4>
              <div className="text-sm text-blue-700 mb-2">
                <strong>{selectedVersionData.projectTitle}</strong> → {selectedVersionData.versionName}
              </div>
              <div className="text-xs text-gray-600 max-h-20 overflow-y-auto bg-white p-2 rounded border">
                {selectedVersionData.text ? 
                  selectedVersionData.text.substring(0, 200) + (selectedVersionData.text.length > 200 ? '...' : '') :
                  'Nessun contenuto'
                }
              </div>
              <div className="mt-2 text-xs text-blue-600">
                ⚠️ Il contenuto del sottocapitolo verrà sostituito con questa versione
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={() => { onClose(); setSelectedVersion(''); }}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Annulla
          </button>
          <button
            onClick={handleConfirm}
            disabled={!selectedVersionData}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg flex items-center gap-2"
          >
            <Link className="w-4 h-4" />
            Collega Versione
          </button>
        </div>
      </div>
    </div>
  );
};

export default VersionLinkModal;
