import React, { useState } from 'react';
import { BookOpen, Plus, FileText, ChevronDown, ChevronRight } from 'lucide-react';

const ChapterManagerSimple = ({
  chapters,
  currentChapter,
  currentSubchapter,
  setCurrentChapter,
  setCurrentSubchapter,
  createChapter,
  createSubchapter,
  loadChapters
}) => {
  const [expandedChapters, setExpandedChapters] = useState(new Set());
  const [newChapterName, setNewChapterName] = useState('');
  const [showNewChapterForm, setShowNewChapterForm] = useState(false);

  // Toggle espansione capitolo
  const toggleChapterExpansion = (chapterId) => {
    setExpandedChapters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(chapterId)) {
        newSet.delete(chapterId);
      } else {
        newSet.add(chapterId);
      }
      return newSet;
    });
  };

  // Crea nuovo capitolo
  const handleCreateChapter = async () => {
    if (newChapterName.trim()) {
      try {
        await createChapter({
          title: newChapterName.trim(),
          description: ''
        });
        setNewChapterName('');
        setShowNewChapterForm(false);
        loadChapters();
      } catch (error) {
        console.error('Errore creazione capitolo:', error);
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Gestione Capitoli</h2>
          <p className="text-sm text-gray-600">
            {chapters?.length || 0} capitoli disponibili
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={loadChapters}
            className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors"
          >
            Aggiorna
          </button>
          
          <button
            onClick={() => setShowNewChapterForm(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded flex items-center gap-2 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Nuovo Capitolo
          </button>
        </div>
      </div>

      {/* Form nuovo capitolo */}
      {showNewChapterForm && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-3">Crea Nuovo Capitolo</h3>
          <div className="flex gap-2">
            <input
              type="text"
              value={newChapterName}
              onChange={(e) => setNewChapterName(e.target.value)}
              placeholder="Nome del capitolo..."
              className="flex-1 px-3 py-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCreateChapter();
                }
              }}
            />
            <button
              onClick={handleCreateChapter}
              disabled={!newChapterName.trim()}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded transition-colors"
            >
              Crea
            </button>
            <button
              onClick={() => {
                setShowNewChapterForm(false);
                setNewChapterName('');
              }}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
            >
              Annulla
            </button>
          </div>
        </div>
      )}

      {/* Lista capitoli */}
      <div className="space-y-4">
        {!chapters || chapters.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Nessun capitolo trovato</p>
            <p className="text-sm text-gray-500 mt-2">
              Crea il tuo primo capitolo per organizzare il contenuto
            </p>
          </div>
        ) : (
          chapters.map((chapter) => (
            <div
              key={chapter.id}
              className={`border rounded-lg p-4 ${
                currentChapter?.id === chapter.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              {/* Header capitolo */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => toggleChapterExpansion(chapter.id)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {expandedChapters.has(chapter.id) ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </button>
                  
                  <div>
                    <h3 className="font-medium text-gray-900">{chapter.title}</h3>
                    <p className="text-sm text-gray-500">
                      {chapter.subchapters?.length || 0} sottocapitoli • 
                      Creato: {new Date(chapter.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <button
                  onClick={() => setCurrentChapter(chapter)}
                  className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                  Seleziona
                </button>
              </div>

              {/* Lista sottocapitoli (se espanso) */}
              {expandedChapters.has(chapter.id) && chapter.subchapters && (
                <div className="mt-4 ml-7 space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Sottocapitoli:</h4>
                  {chapter.subchapters.length === 0 ? (
                    <p className="text-sm text-gray-500 italic">Nessun sottocapitolo</p>
                  ) : (
                    chapter.subchapters.map((subchapter) => (
                      <div
                        key={subchapter.id}
                        className={`flex items-center justify-between p-2 rounded ${
                          currentSubchapter?.id === subchapter.id
                            ? 'bg-blue-100 border border-blue-300'
                            : 'bg-gray-50 hover:bg-gray-100'
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          <FileText className="w-4 h-4 text-gray-400" />
                          <div>
                            <span className="text-sm font-medium">{subchapter.title}</span>
                            <div className="text-xs text-gray-500">
                              Creato: {new Date(subchapter.created_at).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        
                        <button
                          onClick={() => setCurrentSubchapter(subchapter)}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          Seleziona
                        </button>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Capitolo/Sottocapitolo corrente */}
      {(currentChapter || currentSubchapter) && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-green-800">
            <BookOpen className="w-5 h-5" />
            <h3 className="font-medium">Selezione Attiva</h3>
          </div>
          <p className="text-green-700 mt-1">
            {currentChapter && <span><strong>Capitolo:</strong> {currentChapter.title}</span>}
            {currentSubchapter && (
              <span className="ml-4">
                <strong>Sottocapitolo:</strong> {currentSubchapter.title}
              </span>
            )}
          </p>
        </div>
      )}
    </div>
  );
};

export default ChapterManagerSimple;
