import React, { useState } from 'react';
import { Book, Plus, X, FileText } from 'lucide-react';

const ChapterSelectionModal = ({ 
  isOpen, 
  onClose, 
  chapters, 
  onSelectChapter, 
  onCreateNewChapter,
  textPreview 
}) => {
  const [selectedChapterId, setSelectedChapterId] = useState('');
  const [selectedSubchapterId, setSelectedSubchapterId] = useState('');
  const [subchapterTitle, setSubchapterTitle] = useState('');
  const [subchapterDescription, setSubchapterDescription] = useState('');
  const [showNewChapterForm, setShowNewChapterForm] = useState(false);
  const [showNewSubchapterForm, setShowNewSubchapterForm] = useState(false);
  const [newChapterTitle, setNewChapterTitle] = useState('');
  const [newChapterDescription, setNewChapterDescription] = useState('');
  const [versionMode, setVersionMode] = useState('live'); // 'live' o 'backup'

  if (!isOpen) return null;

  const handleConfirm = () => {
    // Nuovo sottocapitolo
    if (selectedChapterId && showNewSubchapterForm && subchapterTitle.trim()) {
      onSelectChapter(selectedChapterId, {
        title: subchapterTitle.trim(),
        description: subchapterDescription.trim(),
        isNewSubchapter: true,
        versionMode
      });
      onClose();
      resetForm();
    }
    // Sottocapitolo esistente
    else if (selectedChapterId && selectedSubchapterId) {
      onSelectChapter(selectedChapterId, {
        existingSubchapterId: selectedSubchapterId,
        versionMode
      });
      onClose();
      resetForm();
    }
  };

  const handleCreateNewChapter = () => {
    if (newChapterTitle.trim() && subchapterTitle.trim()) {
      onCreateNewChapter({
        chapterTitle: newChapterTitle.trim(),
        chapterDescription: newChapterDescription.trim(),
        subchapterTitle: subchapterTitle.trim(),
        subchapterDescription: subchapterDescription.trim(),
        versionMode
      });
      onClose();
      resetForm();
    }
  };

  const resetForm = () => {
    setSelectedChapterId('');
    setSelectedSubchapterId('');
    setSubchapterTitle('');
    setSubchapterDescription('');
    setShowNewChapterForm(false);
    setShowNewSubchapterForm(false);
    setNewChapterTitle('');
    setNewChapterDescription('');
    setVersionMode('live');
  };

  const wordCount = textPreview ? textPreview.split(/\s+/).filter(word => word.length > 0).length : 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <Book className="w-6 h-6 text-blue-600" />
            Aggiungi Testo a Capitolo
          </h2>
          <button
            onClick={() => {
              onClose();
              resetForm();
            }}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Anteprima testo */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h3 className="font-medium text-gray-800 mb-2 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Anteprima Testo ({wordCount} parole)
            </h3>
            <div className="text-sm text-gray-600 max-h-32 overflow-y-auto">
              {textPreview ? textPreview.substring(0, 300) + (textPreview.length > 300 ? '...' : '') : 'Nessun testo'}
            </div>
          </div>

          {/* Modalità di versioning */}
          <div className="mb-6">
            <h3 className="font-medium text-gray-800 mb-3">Gestione Versione</h3>
            <div className="grid grid-cols-2 gap-3">
              <label className={`p-3 border rounded-lg cursor-pointer ${
                versionMode === 'live' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}>
                <input
                  type="radio"
                  name="versionMode"
                  value="live"
                  checked={versionMode === 'live'}
                  onChange={(e) => setVersionMode(e.target.value)}
                  className="sr-only"
                />
                <div className="text-sm font-medium text-blue-800">Aggiorna LIVE</div>
                <div className="text-xs text-gray-600">Sostituisce la versione corrente</div>
              </label>
              <label className={`p-3 border rounded-lg cursor-pointer ${
                versionMode === 'backup' ? 'border-green-500 bg-green-50' : 'border-gray-200'
              }`}>
                <input
                  type="radio"
                  name="versionMode"
                  value="backup"
                  checked={versionMode === 'backup'}
                  onChange={(e) => setVersionMode(e.target.value)}
                  className="sr-only"
                />
                <div className="text-sm font-medium text-green-800">Salva come Backup</div>
                <div className="text-xs text-gray-600">Mantiene LIVE + crea backup</div>
              </label>
            </div>
          </div>

          {/* Informazioni sottocapitolo */}
          {showNewSubchapterForm && (
            <div className="mb-6">
              <h3 className="font-medium text-gray-800 mb-3">Nuovo Sottocapitolo</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Titolo Sottocapitolo *
                  </label>
                  <input
                    type="text"
                    value={subchapterTitle}
                    onChange={(e) => setSubchapterTitle(e.target.value)}
                    placeholder="Es: Scena 1, Prologo, L'incontro..."
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descrizione (opzionale)
                  </label>
                  <input
                    type="text"
                    value={subchapterDescription}
                    onChange={(e) => setSubchapterDescription(e.target.value)}
                    placeholder="Breve descrizione del contenuto..."
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Selezione capitolo e sottocapitolo */}
          <div className="mb-6">
            <h3 className="font-medium text-gray-800 mb-3">Seleziona Destinazione</h3>

            {chapters.length > 0 ? (
              <div className="space-y-3">
                {chapters.map(chapter => (
                  <div key={chapter.id} className="border border-gray-200 rounded-lg">
                    {/* Header capitolo */}
                    <label
                      className={`flex items-center p-3 cursor-pointer transition-colors ${
                        selectedChapterId === chapter.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      <input
                        type="radio"
                        name="chapter"
                        value={chapter.id}
                        checked={selectedChapterId === chapter.id}
                        onChange={(e) => {
                          setSelectedChapterId(e.target.value);
                          setSelectedSubchapterId('');
                          setShowNewSubchapterForm(false);
                        }}
                        className="mr-3"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-800">{chapter.title}</div>
                        {chapter.description && (
                          <div className="text-sm text-gray-600">{chapter.description}</div>
                        )}
                        <div className="text-xs text-gray-500 mt-1">
                          {chapter.subchapters?.length || 0} sottocapitoli
                        </div>
                      </div>
                    </label>

                    {/* Sottocapitoli esistenti */}
                    {selectedChapterId === chapter.id && chapter.subchapters?.length > 0 && (
                      <div className="border-t border-gray-200 bg-gray-50 p-3">
                        <div className="text-sm font-medium text-gray-700 mb-2">Sottocapitoli esistenti:</div>
                        <div className="space-y-2">
                          {chapter.subchapters.map(subchapter => (
                            <label
                              key={subchapter.id}
                              className={`flex items-center p-2 border rounded cursor-pointer ${
                                selectedSubchapterId === subchapter.id
                                  ? 'border-green-500 bg-green-50'
                                  : 'border-gray-200 bg-white hover:bg-gray-50'
                              }`}
                            >
                              <input
                                type="radio"
                                name="subchapter"
                                value={subchapter.id}
                                checked={selectedSubchapterId === subchapter.id}
                                onChange={(e) => {
                                  setSelectedSubchapterId(e.target.value);
                                  setShowNewSubchapterForm(false);
                                }}
                                className="mr-2"
                              />
                              <div className="flex-1">
                                <div className="text-sm font-medium">{subchapter.title}</div>
                                <div className="text-xs text-gray-500">
                                  {subchapter.liveVersion?.wordCount || 0} parole •
                                  {subchapter.backupVersions?.length || 0} backup
                                </div>
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Opzione nuovo sottocapitolo */}
                    {selectedChapterId === chapter.id && (
                      <div className="border-t border-gray-200 bg-gray-50 p-3">
                        <label
                          className={`flex items-center p-2 border-2 border-dashed rounded cursor-pointer ${
                            showNewSubchapterForm
                              ? 'border-purple-400 bg-purple-50'
                              : 'border-gray-300 hover:border-purple-400'
                          }`}
                        >
                          <input
                            type="radio"
                            name="subchapter"
                            value="new"
                            checked={showNewSubchapterForm}
                            onChange={(e) => {
                              setShowNewSubchapterForm(true);
                              setSelectedSubchapterId('');
                            }}
                            className="mr-2"
                          />
                          <div className="flex items-center gap-2">
                            <Plus className="w-4 h-4 text-purple-600" />
                            <span className="text-sm font-medium text-purple-700">Crea Nuovo Sottocapitolo</span>
                          </div>
                        </label>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <Book className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                <p>Nessun capitolo esistente</p>
              </div>
            )}

            {/* Opzione nuovo capitolo */}
            <div className="mt-4">
              <button
                onClick={() => setShowNewChapterForm(!showNewChapterForm)}
                className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-400 hover:text-blue-600 transition-colors flex items-center justify-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Crea Nuovo Capitolo
              </button>

              {showNewChapterForm && (
                <div className="mt-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-800 mb-3">Nuovo Capitolo</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">
                        Titolo Capitolo *
                      </label>
                      <input
                        type="text"
                        value={newChapterTitle}
                        onChange={(e) => setNewChapterTitle(e.target.value)}
                        placeholder="Es: Capitolo 1, Parte Prima..."
                        className="w-full p-2 border border-blue-300 rounded focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">
                        Descrizione Capitolo (opzionale)
                      </label>
                      <input
                        type="text"
                        value={newChapterDescription}
                        onChange={(e) => setNewChapterDescription(e.target.value)}
                        placeholder="Descrizione del capitolo..."
                        className="w-full p-2 border border-blue-300 rounded focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={() => {
              onClose();
              resetForm();
            }}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Annulla
          </button>
          
          {showNewChapterForm ? (
            <button
              onClick={handleCreateNewChapter}
              disabled={!newChapterTitle.trim() || !subchapterTitle.trim()}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              Crea Capitolo e Sottocapitolo
            </button>
          ) : showNewSubchapterForm ? (
            <button
              onClick={handleConfirm}
              disabled={!selectedChapterId || !subchapterTitle.trim()}
              className="px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              {versionMode === 'live' ? 'Crea e Aggiorna LIVE' : 'Crea e Salva come Backup'}
            </button>
          ) : (
            <button
              onClick={handleConfirm}
              disabled={!selectedChapterId || !selectedSubchapterId}
              className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              {versionMode === 'live' ? 'Aggiorna LIVE' : 'Salva come Backup'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChapterSelectionModal;
