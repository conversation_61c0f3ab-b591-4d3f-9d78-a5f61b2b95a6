import React, { useState, useEffect } from 'react';
import { X, Save, Trash2 } from 'lucide-react';

const EditSubchapterModal = ({ 
  subchapter, 
  chapterId,
  isOpen, 
  onClose, 
  onSave, 
  onDelete 
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    text: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (subchapter) {
      setFormData({
        title: subchapter.title || '',
        description: subchapter.description || '',
        text: subchapter.liveVersion?.text || ''
      });
    }
  }, [subchapter]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      await onSave(chapterId, subchapter.id, formData);
      onClose();
    } catch (error) {
      console.error('Errore nel salvataggio:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm(`Sei sicuro di voler eliminare il sottocapitolo "${subchapter.title}"?\n\nQuesta azione non può essere annullata.`)) {
      setIsLoading(true);
      
      try {
        await onDelete(chapterId, subchapter.id);
        onClose();
      } catch (error) {
        console.error('Errore nell\'eliminazione:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (!isOpen || !subchapter) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Modifica Sottocapitolo
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Titolo */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Titolo *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
              disabled={isLoading}
            />
          </div>

          {/* Descrizione */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Descrizione
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />
          </div>

          {/* Testo */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Testo
            </label>
            <textarea
              value={formData.text}
              onChange={(e) => setFormData({ ...formData, text: e.target.value })}
              rows={15}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
              disabled={isLoading}
              placeholder="Inserisci il testo del sottocapitolo..."
            />
            <div className="text-sm text-gray-500 mt-1">
              Parole: {formData.text.split(/\s+/).filter(word => word.length > 0).length}
            </div>
          </div>

          {/* Buttons */}
          <div className={`flex ${onDelete ? 'justify-between' : 'justify-end'} pt-4 border-t`}>
            {/* Delete Button - Solo se onDelete è fornito */}
            {onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                disabled={isLoading}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded-md flex items-center gap-2 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                Elimina
              </button>
            )}

            {/* Save Button */}
            <div className="flex gap-2">
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="px-4 py-2 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 rounded-md transition-colors"
              >
                Annulla
              </button>
              <button
                type="submit"
                disabled={isLoading || !formData.title.trim()}
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-md flex items-center gap-2 transition-colors"
              >
                <Save className="w-4 h-4" />
                {isLoading ? 'Salvando...' : 'Salva'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditSubchapterModal;
