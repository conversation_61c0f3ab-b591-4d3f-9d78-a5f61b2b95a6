import React, { useState, useEffect } from 'react';
import { X, Upload, FileText, Eye, EyeOff, Settings, RefreshCw, Book, BookOpen } from 'lucide-react';

const ImportTextModal = ({ 
  isOpen, 
  onClose, 
  onImport 
}) => {
  const [text, setText] = useState('');
  const [separatorPattern, setSeparatorPattern] = useState('(\\d+)\\.(\\d+)');
  const [chapterPattern, setChapterPattern] = useState('Capitolo\\s+(\\d+)');
  const [showPreview, setShowPreview] = useState(false);
  const [parsedStructure, setParsedStructure] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [patternsChanged, setPatternsChanged] = useState(false);
  const [createZeroSubchapters, setCreateZeroSubchapters] = useState(true);

  // Esempi di pattern comuni
  const patternExamples = [
    {
      name: "Parola Capitolo + Sottocapitoli numerici (Capitolo 1, Capitolo 2 + 1.1, 1.2, 2.1)",
      chapter: "Capitolo\\s+(\\d+)",
      subchapter: "(\\d+)\\.(\\d+)"
    },
    {
      name: "Numerazione semplice + Sottocapitoli (1., 2. + 1.1, 1.2, 2.1)",
      chapter: "^(\\d+)\\.$",
      subchapter: "(\\d+)\\.(\\d+)"
    },
    {
      name: "Capitoli romani + Sottocapitoli (I., II. + I.1, I.2, II.1)",
      chapter: "^([IVX]+)\\.$",
      subchapter: "([IVX]+)\\.(\\d+)"
    },
    {
      name: "Lettere maiuscole + Sottocapitoli (A., B. + A.1, A.2, B.1)",
      chapter: "^([A-Z])\\.$",
      subchapter: "([A-Z])\\.(\\d+)"
    },
    {
      name: "Solo sottocapitoli (rileva automaticamente i capitoli da 1.1, 2.1, 3.1)",
      chapter: "^$",
      subchapter: "(\\d+)\\.(\\d+)"
    }
  ];

  // Funzione per parsare il testo
  const parseText = (inputText, chapterRegex, subchapterRegex, createZeroSubs = true) => {
    if (!inputText.trim()) return [];

    const lines = inputText.split('\n');
    const structure = [];
    let currentChapter = null;
    let currentSubchapter = null;
    let contentBuffer = [];

    console.log('🔍 === INIZIO PARSING ===');

    // Crea le regex senza flag globale per evitare problemi con lastIndex
    const chapterRe = new RegExp(`^\\s*(${chapterRegex})\\s*(.*)$`, 'i');
    const subchapterRe = new RegExp(`^\\s*(${subchapterRegex})\\s*(.*)$`, 'i');

    const saveCurrentContent = () => {
      if (contentBuffer.length > 0) {
        const content = contentBuffer.join('\n').trim();
        if (currentSubchapter) {
          currentSubchapter.content = content;
        } else if (currentChapter && currentChapter.subchapters.length === 0) {
          currentChapter.content = content;
        }
        contentBuffer = [];
      }
    };

    const createChapter = (number, title) => {
      saveCurrentContent();

      // Estrai il numero pulito dal match
      const extractNumber = (input) => {
        const match = input.toString().match(/(\d+)/);
        return match ? match[1] : input.toString().replace(/\.$/, '');
      };

      const cleanNumber = extractNumber(number);
      const chapterTitle = `Capitolo ${cleanNumber}`;

      currentChapter = {
        id: `chapter-${structure.length + 1}`,
        title: chapterTitle,
        number: number, // Mantiene il formato originale
        cleanNumber: cleanNumber, // Numero pulito per confronti (solo cifre)
        content: '',
        subchapters: [],
        hasDirectContent: false // Flag per tracciare se ha contenuto diretto
      };
      structure.push(currentChapter);
      currentSubchapter = null;
      console.log(`📖 CREATO: ${currentChapter.title} (cleanNumber: ${cleanNumber})`);
    };

    const createSubchapter = (number, title, chapterNumber) => {
      // Estrai il numero del capitolo dal sottocapitolo se non fornito
      const targetChapterNumber = chapterNumber || number.split('.')[0];

      // Verifica se dobbiamo creare un nuovo capitolo
      if (!currentChapter || currentChapter.cleanNumber !== targetChapterNumber) {
        console.log(`🆕 NUOVO CAPITOLO: ${targetChapterNumber} (per sottocapitolo)`);
        createChapter(targetChapterNumber, null);
      } else {
        console.log(`✅ USA ESISTENTE: ${currentChapter.title}`);
      }

      // IMPORTANTE: Salva il contenuto PRIMA di creare il sottocapitolo 1.0
      saveCurrentContent();

      // Se il capitolo ha contenuto diretto e questa è la prima sottosezione, crea X.0
      if (createZeroSubs && currentChapter.hasDirectContent && currentChapter.subchapters.length === 0) {
        const zeroNumber = `${currentChapter.cleanNumber}.0`;
        const zeroSubchapter = {
          id: `subchapter-${currentChapter.subchapters.length + 1}`,
          title: zeroNumber,
          number: zeroNumber,
          content: currentChapter.content
        };
        currentChapter.subchapters.push(zeroSubchapter);
        currentChapter.content = ''; // Svuota il contenuto del capitolo
        console.log(`📄 CREATO X.0: ${zeroNumber}`);
      }
      currentSubchapter = {
        id: `subchapter-${currentChapter.subchapters.length + 1}`,
        title: number,
        number: number,
        content: ''
      };
      currentChapter.subchapters.push(currentSubchapter);
      console.log(`📄 CREATO: ${currentSubchapter.number} in ${currentChapter.title}`);
    };

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (!line) {
        // Linea vuota - aggiungi al buffer se non è vuoto
        if (contentBuffer.length > 0) {
          contentBuffer.push('');
        }
        continue;
      }

      // Test per sottocapitolo PRIMA del capitolo (più specifico)
      const subchapterMatch = line.match(subchapterRe);

      if (subchapterMatch) {
        const number = subchapterMatch[1];
        const title = subchapterMatch[2].trim();
        const chapterNumber = number.includes('.') ? number.split('.')[0] : null;

        console.log(`📄 SOTTOCAPITOLO: ${number} → Capitolo ${chapterNumber}`);
        createSubchapter(number, title, chapterNumber);
        continue;
      }

      // Test per capitolo (solo se non è un sottocapitolo)
      const chapterMatch = line.match(chapterRe);

      if (chapterMatch) {
        const number = chapterMatch[1];
        const title = chapterMatch[2].trim();

        console.log(`📖 CAPITOLO: ${number}`);
        createChapter(number, title);
        continue;
      }

      // Contenuto normale
      contentBuffer.push(line);

      // Marca che il capitolo corrente ha contenuto diretto
      if (currentChapter && currentChapter.subchapters.length === 0) {
        currentChapter.hasDirectContent = true;
      }
    }

    // Salva l'ultimo contenuto
    saveCurrentContent();

    // Post-processing: crea sottocapitoli X.0 per capitoli con solo contenuto diretto
    if (createZeroSubs) {
      structure.forEach(chapter => {
        if (chapter.hasDirectContent && chapter.subchapters.length === 0 && chapter.content.trim()) {
          const zeroNumber = `${chapter.cleanNumber}.0`;
          const zeroSubchapter = {
            id: `subchapter-1`,
            title: zeroNumber,
            number: zeroNumber,
            content: chapter.content
          };
          chapter.subchapters.push(zeroSubchapter);
          chapter.content = '';
          console.log(`📄 POST X.0: ${zeroNumber} per ${chapter.title}`);
        }
      });
    }

    console.log('🎯 === STRUTTURA FINALE ===');
    structure.forEach((chapter, i) => {
      console.log(`📖 ${i + 1}. ${chapter.title} (${chapter.cleanNumber})`);
      chapter.subchapters.forEach((sub, j) => {
        console.log(`   📄 ${j + 1}. ${sub.number} - ${sub.title}`);
      });
    });
    console.log('=========================');

    return structure;
  };

  // Funzione per aggiornare la preview
  const refreshPreview = async () => {
    if (!text || !showPreview) return;

    setIsRefreshing(true);
    setPatternsChanged(false); // Reset del flag quando si aggiorna
    try {
      // Piccolo delay per mostrare l'animazione
      await new Promise(resolve => setTimeout(resolve, 300));
      const parsed = parseText(text, chapterPattern, separatorPattern, createZeroSubchapters);
      setParsedStructure(parsed);
    } catch (error) {
      console.error('Errore nel parsing:', error);
      setParsedStructure([]);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Aggiorna preview automaticamente solo quando cambia il testo o si attiva la preview
  useEffect(() => {
    if (text && showPreview) {
      refreshPreview();
    }
  }, [text, showPreview]);

  // Monitora i cambiamenti dei pattern per mostrare l'indicatore
  useEffect(() => {
    if (showPreview && parsedStructure.length > 0) {
      setPatternsChanged(true);
    }
  }, [chapterPattern, separatorPattern, createZeroSubchapters]);

  const handleImport = async () => {
    if (!text.trim()) return;

    setIsLoading(true);
    try {
      const structure = parseText(text, chapterPattern, separatorPattern, createZeroSubchapters);
      await onImport(structure);
      onClose();
      setText('');
      setParsedStructure([]);
    } catch (error) {
      console.error('Errore nell\'import:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyPattern = (pattern) => {
    setChapterPattern(pattern.chapter);
    setSeparatorPattern(pattern.subchapter);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div className="flex items-center gap-3">
            <Upload className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl font-semibold text-gray-900">
              Importa Testo con Divisione Automatica
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-140px)]">
          {/* Pannello sinistro - Input e configurazione */}
          <div className="w-1/2 p-6 border-r overflow-y-auto">
            {/* Pattern preimpostati */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Pattern Preimpostati
              </h3>
              <div className="space-y-2">
                {patternExamples.map((pattern, index) => (
                  <button
                    key={index}
                    onClick={() => applyPattern(pattern)}
                    className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg border transition-colors"
                  >
                    <div className="font-medium text-gray-900">{pattern.name}</div>
                    <div className="text-sm text-gray-600">
                      Capitoli: <code className="bg-gray-200 px-1 rounded">{pattern.chapter}</code>
                    </div>
                    <div className="text-sm text-gray-600">
                      Sottocapitoli: <code className="bg-gray-200 px-1 rounded">{pattern.subchapter}</code>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Pattern personalizzati */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Pattern Personalizzati</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pattern Capitoli (Regex)
                  </label>
                  <input
                    type="text"
                    value={chapterPattern}
                    onChange={(e) => setChapterPattern(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="es: \\d+\\."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pattern Sottocapitoli (Regex)
                  </label>
                  <input
                    type="text"
                    value={separatorPattern}
                    onChange={(e) => setSeparatorPattern(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="es: \\d+\\.\\d+"
                  />
                </div>
              </div>
            </div>

            {/* Opzioni avanzate */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Opzioni Avanzate</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700">
                      Crea Sottocapitolo X.0 Automatico
                    </label>
                    <p className="text-xs text-gray-600 mt-1">
                      Il contenuto tra "Capitolo X" e "X.1" diventa automaticamente "X.0"
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => setCreateZeroSubchapters(!createZeroSubchapters)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      createZeroSubchapters ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        createZeroSubchapters ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>

            {/* Area testo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Testo da Importare
              </label>
              <textarea
                value={text}
                onChange={(e) => setText(e.target.value)}
                rows={12}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                placeholder={`Incolla qui il tuo testo con capitoli e sottocapitoli...

Esempio:
Capitolo 1
Contenuto del primo capitolo.

1.1 Primo Sottocapitolo
Contenuto del sottocapitolo.

1.2 Secondo Sottocapitolo
Altro contenuto.

Capitolo 2
Contenuto del secondo capitolo.

2.1 Sottocapitolo del Secondo
Contenuto del sottocapitolo del secondo capitolo.`}
              />
              <div className="text-sm text-gray-500 mt-1">
                Caratteri: {text.length}
              </div>
            </div>
          </div>

          {/* Pannello destro - Preview */}
          <div className="w-1/2 p-6 overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-3">
                <h3 className="text-lg font-medium text-gray-900">Preview Struttura</h3>
                {patternsChanged && showPreview && (
                  <div className="flex items-center gap-2 px-2 py-1 bg-orange-100 text-orange-700 rounded-md text-sm">
                    <RefreshCw className="w-3 h-3" />
                    <span>Pattern modificati</span>
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                {showPreview && (
                  <button
                    onClick={refreshPreview}
                    disabled={isRefreshing || !text.trim()}
                    className={`flex items-center gap-2 px-3 py-1 rounded-md transition-colors ${
                      patternsChanged
                        ? 'bg-orange-100 hover:bg-orange-200 text-orange-700 border border-orange-300'
                        : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
                    } disabled:bg-gray-100 disabled:text-gray-400`}
                  >
                    <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    {isRefreshing ? 'Aggiornando...' : 'Aggiorna'}
                  </button>
                )}
                <button
                  onClick={() => setShowPreview(!showPreview)}
                  className="flex items-center gap-2 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  {showPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  {showPreview ? 'Nascondi' : 'Mostra'}
                </button>
              </div>
            </div>

            {patternsChanged && showPreview && parsedStructure.length > 0 && (
              <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center gap-2 text-orange-700">
                  <RefreshCw className="w-4 h-4" />
                  <span className="font-medium">Pattern modificati</span>
                </div>
                <p className="text-sm text-orange-600 mt-1">
                  I pattern sono stati modificati. Clicca "Aggiorna" per vedere la nuova struttura.
                </p>
              </div>
            )}

            {showPreview && parsedStructure.length > 0 ? (
              <div className="space-y-4">
                {parsedStructure.map((chapter, chapterIndex) => (
                  <div key={chapter.id} className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Book className="w-4 h-4 text-blue-500" />
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded">
                        CAPITOLO
                      </span>
                      <span className="font-medium text-blue-700">
                        {chapter.title}
                      </span>
                      <span className="text-sm text-gray-500">
                        ({chapter.number})
                      </span>
                    </div>
                    
                    {chapter.content && (
                      <div className="ml-6 mb-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                        {chapter.content.substring(0, 100)}
                        {chapter.content.length > 100 && '...'}
                      </div>
                    )}

                    {chapter.subchapters.map((subchapter, subIndex) => (
                      <div key={subchapter.id} className="ml-6 mt-2 border-l-2 border-green-200 pl-4">
                        <div className="flex items-center gap-2 mb-1">
                          <BookOpen className="w-3 h-3 text-green-500" />
                          <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded">
                            SOTTOCAPITOLO
                          </span>
                          <span className="font-medium text-green-700 text-sm">
                            {subchapter.title}
                          </span>
                          <span className="text-xs text-gray-500">
                            ({subchapter.number})
                          </span>
                        </div>
                        {subchapter.content && (
                          <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                            {subchapter.content.substring(0, 80)}
                            {subchapter.content.length > 80 && '...'}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ) : showPreview ? (
              <div className="text-center text-gray-500 py-8">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Nessuna struttura rilevata</p>
                <p className="text-sm">Verifica i pattern e il testo inserito</p>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <Eye className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Clicca "Mostra" per vedere la preview</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            {parsedStructure.length > 0 && (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Book className="w-4 h-4 text-blue-500" />
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded">
                    CAPITOLI
                  </span>
                  <span className="font-medium">{parsedStructure.length}</span>
                </div>
                <div className="flex items-center gap-2">
                  <BookOpen className="w-4 h-4 text-green-500" />
                  <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded">
                    SOTTOCAPITOLI
                  </span>
                  <span className="font-medium">
                    {parsedStructure.reduce((total, ch) => total + ch.subchapters.length, 0)}
                  </span>
                </div>
              </div>
            )}
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              disabled={isLoading}
              className="px-4 py-2 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 rounded-md transition-colors"
            >
              Annulla
            </button>
            <button
              onClick={handleImport}
              disabled={isLoading || !text.trim() || parsedStructure.length === 0}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-md flex items-center gap-2 transition-colors"
            >
              <Upload className="w-4 h-4" />
              {isLoading ? 'Importando...' : 'Importa Struttura'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImportTextModal;
