import React, { useState } from 'react';
import { X, AlertTriangle, Trash2 } from 'lucide-react';

const ConfirmDeleteModal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  itemType = "elemento",
  isLoading = false
}) => {
  const handleConfirm = () => {
    onConfirm();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-6 h-6 text-orange-500" />
            <h2 className="text-xl font-semibold text-gray-900">
              {title || "Conferma Eliminazione"}
            </h2>
          </div>
          <button
            onClick={onClose}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Main Message */}
          <div className="text-center">
            <p className="text-gray-800 leading-relaxed mb-4">
              {message || `Sei sicuro di voler eliminare questo ${itemType}?`}
            </p>

            {itemName && (
              <div className="p-3 bg-gray-50 rounded border">
                <p className="font-medium text-gray-900">
                  {itemType.charAt(0).toUpperCase() + itemType.slice(1)}: <span className="font-bold">"{itemName}"</span>
                </p>
              </div>
            )}
          </div>

          {/* Simple Warning */}
          <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
            <AlertTriangle className="w-4 h-4 text-orange-500" />
            <span>Questa azione non può essere annullata</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 rounded-md transition-colors"
          >
            Annulla
          </button>
          <button
            onClick={handleConfirm}
            disabled={isLoading}
            className="px-4 py-2 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded-md flex items-center gap-2 transition-colors"
          >
            <Trash2 className="w-4 h-4" />
            {isLoading ? 'Eliminando...' : 'Elimina Definitivamente'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDeleteModal;
