import React, { useState, useEffect } from 'react';
import { FileText, BookOpen, History, Save, Plus, Package, PenTool } from 'lucide-react';

// Importazioni componenti core
import ProjectsTab from './Projects/ProjectsTabSimple';
import InputTab from './TextEditor/InputTabSimple';
import ChapterManager from './Chapters/ChapterManager';
import CodexTab from '../WriterCodex/components/CodexTab';
import WriteTab from './Write/WriteTab';
import EditChapterModal from './Chapters/EditChapterModal';
import EditSubchapterModal from './Chapters/EditSubchapterModal';
import ConfirmDeleteModal from './Common/ConfirmDeleteModal';
import ImportTextModal from './Chapters/ImportTextModal';
import ExtractCodexModal from './Codex/ExtractCodexModal';

// Importazioni hooks core
import { useUnifiedProjectManager } from '../hooks/useUnifiedProjectManager';
import { useUnifiedChapterManager } from '../hooks/useUnifiedChapterManager';
import { useChapters } from '../hooks/useChapters';

const MainEditor = () => {
  // Stati core
  const [text, setText] = useState('');
  const [activeTab, setActiveTab] = useState('input');

  // Stati per le modali di modifica
  const [editChapterModal, setEditChapterModal] = useState({ isOpen: false, chapter: null });
  const [editSubchapterModal, setEditSubchapterModal] = useState({ isOpen: false, chapter: null, subchapter: null });

  // Stati per le modali di conferma eliminazione
  const [deleteChapterModal, setDeleteChapterModal] = useState({ isOpen: false, chapter: null });
  const [deleteSubchapterModal, setDeleteSubchapterModal] = useState({ isOpen: false, chapter: null, subchapter: null });
  const [isDeleting, setIsDeleting] = useState(false);

  // Stati per la modale di import
  const [importModal, setImportModal] = useState({ isOpen: false });

  // Stati per la modale di estrazione Codex
  const [extractCodexModal, setExtractCodexModal] = useState({ isOpen: false });

  // Hooks core
  const {
    projects,
    currentProject,
    currentVersionId,
    activeVersionId,
    loadProjects,
    loadProject,
    deleteProject,
    saveProject: saveProjectHook,
    createVersion,
    setActiveProject,
    setActiveVersionId
  } = useUnifiedProjectManager();

  const {
    chapters,
    currentChapter,
    currentSubchapter,
    loadChapters,
    createChapter,
    createSubchapter,
    setCurrentChapter,
    setCurrentSubchapter
  } = useUnifiedChapterManager();

  // Hook per operazioni CRUD sui capitoli
  const { updateChapter, deleteChapter, updateSubchapter, deleteSubchapter, importStructure } = useChapters();

  // Configurazione tab core
  const tabs = [
    { id: 'input', label: 'Testo', icon: FileText },
    { id: 'write', label: 'Scrivi', icon: PenTool },
    { id: 'chapters', label: 'Capitoli', icon: BookOpen },
    { id: 'projects', label: 'Progetti', icon: History },
    { id: 'codex', label: 'Codex', icon: Package }
  ];

  // Funzioni per gestire le modali
  const handleEditChapter = (chapter) => {
    setEditChapterModal({ isOpen: true, chapter });
  };

  const handleEditSubchapter = (chapter, subchapter) => {
    setEditSubchapterModal({ isOpen: true, chapter, subchapter });
  };

  const handleSaveChapter = async (chapterId, data) => {
    await updateChapter(chapterId, data);
    await loadChapters(); // Ricarica i capitoli
  };

  // Funzioni per mostrare modali di conferma eliminazione
  const handleDeleteChapter = (chapter) => {
    setDeleteChapterModal({ isOpen: true, chapter });
  };

  const handleDeleteSubchapter = (chapter, subchapter) => {
    setDeleteSubchapterModal({ isOpen: true, chapter, subchapter });
  };

  // Funzioni per eliminazione effettiva
  const confirmDeleteChapter = async () => {
    setIsDeleting(true);
    try {
      await deleteChapter(deleteChapterModal.chapter.id);
      await loadChapters(); // Ricarica i capitoli
      setDeleteChapterModal({ isOpen: false, chapter: null });
    } catch (error) {
      console.error('Errore eliminazione capitolo:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const confirmDeleteSubchapter = async () => {
    setIsDeleting(true);
    try {
      await deleteSubchapter(deleteSubchapterModal.chapter.id, deleteSubchapterModal.subchapter.id);
      await loadChapters(); // Ricarica i capitoli
      setDeleteSubchapterModal({ isOpen: false, chapter: null, subchapter: null });
    } catch (error) {
      console.error('Errore eliminazione sottocapitolo:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleSaveSubchapter = async (chapterId, subchapterId, data) => {
    await updateSubchapter(chapterId, subchapterId, data);
    await loadChapters(); // Ricarica i capitoli
  };

  // Funzione per gestire l'import
  const handleImportStructure = async (structure) => {
    await importStructure(structure);
    await loadChapters(); // Ricarica i capitoli
    await loadProjects(); // Ricarica i progetti (per vedere i nuovi progetti creati)
  };

  // Funzione per raccogliere tutto il testo dai capitoli
  const getAllChaptersText = () => {
    let allText = '';

    chapters.forEach(chapter => {
      // Aggiungi il titolo del capitolo
      allText += `\n\n=== ${chapter.title} ===\n\n`;

      // Aggiungi i sottocapitoli
      if (chapter.subchapters && chapter.subchapters.length > 0) {
        chapter.subchapters.forEach(subchapter => {
          allText += `\n--- ${subchapter.title} ---\n\n`;
          // Accedi al testo tramite liveVersion.text
          if (subchapter.liveVersion && subchapter.liveVersion.text) {
            allText += subchapter.liveVersion.text + '\n\n';
          }
        });
      }
    });

    return allText.trim();
  };

  // Caricamento iniziale
  useEffect(() => {
    loadProjects();
    loadChapters();
  }, [loadProjects, loadChapters]);

  // Funzione per ottenere la versione corrente
  const getCurrentVersion = () => {
    if (!currentProject || !activeVersionId) return null;
    return currentProject.versions?.find(v => v.id === activeVersionId) || null;
  };

  // Sincronizza testo con versione corrente
  const currentVersion = getCurrentVersion();
  useEffect(() => {
    if (activeTab === 'chapters') return;
    
    if (currentVersion && currentVersion.text !== text) {
      setText(currentVersion.text);
    }
  }, [currentVersionId, currentVersion, activeTab, text]);

  // Wrapper per saveProject semplificato
  const saveProject = () => {
    if (text.trim()) {
      saveProjectHook(text);
    }
  };

  // Handler per cambio tab
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">WriterTool</h1>
              
              {/* Progetto corrente */}
              {currentProject && (
                <div className="flex items-center space-x-2 text-sm">
                  <span className="text-gray-500">Progetto:</span>
                  <span className="font-medium text-blue-600">
                    {currentProject.title}
                    {currentProject.versions && activeVersionId && (
                      <span className="text-blue-400 ml-1">
                        v{currentProject.versions.findIndex(v => v.id === activeVersionId) + 1}
                      </span>
                    )}
                  </span>
                </div>
              )}
            </div>

            {/* Azioni rapide */}
            <div className="flex items-center space-x-2">
              {currentProject && (
                <>
                  <button
                    onClick={saveProject}
                    disabled={!text.trim()}
                    className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-xs flex items-center gap-1 transition-colors"
                    title="Salva versione corrente"
                  >
                    <Save className="w-3 h-3" />
                    Salva
                  </button>
                  
                  <button
                    onClick={() => {
                      if (currentProject && text.trim()) {
                        createVersion(currentProject.id, text);
                      }
                    }}
                    disabled={!currentProject || !text.trim()}
                    className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-xs flex items-center gap-1 transition-colors"
                    title="Crea nuova versione"
                  >
                    <Plus className="w-3 h-3" />
                    Nuova
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content Area */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Tab Content */}
            {activeTab === 'input' && (
              <InputTab
                text={text}
                setText={setText}
                currentProject={currentProject}
                saveProject={saveProject}
                activeVersionId={activeVersionId}
                setActiveVersionId={setActiveVersionId}
                onExtractCodex={() => {
                  const chaptersText = getAllChaptersText();
                  const combinedText = text.trim() + '\n\n' + chaptersText;
                  setExtractCodexModal({ isOpen: true, text: combinedText });
                }}
              />
            )}

            {activeTab === 'write' && (
              <WriteTab
                chapters={chapters}
                projects={projects}
                onExtractCodex={() => {
                  const chaptersText = getAllChaptersText();
                  setExtractCodexModal({ isOpen: true, text: chaptersText });
                }}
              />
            )}

            {activeTab === 'projects' && (
              <ProjectsTab
                projects={projects}
                currentProject={currentProject}
                loadProjects={loadProjects}
                loadProject={loadProject}
                deleteProject={deleteProject}
                setActiveProject={setActiveProject}
                setActiveVersionId={setActiveVersionId}
                activeVersionId={activeVersionId}
              />
            )}

            {activeTab === 'chapters' && (
              <ChapterManager
                chapters={chapters}
                currentChapter={currentChapter}
                currentSubchapter={currentSubchapter}
                onCreateChapter={createChapter}
                onCreateSubchapter={createSubchapter}
                onLoadChapter={setCurrentChapter}
                onLoadSubchapter={setCurrentSubchapter}
                onDeleteChapter={(chapterId) => {
                  const chapter = chapters.find(c => c.id === chapterId);
                  handleDeleteChapter(chapter);
                }}
                onDeleteSubchapter={(chapterId, subchapterId) => {
                  const chapter = chapters.find(c => c.id === chapterId);
                  const subchapter = chapter?.subchapters?.find(s => s.id === subchapterId);
                  handleDeleteSubchapter(chapter, subchapter);
                }}
                onEditChapter={handleEditChapter}
                onEditSubchapter={handleEditSubchapter}
                onImportText={() => setImportModal({ isOpen: true })}
                getChapterStats={() => ({})}
              />
            )}

            {activeTab === 'codex' && (
              <CodexTab
                currentProject={currentProject}
                chapters={chapters}
                projects={projects}
              />
            )}

            {/* Messaggio per funzionalità spostate */}
            {!['input', 'write', 'projects', 'chapters', 'codex'].includes(activeTab) && (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Funzionalità spostata nel WriterCodex</p>
                <p className="text-sm text-gray-500 mt-2">
                  🔧 Le funzionalità di analisi sono ora disponibili come tool separati
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modali di modifica */}
      <EditChapterModal
        chapter={editChapterModal.chapter}
        isOpen={editChapterModal.isOpen}
        onClose={() => setEditChapterModal({ isOpen: false, chapter: null })}
        onSave={handleSaveChapter}
        onDelete={null} // Eliminazione gestita da modale separata
      />

      <EditSubchapterModal
        subchapter={editSubchapterModal.subchapter}
        chapterId={editSubchapterModal.chapter?.id}
        isOpen={editSubchapterModal.isOpen}
        onClose={() => setEditSubchapterModal({ isOpen: false, chapter: null, subchapter: null })}
        onSave={handleSaveSubchapter}
        onDelete={null} // Eliminazione gestita da modale separata
      />

      {/* Modali di conferma eliminazione */}
      <ConfirmDeleteModal
        isOpen={deleteChapterModal.isOpen}
        onClose={() => setDeleteChapterModal({ isOpen: false, chapter: null })}
        onConfirm={confirmDeleteChapter}
        title="Elimina Capitolo"
        message={`Sei sicuro di voler eliminare il capitolo "${deleteChapterModal.chapter?.title}"? Questa azione eliminerà anche tutti i sottocapitoli associati.`}
        itemName={deleteChapterModal.chapter?.title}
        itemType="capitolo"
        isLoading={isDeleting}
      />

      <ConfirmDeleteModal
        isOpen={deleteSubchapterModal.isOpen}
        onClose={() => setDeleteSubchapterModal({ isOpen: false, chapter: null, subchapter: null })}
        onConfirm={confirmDeleteSubchapter}
        title="Elimina Sottocapitolo"
        message={`Sei sicuro di voler eliminare il sottocapitolo "${deleteSubchapterModal.subchapter?.title}"? Tutti i contenuti e i collegamenti verranno eliminati.`}
        itemName={deleteSubchapterModal.subchapter?.title}
        itemType="sottocapitolo"
        isLoading={isDeleting}
      />

      {/* Modale di import */}
      <ImportTextModal
        isOpen={importModal.isOpen}
        onClose={() => setImportModal({ isOpen: false })}
        onImport={handleImportStructure}
      />

      {/* Modale di estrazione Codex */}
      <ExtractCodexModal
        isOpen={extractCodexModal.isOpen}
        onClose={() => setExtractCodexModal({ isOpen: false })}
        text={extractCodexModal.text || text}
        onElementsCreated={(elements) => {
          console.log('Elementi Codex creati:', elements);
          // Qui potresti aggiungere logica per aggiornare il Codex se necessario
        }}
      />
    </div>
  );
};

export default MainEditor;
