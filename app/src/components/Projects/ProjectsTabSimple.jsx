import React, { useState } from 'react';
import { History, FileText, Plus, Trash2, Eye, ChevronDown, ChevronRight } from 'lucide-react';

const ProjectsTabSimple = ({
  projects,
  currentProject,
  loadProjects,
  loadProject,
  deleteProject,
  setActiveProject,
  setActiveVersionId,
  activeVersionId
}) => {
  const [expandedProjects, setExpandedProjects] = useState(new Set());

  // Toggle espansione progetto
  const toggleProjectExpansion = (projectId) => {
    setExpandedProjects(prev => {
      const newSet = new Set(prev);
      if (newSet.has(projectId)) {
        newSet.delete(projectId);
      } else {
        newSet.add(projectId);
      }
      return newSet;
    });
  };

  // Carica progetto e versione
  const handleLoadProject = async (project, versionId = null) => {
    try {
      const fullProject = await loadProject(project.id);
      setActiveProject(fullProject);
      
      if (versionId) {
        setActiveVersionId(versionId);
      } else if (fullProject.versions && fullProject.versions.length > 0) {
        // Carica l'ultima versione
        setActiveVersionId(fullProject.versions[fullProject.versions.length - 1].id);
      }
    } catch (error) {
      console.error('Errore caricamento progetto:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Gestione Progetti</h2>
          <p className="text-sm text-gray-600">
            {projects?.length || 0} progetti disponibili
          </p>
        </div>
        
        <button
          onClick={loadProjects}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded flex items-center gap-2 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Aggiorna Lista
        </button>
      </div>

      {/* Lista progetti */}
      <div className="space-y-4">
        {!projects || projects.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Nessun progetto trovato</p>
            <p className="text-sm text-gray-500 mt-2">
              Crea un nuovo progetto dalla tab "Testo"
            </p>
          </div>
        ) : (
          projects.map((project) => (
            <div
              key={project.id}
              className={`border rounded-lg p-4 ${
                currentProject?.id === project.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              {/* Header progetto */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => toggleProjectExpansion(project.id)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {expandedProjects.has(project.id) ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </button>
                  
                  <div>
                    <h3 className="font-medium text-gray-900">{project.title}</h3>
                    <p className="text-sm text-gray-500">
                      {project.versions?.length || 0} versioni • 
                      Aggiornato: {new Date(project.updated_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleLoadProject(project)}
                    className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm flex items-center gap-1 transition-colors"
                  >
                    <Eye className="w-3 h-3" />
                    Carica
                  </button>

                  {deleteProject && (
                    <button
                      onClick={() => deleteProject(project.id)}
                      className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm flex items-center gap-1 transition-colors"
                    >
                      <Trash2 className="w-3 h-3" />
                      Elimina
                    </button>
                  )}
                </div>
              </div>

              {/* Lista versioni (se espanso) */}
              {expandedProjects.has(project.id) && project.versions && (
                <div className="mt-4 ml-7 space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Versioni:</h4>
                  {project.versions.map((version, index) => (
                    <div
                      key={version.id}
                      className={`flex items-center justify-between p-2 rounded ${
                        activeVersionId === version.id
                          ? 'bg-blue-100 border border-blue-300'
                          : 'bg-gray-50 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 text-gray-400" />
                        <div>
                          <span className="text-sm font-medium">
                            v{index + 1} {version.name && `- ${version.name}`}
                          </span>
                          <div className="text-xs text-gray-500">
                            {version.word_count || 0} parole • 
                            {new Date(version.timestamp).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => handleLoadProject(project, version.id)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        Carica
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Progetto corrente */}
      {currentProject && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-green-800">
            <Eye className="w-5 h-5" />
            <h3 className="font-medium">Progetto Attivo</h3>
          </div>
          <p className="text-green-700 mt-1">
            <strong>{currentProject.title}</strong>
            {activeVersionId && currentProject.versions && (
              <span className="ml-2">
                • Versione {currentProject.versions.findIndex(v => v.id === activeVersionId) + 1}
              </span>
            )}
          </p>
        </div>
      )}
    </div>
  );
};

export default ProjectsTabSimple;
