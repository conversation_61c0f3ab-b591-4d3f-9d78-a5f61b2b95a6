import React, { useState } from 'react';
import { Link, X, Book } from 'lucide-react';

const LinkToChapterModal = ({ 
  isOpen, 
  onClose, 
  sourceProject,
  sourceVersion,
  chapters,
  onLinkToSubchapter
}) => {
  const [selectedChapterId, setSelectedChapterId] = useState('');
  const [selectedSubchapterId, setSelectedSubchapterId] = useState('');

  if (!isOpen || !sourceProject || !sourceVersion) return null;

  const handleConfirm = () => {
    if (selectedChapterId && selectedSubchapterId) {
      const selectedChapter = chapters.find(c => c.id === selectedChapterId);
      const selectedSubchapter = selectedChapter?.subchapters?.find(s => s.id === selectedSubchapterId);
      
      if (selectedChapter && selectedSubchapter) {
        onLinkToSubchapter(
          selectedChapter,
          selectedSubchapter,
          sourceProject.id,
          sourceVersion.id,
          {
            projectTitle: sourceProject.title || sourceProject.name,
            versionName: sourceVersion.name || `Versione ${new Date(sourceVersion.timestamp).toLocaleDateString()}`,
            text: sourceVersion.text || '',
            wordCount: sourceVersion.wordCount || 0,
            analysis: sourceVersion.analysis
          }
        );
      }
    }

    onClose();
    resetForm();
  };

  const resetForm = () => {
    setSelectedChapterId('');
    setSelectedSubchapterId('');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <Link className="w-6 h-6 text-green-600" />
            Collega Versione a Sottocapitolo
          </h2>
          <button onClick={() => { onClose(); resetForm(); }} className="text-gray-400 hover:text-gray-600">
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Sorgente */}
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="font-medium text-green-800 mb-2">Versione da Collegare</h3>
            <div className="text-sm text-green-700">
              <div><strong>📁 Progetto:</strong> {sourceProject.title || sourceProject.name}</div>
              <div><strong>📄 Versione:</strong> {sourceVersion.name || `Versione ${new Date(sourceVersion.timestamp).toLocaleDateString()}`}</div>
              <div><strong>📝 Parole:</strong> {sourceVersion.wordCount || 0}</div>
              <div><strong>🕒 Creata:</strong> {new Date(sourceVersion.timestamp).toLocaleDateString()}</div>
            </div>
          </div>

          {/* Selezione destinazione */}
          <div>
            <h3 className="font-medium text-gray-800 mb-3">Seleziona Sottocapitolo di Destinazione</h3>
            
            {chapters.length > 0 ? (
              <div className="space-y-3">
                {chapters.map(chapter => (
                  <div key={chapter.id} className="border border-gray-200 rounded-lg">
                    <label className={`flex items-center p-3 cursor-pointer ${
                      selectedChapterId === chapter.id ? 'bg-blue-50' : 'hover:bg-gray-50'
                    }`}>
                      <input
                        type="radio"
                        name="chapter"
                        value={chapter.id}
                        checked={selectedChapterId === chapter.id}
                        onChange={() => {
                          setSelectedChapterId(chapter.id);
                          setSelectedSubchapterId(''); // Reset subchapter selection
                        }}
                        className="mr-3"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-800">{chapter.title}</div>
                        <div className="text-sm text-gray-600">{chapter.subchapters?.length || 0} sottocapitoli</div>
                      </div>
                    </label>

                    {/* Sottocapitoli del capitolo selezionato */}
                    {selectedChapterId === chapter.id && chapter.subchapters && (
                      <div className="border-t border-gray-200 bg-gray-50 p-3 space-y-2">
                        <div className="text-sm font-medium text-gray-700 mb-2">Seleziona Sottocapitolo:</div>
                        {chapter.subchapters.map(subchapter => (
                          <label
                            key={subchapter.id}
                            className={`flex items-center p-2 border rounded cursor-pointer ${
                              selectedSubchapterId === subchapter.id
                                ? 'border-green-500 bg-green-50'
                                : 'border-gray-200 bg-white hover:bg-gray-50'
                            }`}
                          >
                            <input
                              type="radio"
                              name="subchapter"
                              value={subchapter.id}
                              checked={selectedSubchapterId === subchapter.id}
                              onChange={(e) => setSelectedSubchapterId(e.target.value)}
                              className="mr-2"
                            />
                            <div className="flex-1">
                              <div className="text-sm font-medium">{subchapter.title}</div>
                              <div className="text-xs text-gray-500 flex items-center gap-3">
                                <span>📝 {subchapter.liveVersion?.wordCount || 0} parole</span>
                                {subchapter.liveVersion?.sourceLink ? (
                                  <span className="text-orange-600">
                                    🔗 Già collegato a: {subchapter.liveVersion.sourceLink.projectTitle}
                                  </span>
                                ) : (
                                  <span className="text-green-600">✅ Disponibile per collegamento</span>
                                )}
                              </div>
                            </div>
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
                <Book className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                <p>Nessun capitolo disponibile</p>
                <p className="text-sm">Crea prima alcuni capitoli con sottocapitoli</p>
              </div>
            )}
          </div>

          {/* Anteprima collegamento */}
          {selectedChapterId && selectedSubchapterId && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-800 mb-2">Anteprima Collegamento</h4>
              <div className="text-sm text-blue-700">
                <div className="flex items-center gap-2 mb-1">
                  <span>📁 <strong>{sourceProject.title || sourceProject.name}</strong></span>
                  <span>→</span>
                  <span>📄 <strong>{sourceVersion.name || 'Versione'}</strong></span>
                </div>
                <div className="flex items-center gap-2">
                  <span>📖 <strong>{chapters.find(c => c.id === selectedChapterId)?.title}</strong></span>
                  <span>→</span>
                  <span>📄 <strong>{chapters.find(c => c.id === selectedChapterId)?.subchapters?.find(s => s.id === selectedSubchapterId)?.title}</strong></span>
                </div>
              </div>
              <div className="mt-2 text-xs text-blue-600">
                ⚠️ Il contenuto del sottocapitolo verrà sostituito con questa versione
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={() => { onClose(); resetForm(); }}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Annulla
          </button>
          <button
            onClick={handleConfirm}
            disabled={!selectedChapterId || !selectedSubchapterId}
            className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg flex items-center gap-2"
          >
            <Link className="w-4 h-4" />
            Collega Versione
          </button>
        </div>
      </div>
    </div>
  );
};

export default LinkToChapterModal;
