import { History, Download, Upload, Trash2, RefreshCw, ArrowRight, FileText, Eye, EyeOff, ChevronDown, ChevronRight } from 'lucide-react';
import useSyncManager from '../../hooks/useSyncManager';
import { useState } from 'react';

const ProjectsTab = ({
  projects: savedProjects, // Alias per compatibilità
  currentProject,
  chapters,
  loadProject,
  deleteProject,
  exportProject,
  importProject,
  onLinkToChapter
}) => {
  const { getAllChapters } = useSyncManager();
  const [expandedVersions, setExpandedVersions] = useState(new Set());

  // Funzione per gestire l'espansione delle versioni
  const toggleVersionExpansion = (versionId) => {
    setExpandedVersions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(versionId)) {
        newSet.delete(versionId);
      } else {
        newSet.add(versionId);
      }
      return newSet;
    });
  };

  // Trova tutti i sottocapitoli che usano contenuti da questo progetto/versione
  const findLinkedSubchapters = (projectId, versionId) => {
    const linkedSubchapters = [];

    chapters?.forEach(chapter => {
      chapter.subchapters?.forEach(subchapter => {
        // Controlla la nuova struttura sourceLink con controllo robusto degli ID
        const sourceLink = subchapter.liveVersion?.sourceLink;
        if (sourceLink &&
            String(sourceLink.projectId) === String(projectId) &&
            String(sourceLink.versionId) === String(versionId)) {
          linkedSubchapters.push({
            chapterId: chapter.id,
            chapterTitle: chapter.title,
            subchapterId: subchapter.id,
            subchapterTitle: subchapter.title,
            syncMode: 'linked', // Nuovo sistema di collegamento
            syncedAt: sourceLink.linkedAt,
            wordCount: subchapter.liveVersion?.wordCount || 0
          });
        }
      });
    });

    return linkedSubchapters;
  };

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200">
        <h3 className="text-xl font-bold text-purple-800 mb-2 flex items-center gap-2">
          <History className="w-6 h-6" />
          Gestione Progetti
        </h3>
        <p className="text-purple-700 mb-4 text-sm">
          Gestisci tutti i tuoi progetti letterari con versioni e analisi complete
        </p>

        {/* Statistiche di sincronizzazione globali */}
        {(() => {
          const chapters = getAllChapters();
          const totalSubchapters = chapters.reduce((sum, ch) => sum + (ch.subchapters?.length || 0), 0);
          const syncedSubchapters = chapters.reduce((sum, ch) =>
            sum + (ch.subchapters?.filter(sub => sub.contentOrigin?.sourceProject).length || 0), 0);

          if (syncedSubchapters > 0) {
            return (
              <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <RefreshCw className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-blue-800">Stato Sincronizzazione</span>
                </div>
                <div className="text-sm text-blue-700">
                  <span className="font-medium">{syncedSubchapters}</span> di <span className="font-medium">{totalSubchapters}</span> sottocapitoli
                  sono sincronizzati con versioni di progetti ({Math.round((syncedSubchapters / totalSubchapters) * 100)}%)
                </div>
              </div>
            );
          }
          return null;
        })()}

        {/* Controlli Import/Export */}
        <div className="flex gap-2 mb-4">
          <label className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 cursor-pointer flex items-center gap-2">
            <Upload className="w-4 h-4" />
            Importa Progetto
            <input
              type="file"
              accept=".json"
              onChange={importProject}
              className="hidden"
            />
          </label>
        </div>

        {savedProjects.length === 0 ? (
          <div className="text-center py-8">
            <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Nessun progetto salvato</p>
            <p className="text-sm text-gray-500 mt-2">Crea il tuo primo progetto nella scheda "Testo"</p>
          </div>
        ) : (
          <div className="space-y-4">
            {savedProjects.map((project) => (
              <div key={project.id} className={`bg-white p-4 rounded-lg border ${
                currentProject?.id === project.id ? 'border-purple-300 bg-purple-50' : 'border-gray-200'
              }`}>
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-800 flex items-center gap-2">
                      {project.name}
                      {currentProject?.id === project.id && (
                        <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs">
                          Attivo
                        </span>
                      )}
                    </h4>
                    <p className="text-sm text-gray-600">
                      Creato: {new Date(project.created_at || project.created).toLocaleDateString()} {new Date(project.created_at || project.created).toLocaleTimeString()} •
                      Modificato: {new Date(project.updated_at || project.lastModified).toLocaleDateString()} {new Date(project.updated_at || project.lastModified).toLocaleTimeString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => exportProject(project)}
                      className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
                    >
                      <Download className="w-4 h-4" />
                      Esporta
                    </button>
                    <button
                      onClick={() => deleteProject(project.id)}
                      className="text-red-600 hover:text-red-800 text-sm flex items-center gap-1"
                    >
                      <Trash2 className="w-4 h-4" />
                      Elimina
                    </button>
                  </div>
                </div>

                {/* Statistiche progetto */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                  <div className="bg-blue-50 p-2 rounded text-center">
                    <div className="text-lg font-bold text-blue-600">{project.versions.length}</div>
                    <div className="text-xs text-gray-600">Versioni</div>
                  </div>
                  <div className="bg-green-50 p-2 rounded text-center">
                    <div className="text-lg font-bold text-green-600">
                      {project.versions[project.versions.length - 1]?.wordCount || 0}
                    </div>
                    <div className="text-xs text-gray-600">Parole</div>
                  </div>
                  <div className="bg-purple-50 p-2 rounded text-center">
                    <div className="text-lg font-bold text-purple-600">
                      {project.versions[project.versions.length - 1]?.scores?.overall || 0}
                    </div>
                    <div className="text-xs text-gray-600">Punteggio</div>
                  </div>
                  <div className="bg-orange-50 p-2 rounded text-center">
                    <div className="text-lg font-bold text-orange-600">
                      {Math.round((Date.now() - new Date(project.updated_at || project.lastModified)) / (1000 * 60 * 60 * 24))}
                    </div>
                    <div className="text-xs text-gray-600">Giorni fa</div>
                  </div>
                </div>

                {/* Lista versioni */}
                <div className="bg-gray-50 p-3 rounded">
                  <h5 className="font-medium text-gray-700 mb-2">📝 Versioni ({project.versions.length})</h5>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {project.versions.map((version, index) => {
                      const linkedSubchapters = findLinkedSubchapters(project.id, version.id);

                      return (
                        <div key={version.id} className="bg-white p-3 rounded border border-gray-200">
                          {/* Header versione */}
                          <div className="flex justify-between items-center mb-2">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => toggleVersionExpansion(version.id)}
                                className="text-gray-500 hover:text-gray-700"
                              >
                                {expandedVersions.has(version.id) ?
                                  <ChevronDown className="w-4 h-4" /> :
                                  <ChevronRight className="w-4 h-4" />
                                }
                              </button>
                              <span className="font-medium">v{project.versions.length - index}</span>
                              <span className="text-gray-500">
                                {new Date(version.timestamp).toLocaleDateString()} {new Date(version.timestamp).toLocaleTimeString()}
                              </span>
                              <span className="text-gray-500">
                                {version.word_count || version.wordCount || 0} parole
                              </span>
                              {version.scores && (
                                <span className="text-gray-500">
                                  • {version.scores.overall}/100
                                </span>
                              )}
                            </div>
                            <div className="flex gap-2">
                              <button
                                onClick={() => toggleVersionExpansion(version.id)}
                                className="text-gray-600 hover:text-gray-800 px-2 py-1 rounded text-xs flex items-center gap-1"
                                title={expandedVersions.has(version.id) ? "Nascondi testo" : "Mostra testo"}
                              >
                                {expandedVersions.has(version.id) ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                                {expandedVersions.has(version.id) ? "Nascondi" : "Mostra"}
                              </button>
                              <button
                                onClick={() => loadProject(project, version.id)}
                                className="bg-purple-600 text-white px-3 py-1 rounded text-xs hover:bg-purple-700"
                              >
                                Carica
                              </button>
                              <button
                                onClick={() => onLinkToChapter && onLinkToChapter(project, version)}
                                className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 flex items-center gap-1"
                              >
                                <ArrowRight className="w-3 h-3" />
                                Collega
                              </button>
                            </div>
                          </div>

                          {/* Testo della versione - Espandibile */}
                          {expandedVersions.has(version.id) && (
                            <div className="mt-3 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                              <div className="flex items-center gap-2 mb-3">
                                <FileText className="w-4 h-4 text-gray-600" />
                                <span className="font-medium text-gray-700">Contenuto della versione</span>
                              </div>
                              {version.text && version.text.trim() ? (
                                <div className="bg-white p-4 rounded border border-gray-300 max-h-96 overflow-y-auto">
                                  <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed">
                                    {version.text}
                                  </pre>
                                </div>
                              ) : (
                                <div className="bg-yellow-50 p-3 rounded border border-yellow-200 text-center">
                                  <p className="text-yellow-700 text-sm">
                                    ⚠️ Nessun testo salvato in questa versione
                                  </p>
                                </div>
                              )}

                              {/* Statistiche del testo */}
                              {version.text && version.text.trim() && (
                                <div className="mt-3 pt-3 border-t border-gray-200">
                                  <div className="grid grid-cols-3 gap-4 text-xs text-gray-600">
                                    <div>
                                      <span className="font-medium">Caratteri:</span> {version.text.length}
                                    </div>
                                    <div>
                                      <span className="font-medium">Parole:</span> {version.text.split(/\s+/).filter(w => w.length > 0).length}
                                    </div>
                                    <div>
                                      <span className="font-medium">Paragrafi:</span> {version.text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Collegamenti ai sottocapitoli - MOLTO VISIBILE */}
                          {linkedSubchapters.length > 0 ? (
                            <div className="mt-2 p-3 bg-gradient-to-r from-green-50 to-blue-50 border border-green-300 rounded-lg">
                              <div className="flex items-center gap-2 mb-2">
                                <RefreshCw className="w-4 h-4 text-green-600" />
                                <span className="text-sm font-semibold text-green-800">
                                  🔗 COLLEGATA A {linkedSubchapters.length} SOTTOCAPITOLO{linkedSubchapters.length > 1 ? 'I' : ''}
                                </span>
                              </div>
                              <div className="space-y-2">
                                {linkedSubchapters.map((link) => (
                                  <div key={`${link.chapterId}-${link.subchapterId}`} className="bg-white p-2 rounded border border-green-200">
                                    <div className="flex items-center gap-2 text-sm">
                                      <FileText className="w-4 h-4 text-blue-600" />
                                      <span className="font-medium text-gray-800">
                                        📖 {link.chapterTitle}
                                      </span>
                                      <ArrowRight className="w-3 h-3 text-gray-400" />
                                      <span className="font-medium text-blue-700">
                                        📄 {link.subchapterTitle}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-3 mt-1 text-xs text-gray-600">
                                      <span>📝 {link.word_count || link.wordCount || 0} parole</span>
                                      <span>🕒 {new Date(link.linked_at || link.syncedAt).toLocaleDateString()} {new Date(link.linked_at || link.syncedAt).toLocaleTimeString()}</span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ) : (
                            <div className="mt-2 p-2 bg-gray-50 border border-gray-200 border-dashed rounded-lg">
                              <div className="flex items-center gap-2 text-gray-500">
                                <RefreshCw className="w-3 h-3" />
                                <span className="text-xs">❌ Non collegata a nessun sottocapitolo</span>
                              </div>
                              <div className="text-xs text-gray-400 mt-1">
                                Usa il pulsante "Trasferisci" per collegare questa versione
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Confronto versioni se ci sono più versioni */}
                {project.versions.length > 1 && (
                  <div className="mt-3 p-3 bg-blue-50 rounded">
                    <h5 className="font-medium text-blue-800 mb-2">📈 Progressi</h5>
                    <div className="text-sm space-y-1">
                      {(() => {
                        const first = project.versions[0];
                        const latest = project.versions[project.versions.length - 1];
                        const wordDiff = latest.wordCount - first.wordCount;
                        const scoreDiff = latest.scores?.overall - first.scores?.overall || 0;
                        
                        return (
                          <>
                            <div className="flex justify-between">
                              <span>Parole:</span>
                              <span className={wordDiff >= 0 ? 'text-green-600' : 'text-red-600'}>
                                {wordDiff >= 0 ? '+' : ''}{wordDiff}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Punteggio:</span>
                              <span className={scoreDiff >= 0 ? 'text-green-600' : 'text-red-600'}>
                                {scoreDiff >= 0 ? '+' : ''}{scoreDiff}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Versioni:</span>
                              <span className="text-blue-600">{project.versions.length} revisioni</span>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                )}

                {/* Pulsante carica progetto */}
                <div className="mt-3 text-center">
                  <button
                    onClick={() => loadProject(project)}
                    className="bg-purple-600 text-white px-6 py-2 rounded hover:bg-purple-700"
                  >
                    {currentProject?.id === project.id ? 'Progetto Attivo' : 'Carica Progetto'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectsTab;
