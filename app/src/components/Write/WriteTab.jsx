import React, { useState, useEffect, useCallback } from 'react';
import { Book, Eye, EyeOff, ChevronDown, ChevronUp, FileText } from 'lucide-react';
import TextWithCodexLinks from '../../WriterCodex/components/TextWithCodexLinks';
import CodexElementModal from '../../WriterCodex/components/CodexElementModal';
import { useCodex } from '../../WriterCodex/hooks/useCodex';

const WriteTab = ({ chapters, projects }) => {
  const [showCodexLinks, setShowCodexLinks] = useState(true);
  const [selectedCodexElement, setSelectedCodexElement] = useState(null);
  const [selectedElementCitations, setSelectedElementCitations] = useState([]);
  const [showCodexModal, setShowCodexModal] = useState(false);
  const [expandedChapters, setExpandedChapters] = useState({});
  const [chapterTexts, setChapterTexts] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Codex hook
  const { elements, analyzeTextReferences, extractCharacterQuotes } = useCodex();





  // Load chapter texts
  const processSubchapter = (subchapter, chapterId, texts, projects) => {
    // Check for direct text in liveVersion or sourceLink to project
    if (subchapter.liveVersion && subchapter.liveVersion.text) {
      texts[chapterId][subchapter.id] = {
        title: subchapter.title,
        text: subchapter.liveVersion.text,
        projectTitle: subchapter.sourceLink?.projectTitle || 'Direct text',
        versionIndex: 1,
        wordCount: subchapter.liveVersion.wordCount || 0
      };
    } else if (
      subchapter.sourceLink &&
      subchapter.sourceLink.projectId &&
      subchapter.sourceLink.versionId &&
      projects
    ) {
      try {
        const project = projects.find(p => p.id === subchapter.sourceLink.projectId);
        if (project && project.versions) {
          const version = project.versions.find(v => v.id === subchapter.sourceLink.versionId);
          if (version && version.text) {
            texts[chapterId][subchapter.id] = {
              title: subchapter.title,
              text: version.text,
              projectTitle: project.title,
              versionIndex: project.versions.findIndex(v => v.id === subchapter.sourceLink.versionId) + 1
            };
          }
        }
      } catch (error) {
        console.error(`Error loading subchapter ${subchapter.id}:`, error);
      }
    }
  };

  const processChapters = (chapters, projects) => {
    const texts = {};
    for (const chapter of chapters) {
      if (chapter.subchapters && chapter.subchapters.length > 0) {
        texts[chapter.id] = {};
        for (const subchapter of chapter.subchapters) {
          processSubchapter(subchapter, chapter.id, texts, projects);
        }
      }
    }
    return texts;
  };

  useEffect(() => {
    if (!chapters || chapters.length === 0) {
      return;
    }
    setIsLoading(true);
    try {
      const texts = processChapters(chapters, projects);
      setChapterTexts(texts);
    } catch (error) {
      console.error('Error loading chapter texts:', error);
    } finally {
      setIsLoading(false);
    }
  }, [chapters, projects]);

  // Handle chapter expand/collapse
  const toggleChapter = useCallback((chapterId) => {
    setExpandedChapters(prev => ({
      ...prev,
      [chapterId]: !prev[chapterId]
    }));
  }, []);

  // Handle Codex element click
  const handleCodexElementClick = (element, reference) => {
    setSelectedCodexElement(element);

    // Calcola citazioni in tempo reale
    let elementCitations = [];

    Object.entries(chapterTexts).forEach(([chapterId, chapterData]) => {
      const chapter = chapters.find(c => c.id === chapterId);
      const chapterTitle = chapter?.title || 'Capitolo senza titolo';

      Object.entries(chapterData).forEach(([, subchapterData]) => {
        const quotes = extractCharacterQuotes(
          subchapterData.text,
          chapterTitle,
          subchapterData.title
        );

        // Aggiungi le citazioni per questo elemento specifico
        if (quotes[element.id]) {
          elementCitations.push(...quotes[element.id].quotes);
        }
      });
    });

    // Ordina le citazioni per timestamp (più recenti prima)
    elementCitations.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    setSelectedElementCitations(elementCitations);
    setShowCodexModal(true);
  };



  // Combine all texts for Codex analysis
  const getAllTexts = () => {
    let allTexts = '';
    Object.values(chapterTexts).forEach(chapterData => {
      Object.values(chapterData).forEach(subchapterData => {
        if (subchapterData.text) {
          allTexts += subchapterData.text + '\n\n';
        }
      });
    });
    return allTexts;
  };

  // Analyze all texts for Codex references
  const allTexts = getAllTexts();
  const globalReferences = analyzeTextReferences(allTexts);

  // Accessibility: announce reference count changes
  const [referenceCount, setReferenceCount] = useState(globalReferences.length);
  useEffect(() => {
    setReferenceCount(globalReferences.length);
  }, [globalReferences.length]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12" aria-busy="true" aria-live="polite">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p className="ml-3 text-gray-600">Loading texts...</p>
      </div>
    );
  }

  if (!chapters || chapters.length === 0) {
    return (
      <div className="text-center py-12" aria-live="polite">
        <Book className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">No chapters available</p>
        <p className="text-sm text-gray-500 mt-2">
          Go to the "Chapters" tab to create and organize your chapters
        </p>
      </div>
    );
  }

  const hasAnyText = Object.keys(chapterTexts).some(chapterId =>
    Object.keys(chapterTexts[chapterId]).length > 0
  );

  if (!hasAnyText) {
    return (
      <div className="text-center py-12" aria-live="polite">
        <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">No text available in chapters</p>
        <p className="text-sm text-gray-500 mt-2">
          Link versions to subchapters to display texts
        </p>
      </div>
    );
  }

  // Subcomponents for readability and accessibility
  const CodexLinkToggle = ({ showCodexLinks, setShowCodexLinks, globalReferences }) => (
    <button
      onClick={() => setShowCodexLinks(!showCodexLinks)}
      className={`px-4 py-2 rounded flex items-center gap-2 transition-colors ${
        showCodexLinks
          ? 'bg-purple-500 hover:bg-purple-600 text-white'
          : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
      }`}
      aria-pressed={showCodexLinks}
      aria-live="polite"
    >
      {showCodexLinks ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
      {showCodexLinks ? 'Hide Links' : 'Show Links'}
      {showCodexLinks && globalReferences.length > 0 && (
        <span className="bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full" aria-live="polite">
          {globalReferences.length}
        </span>
      )}
    </button>
  );

  const ChapterHeader = ({ chapter, chapterData, hasSubchapters, isExpanded, toggleChapter }) => (
    <div
      className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
      role="button"
      tabIndex={0}
      aria-expanded={isExpanded}
      aria-controls={`chapter-content-${chapter.id}`}
      onClick={() => toggleChapter(chapter.id)}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          toggleChapter(chapter.id);
        }
      }}
    >
      <div className="flex items-center gap-3">
        <Book className="w-5 h-5 text-blue-600" />
        <div>
          <h2 className="font-medium text-gray-900">{chapter.title}</h2>
          {chapter.description && (
            <p className="text-sm text-gray-600">{chapter.description}</p>
          )}
        </div>
      </div>
      <div className="flex items-center gap-2">
        {hasSubchapters && (
          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
            {Object.keys(chapterData).length} subchapters
          </span>
        )}
        {hasSubchapters && (
          isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
        )}
      </div>
    </div>
  );

  const SubchapterContent = ({ subchapterId, subchapterData, showCodexLinks, analyzeTextReferences, handleCodexElementClick }) => {
    const subchapterReferences = analyzeTextReferences(subchapterData.text);


    return (
      <div key={subchapterId} className="border-l-4 border-blue-200 pl-4">
        <div className="mb-3">
          <h3 className="font-medium text-gray-800">{subchapterData.title}</h3>
          <p className="text-xs text-gray-500">
            From: {subchapterData.projectTitle} (v{subchapterData.versionIndex})
          </p>
        </div>
        <div className="bg-white border border-gray-200 rounded p-4">


          {showCodexLinks && subchapterReferences.length > 0 ? (
            <div className="relative">
              <TextWithCodexLinks
                text={subchapterData.text}
                references={subchapterReferences}
                onElementClick={handleCodexElementClick}
                className="whitespace-pre-wrap leading-relaxed text-gray-800"
                linkClassName="text-purple-600 hover:text-purple-800 underline cursor-pointer bg-purple-50 px-1 rounded font-medium"
              />
              <div className="absolute top-2 right-2 bg-purple-100 text-purple-700 text-xs px-2 py-1 rounded" aria-live="polite">
                {subchapterReferences.length} references
              </div>
            </div>
          ) : (
            <div className="whitespace-pre-wrap leading-relaxed text-gray-800">
              {subchapterData.text}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-lg font-semibold text-gray-900">Sequential View</h1>
          <p className="text-sm text-gray-600">
            All chapter texts in sequence with Codex links
          </p>
        </div>

        {/* Controls */}
        <div className="flex gap-2">
          {/* Toggle Codex Links */}
          {elements.length > 0 && (
            <CodexLinkToggle showCodexLinks={showCodexLinks} setShowCodexLinks={setShowCodexLinks} globalReferences={globalReferences} />
          )}
        </div>
      </div>
      {/* Chapters */}
      <div className="space-y-4">
        {chapters.map((chapter) => {
          const chapterData = chapterTexts[chapter.id] || {};
          const hasSubchapters = Object.keys(chapterData).length > 0;
          const isExpanded = expandedChapters[chapter.id] !== false;
          return (
            <div key={chapter.id} className="border border-gray-200 rounded-lg">
              <ChapterHeader
                chapter={chapter}
                chapterData={chapterData}
                hasSubchapters={hasSubchapters}
                isExpanded={isExpanded}
                toggleChapter={toggleChapter}
              />
              {/* Chapter Content */}
              {isExpanded && hasSubchapters && (
                <div className="p-4 space-y-6" id={`chapter-content-${chapter.id}`}
                  aria-live="polite">
                  {Object.entries(chapterData).map(([subchapterId, subchapterData]) => (
                    <SubchapterContent
                      key={subchapterId}
                      subchapterId={subchapterId}
                      subchapterData={subchapterData}
                      showCodexLinks={showCodexLinks}
                      analyzeTextReferences={analyzeTextReferences}
                      handleCodexElementClick={handleCodexElementClick}
                    />
                  ))}
                </div>
              )}
              {/* Message if no subchapters */}
              {isExpanded && !hasSubchapters && (
                <div className="p-4 text-center text-gray-500" aria-live="polite">
                  <FileText className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No subchapters with linked text</p>
                </div>
              )}
            </div>
          );
        })}
      </div>
      {/* Codex Element Modal */}
      <CodexElementModal
        element={selectedCodexElement}
        isOpen={showCodexModal}
        onClose={() => {
          setShowCodexModal(false);
          setSelectedElementCitations([]);
        }}
        onEdit={(element) => {
          console.log('Edit element:', element);
        }}
        onDelete={(element) => {
          console.log('Delete element:', element);
        }}
        citations={selectedElementCitations}
      />
    </div>
  );
};

export default WriteTab;
