import React, { useState } from 'react';
import { ArrowRight, X, Book, Plus } from 'lucide-react';

const TransferToChapterModal = ({ 
  isOpen, 
  onClose, 
  sourceProject,
  sourceVersion,
  chapters,
  onTransfer,
  onCreateNewChapter
}) => {
  const [selectedChapterId, setSelectedChapterId] = useState('');
  const [selectedSubchapterId, setSelectedSubchapterId] = useState('');
  const [showNewSubchapterForm, setShowNewSubchapterForm] = useState(false);
  const [showNewChapterForm, setShowNewChapterForm] = useState(false);
  const [newSubchapterTitle, setNewSubchapterTitle] = useState('');
  const [newSubchapterDescription, setNewSubchapterDescription] = useState('');
  const [newChapterTitle, setNewChapterTitle] = useState('');
  const [newChapterDescription, setNewChapterDescription] = useState('');
  const [versionMode, setVersionMode] = useState('live');

  if (!isOpen || !sourceProject || !sourceVersion) return null;

  const handleConfirm = () => {
    if (showNewChapterForm && newChapterTitle.trim() && newSubchapterTitle.trim()) {
      onCreateNewChapter({
        chapterTitle: newChapterTitle.trim(),
        chapterDescription: newChapterDescription.trim(),
        subchapterTitle: newSubchapterTitle.trim(),
        subchapterDescription: newSubchapterDescription.trim(),
        sourceProject,
        sourceVersion,
        versionMode
      });
    } else if (selectedChapterId) {
      onTransfer({
        sourceProject,
        sourceVersion,
        chapterId: selectedChapterId,
        subchapterId: selectedSubchapterId,
        newSubchapterTitle: showNewSubchapterForm ? newSubchapterTitle.trim() : null,
        newSubchapterDescription: showNewSubchapterForm ? newSubchapterDescription.trim() : null,
        versionMode
      });
    }

    onClose();
    resetForm();
  };

  const resetForm = () => {
    setSelectedChapterId('');
    setSelectedSubchapterId('');
    setShowNewSubchapterForm(false);
    setShowNewChapterForm(false);
    setNewSubchapterTitle('');
    setNewSubchapterDescription('');
    setNewChapterTitle('');
    setNewChapterDescription('');
    setVersionMode('live');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <ArrowRight className="w-6 h-6 text-blue-600" />
            Trasferisci a Capitolo
          </h2>
          <button onClick={() => { onClose(); resetForm(); }} className="text-gray-400 hover:text-gray-600">
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Sorgente */}
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-medium text-blue-800 mb-2">Sorgente</h3>
            <div className="text-sm text-blue-700">
              <div><strong>Progetto:</strong> {sourceProject.title}</div>
              <div><strong>Versione:</strong> {sourceVersion.name || `Versione ${new Date(sourceVersion.timestamp).toLocaleDateString()}`}</div>
              <div><strong>Parole:</strong> {sourceVersion.wordCount || 0}</div>
            </div>
          </div>

          {/* Modalità trasferimento */}
          <div>
            <h3 className="font-medium text-gray-800 mb-3">Modalità Trasferimento</h3>
            <div className="grid grid-cols-2 gap-3">
              <label className={`p-3 border rounded-lg cursor-pointer ${
                versionMode === 'live' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}>
                <input
                  type="radio"
                  name="versionMode"
                  value="live"
                  checked={versionMode === 'live'}
                  onChange={(e) => setVersionMode(e.target.value)}
                  className="sr-only"
                />
                <div className="text-sm font-medium text-blue-800">Aggiorna LIVE</div>
                <div className="text-xs text-gray-600">Sostituisce la versione corrente</div>
              </label>
              <label className={`p-3 border rounded-lg cursor-pointer ${
                versionMode === 'backup' ? 'border-green-500 bg-green-50' : 'border-gray-200'
              }`}>
                <input
                  type="radio"
                  name="versionMode"
                  value="backup"
                  checked={versionMode === 'backup'}
                  onChange={(e) => setVersionMode(e.target.value)}
                  className="sr-only"
                />
                <div className="text-sm font-medium text-green-800">Salva come Backup</div>
                <div className="text-xs text-gray-600">Mantiene LIVE + crea backup</div>
              </label>
            </div>
          </div>

          {/* Destinazione */}
          <div>
            <h3 className="font-medium text-gray-800 mb-3">Seleziona Destinazione</h3>
            
            {/* Opzione nuovo capitolo */}
            <div className="mb-4">
              <label className={`flex items-center p-3 border-2 border-dashed rounded-lg cursor-pointer ${
                showNewChapterForm ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
              }`}>
                <input
                  type="radio"
                  name="destination"
                  value="new-chapter"
                  checked={showNewChapterForm}
                  onChange={() => {
                    setShowNewChapterForm(true);
                    setSelectedChapterId('');
                    setSelectedSubchapterId('');
                    setShowNewSubchapterForm(false);
                  }}
                  className="mr-3"
                />
                <div className="flex items-center gap-2">
                  <Book className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-blue-700">Crea Nuovo Capitolo</span>
                </div>
              </label>

              {showNewChapterForm && (
                <div className="mt-3 p-4 bg-blue-50 rounded-lg border border-blue-200 space-y-3">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">Titolo Capitolo *</label>
                      <input
                        type="text"
                        value={newChapterTitle}
                        onChange={(e) => setNewChapterTitle(e.target.value)}
                        placeholder="Es: Capitolo 1..."
                        className="w-full p-2 border border-blue-300 rounded"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">Titolo Sottocapitolo *</label>
                      <input
                        type="text"
                        value={newSubchapterTitle}
                        onChange={(e) => setNewSubchapterTitle(e.target.value)}
                        placeholder="Es: Prologo..."
                        className="w-full p-2 border border-blue-300 rounded"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">Descrizione Capitolo</label>
                      <input
                        type="text"
                        value={newChapterDescription}
                        onChange={(e) => setNewChapterDescription(e.target.value)}
                        placeholder="Descrizione..."
                        className="w-full p-2 border border-blue-300 rounded"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">Descrizione Sottocapitolo</label>
                      <input
                        type="text"
                        value={newSubchapterDescription}
                        onChange={(e) => setNewSubchapterDescription(e.target.value)}
                        placeholder="Descrizione..."
                        className="w-full p-2 border border-blue-300 rounded"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Capitoli esistenti */}
            {chapters.length > 0 && (
              <div className="space-y-2">
                {chapters.map(chapter => (
                  <div key={chapter.id} className="border border-gray-200 rounded-lg">
                    <label className={`flex items-center p-3 cursor-pointer ${
                      selectedChapterId === chapter.id ? 'bg-gray-50' : 'hover:bg-gray-50'
                    }`}>
                      <input
                        type="radio"
                        name="destination"
                        value={chapter.id}
                        checked={selectedChapterId === chapter.id}
                        onChange={() => {
                          setSelectedChapterId(chapter.id);
                          setSelectedSubchapterId('');
                          setShowNewSubchapterForm(false);
                          setShowNewChapterForm(false);
                        }}
                        className="mr-3"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-800">{chapter.title}</div>
                        <div className="text-sm text-gray-600">{chapter.subchapters?.length || 0} sottocapitoli</div>
                      </div>
                    </label>

                    {selectedChapterId === chapter.id && (
                      <div className="border-t border-gray-200 bg-gray-50 p-3 space-y-2">
                        {/* Sottocapitoli esistenti */}
                        {chapter.subchapters?.map(subchapter => (
                          <label
                            key={subchapter.id}
                            className={`flex items-center p-2 border rounded cursor-pointer ${
                              selectedSubchapterId === subchapter.id
                                ? 'border-green-500 bg-green-50'
                                : 'border-gray-200 bg-white hover:bg-gray-50'
                            }`}
                          >
                            <input
                              type="radio"
                              name="subchapter"
                              value={subchapter.id}
                              checked={selectedSubchapterId === subchapter.id}
                              onChange={(e) => {
                                setSelectedSubchapterId(e.target.value);
                                setShowNewSubchapterForm(false);
                              }}
                              className="mr-2"
                            />
                            <div className="flex-1">
                              <div className="text-sm font-medium">{subchapter.title}</div>
                              <div className="text-xs text-gray-500">
                                {subchapter.liveVersion?.wordCount || 0} parole
                              </div>
                            </div>
                          </label>
                        ))}

                        {/* Opzione nuovo sottocapitolo */}
                        <label className={`flex items-center p-2 border-2 border-dashed rounded cursor-pointer ${
                          showNewSubchapterForm ? 'border-purple-400 bg-purple-50' : 'border-gray-300'
                        }`}>
                          <input
                            type="radio"
                            name="subchapter"
                            value="new"
                            checked={showNewSubchapterForm}
                            onChange={() => {
                              setShowNewSubchapterForm(true);
                              setSelectedSubchapterId('');
                            }}
                            className="mr-2"
                          />
                          <div className="flex items-center gap-2">
                            <Plus className="w-3 h-3 text-purple-600" />
                            <span className="text-sm text-purple-700">Nuovo Sottocapitolo</span>
                          </div>
                        </label>

                        {showNewSubchapterForm && (
                          <div className="p-3 bg-purple-50 rounded border border-purple-200 space-y-2">
                            <input
                              type="text"
                              value={newSubchapterTitle}
                              onChange={(e) => setNewSubchapterTitle(e.target.value)}
                              placeholder="Titolo nuovo sottocapitolo"
                              className="w-full p-2 border border-purple-300 rounded text-sm"
                            />
                            <input
                              type="text"
                              value={newSubchapterDescription}
                              onChange={(e) => setNewSubchapterDescription(e.target.value)}
                              placeholder="Descrizione (opzionale)"
                              className="w-full p-2 border border-purple-300 rounded text-sm"
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {chapters.length === 0 && (
              <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
                <Book className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                <p>Nessun capitolo disponibile</p>
                <p className="text-sm">Crea un nuovo capitolo per trasferire il contenuto</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={() => { onClose(); resetForm(); }}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Annulla
          </button>
          <button
            onClick={handleConfirm}
            disabled={!showNewChapterForm && !selectedChapterId}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg"
          >
            {versionMode === 'live' ? 'Trasferisci e Aggiorna LIVE' : 'Trasferisci come Backup'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TransferToChapterModal;
