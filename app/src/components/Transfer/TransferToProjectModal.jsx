import React, { useState } from 'react';
import { Upload, X, FolderPlus } from 'lucide-react';

const TransferToProjectModal = ({ 
  isOpen, 
  onClose, 
  sourceChapter,
  sourceSubchapter,
  projects,
  onTransfer,
  onCreateNewProject
}) => {
  const [selectedProjectId, setSelectedProjectId] = useState('');
  const [showNewProjectForm, setShowNewProjectForm] = useState(false);
  const [newProjectTitle, setNewProjectTitle] = useState('');
  const [newProjectDescription, setNewProjectDescription] = useState('');
  const [versionName, setVersionName] = useState('');

  if (!isOpen || !sourceChapter || !sourceSubchapter) return null;

  const handleConfirm = () => {
    if (showNewProjectForm && newProjectTitle.trim()) {
      onCreateNewProject({
        projectTitle: newProjectTitle.trim(),
        projectDescription: newProjectDescription.trim(),
        versionName: versionName.trim() || `Da ${sourceChapter.title} - ${sourceSubchapter.title}`,
        sourceChapter,
        sourceSubchapter
      });
    } else if (selectedProjectId) {
      onTransfer({
        projectId: selectedProjectId,
        versionName: versionName.trim() || `Da ${sourceChapter.title} - ${sourceSubchapter.title}`,
        sourceChapter,
        sourceSubchapter
      });
    }

    onClose();
    resetForm();
  };

  const resetForm = () => {
    setSelectedProjectId('');
    setShowNewProjectForm(false);
    setNewProjectTitle('');
    setNewProjectDescription('');
    setVersionName('');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <Upload className="w-6 h-6 text-green-600" />
            Esporta a Progetto
          </h2>
          <button onClick={() => { onClose(); resetForm(); }} className="text-gray-400 hover:text-gray-600">
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Sorgente */}
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="font-medium text-green-800 mb-2">Sorgente</h3>
            <div className="text-sm text-green-700">
              <div><strong>Capitolo:</strong> {sourceChapter.title}</div>
              <div><strong>Sottocapitolo:</strong> {sourceSubchapter.title}</div>
              <div><strong>Parole:</strong> {sourceSubchapter.liveVersion?.wordCount || 0}</div>
              {sourceSubchapter.description && (
                <div><strong>Descrizione:</strong> {sourceSubchapter.description}</div>
              )}
            </div>
          </div>

          {/* Nome versione */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome Versione
            </label>
            <input
              type="text"
              value={versionName}
              onChange={(e) => setVersionName(e.target.value)}
              placeholder={`Da ${sourceChapter.title} - ${sourceSubchapter.title}`}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              Se lasci vuoto, verrà usato il nome predefinito
            </p>
          </div>

          {/* Destinazione */}
          <div>
            <h3 className="font-medium text-gray-800 mb-3">Seleziona Destinazione</h3>
            
            {/* Opzione nuovo progetto */}
            <div className="mb-4">
              <label className={`flex items-center p-3 border-2 border-dashed rounded-lg cursor-pointer ${
                showNewProjectForm ? 'border-green-400 bg-green-50' : 'border-gray-300 hover:border-green-400'
              }`}>
                <input
                  type="radio"
                  name="destination"
                  value="new-project"
                  checked={showNewProjectForm}
                  onChange={() => {
                    setShowNewProjectForm(true);
                    setSelectedProjectId('');
                  }}
                  className="mr-3"
                />
                <div className="flex items-center gap-2">
                  <FolderPlus className="w-4 h-4 text-green-600" />
                  <span className="font-medium text-green-700">Crea Nuovo Progetto</span>
                </div>
              </label>

              {showNewProjectForm && (
                <div className="mt-3 p-4 bg-green-50 rounded-lg border border-green-200 space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-green-700 mb-1">Titolo Progetto *</label>
                    <input
                      type="text"
                      value={newProjectTitle}
                      onChange={(e) => setNewProjectTitle(e.target.value)}
                      placeholder="Es: Il mio nuovo romanzo..."
                      className="w-full p-2 border border-green-300 rounded"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-green-700 mb-1">Descrizione Progetto</label>
                    <textarea
                      value={newProjectDescription}
                      onChange={(e) => setNewProjectDescription(e.target.value)}
                      placeholder="Descrizione del progetto..."
                      className="w-full p-2 border border-green-300 rounded h-20"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Progetti esistenti */}
            {projects.length > 0 && (
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {projects.map(project => (
                  <label
                    key={project.id}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer ${
                      selectedProjectId === project.id
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <input
                      type="radio"
                      name="destination"
                      value={project.id}
                      checked={selectedProjectId === project.id}
                      onChange={() => {
                        setSelectedProjectId(project.id);
                        setShowNewProjectForm(false);
                      }}
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-800">{project.title}</div>
                      {project.description && (
                        <div className="text-sm text-gray-600">{project.description}</div>
                      )}
                      <div className="text-xs text-gray-500 mt-1">
                        {project.versions?.length || 0} versioni • Ultimo aggiornamento: {new Date(project.updatedAt || project.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            )}

            {projects.length === 0 && (
              <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
                <FolderPlus className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                <p>Nessun progetto disponibile</p>
                <p className="text-sm">Crea un nuovo progetto per esportare il contenuto</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={() => { onClose(); resetForm(); }}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Annulla
          </button>
          <button
            onClick={handleConfirm}
            disabled={!showNewProjectForm && !selectedProjectId}
            className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg"
          >
            {showNewProjectForm ? 'Crea Progetto ed Esporta' : 'Esporta a Progetto'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TransferToProjectModal;
