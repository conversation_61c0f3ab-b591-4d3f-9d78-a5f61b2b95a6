import React, { useState, useEffect, useRef } from 'react';
import { Save, FileText, Download, ChevronDown, ChevronUp, Package, Sparkles } from 'lucide-react';
import TextWithCodexLinks from '../../WriterCodex/components/TextWithCodexLinks';
import CodexElementModal from '../../WriterCodex/components/CodexElementModal';
import { useCodex } from '../../WriterCodex/hooks/useCodex';

const InputTabSimple = ({
  text,
  setText,
  currentProject,
  saveProject,
  activeVersionId,
  setActiveVersionId,
  onExtractCodex
}) => {
  const [showVersionSelector, setShowVersionSelector] = useState(false);
  const [showCodexLinks, setShowCodexLinks] = useState(false);
  const [selectedCodexElement, setSelectedCodexElement] = useState(null);
  const [showCodexModal, setShowCodexModal] = useState(false);
  const dropdownRef = useRef(null);

  // Hook per il Codex
  const { elements, analyzeTextReferences } = useCodex();

  // Chiudi dropdown quando si clicca fuori
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowVersionSelector(false);
      }
    };

    if (showVersionSelector) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showVersionSelector]);

  // Funzione per caricare una versione specifica
  const handleLoadVersion = (versionId) => {
    const version = currentProject?.versions?.find(v => v.id === versionId);
    if (version) {
      const versionIndex = currentProject.versions.findIndex(v => v.id === versionId) + 1;
      const confirmMessage = `Vuoi caricare la versione ${versionIndex}?\n\nQuesto sostituirà il testo corrente nell'editor.`;

      if (window.confirm(confirmMessage)) {
        setText(version.text);
        setActiveVersionId(versionId);
        setShowVersionSelector(false);
      }
    }
  };

  // Ottieni la versione attiva
  const activeVersion = currentProject?.versions?.find(v => v.id === activeVersionId);

  // Analizza il testo per trovare riferimenti al Codex
  const textReferences = analyzeTextReferences(text);

  // Gestisce il click su un elemento del Codex
  const handleCodexElementClick = (element, reference) => {
    setSelectedCodexElement(element);
    setShowCodexModal(true);
  };

  return (
    <div className="space-y-4">
      {/* Header con info progetto */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Editor di Testo</h2>
          {currentProject && (
            <div className="text-sm text-gray-600">
              <span>Progetto: <span className="font-medium">{currentProject.title}</span></span>
              {activeVersion && (
                <span className="ml-4">
                  Versione: <span className="font-medium text-blue-600">
                    {currentProject.versions.findIndex(v => v.id === activeVersionId) + 1}
                  </span>
                  <span className="text-xs text-gray-500 ml-1">
                    ({new Date(activeVersion.timestamp).toLocaleDateString()})
                  </span>
                </span>
              )}
            </div>
          )}
        </div>

        {/* Pulsanti azioni */}
        {currentProject && (
          <div className="flex items-center gap-2">
            {/* Pulsante Carica */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setShowVersionSelector(!showVersionSelector)}
                disabled={!currentProject?.versions?.length}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded flex items-center gap-2 transition-colors"
              >
                <Download className="w-4 h-4" />
                Carica
                {showVersionSelector ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
              </button>

              {/* Dropdown versioni */}
              {showVersionSelector && currentProject?.versions && (
                <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-64">
                  <div className="p-2 border-b border-gray-100">
                    <p className="text-xs font-medium text-gray-700">Seleziona versione da caricare:</p>
                  </div>
                  <div className="max-h-60 overflow-y-auto">
                    {currentProject.versions.map((version, index) => (
                      <button
                        key={version.id}
                        onClick={() => handleLoadVersion(version.id)}
                        className={`w-full text-left px-3 py-2 hover:bg-gray-50 flex items-center justify-between ${
                          activeVersionId === version.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        <div>
                          <div className="font-medium">
                            v{index + 1} {version.name && `- ${version.name}`}
                          </div>
                          <div className="text-xs text-gray-500">
                            {version.word_count || 0} parole • {new Date(version.timestamp).toLocaleDateString()}
                          </div>
                        </div>
                        {activeVersionId === version.id && (
                          <div className="text-blue-500 text-xs">Attiva</div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Toggle Codex Links */}
            {elements.length > 0 && (
              <button
                onClick={() => setShowCodexLinks(!showCodexLinks)}
                className={`px-4 py-2 rounded flex items-center gap-2 transition-colors ${
                  showCodexLinks
                    ? 'bg-purple-500 hover:bg-purple-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                }`}
              >
                <Package className="w-4 h-4" />
                {showCodexLinks ? 'Nascondi Link' : 'Mostra Link'}
                {textReferences.length > 0 && (
                  <span className="bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full">
                    {textReferences.length}
                  </span>
                )}
              </button>
            )}

            {/* Pulsante Aggiorna Codex */}
            <button
              onClick={() => onExtractCodex && onExtractCodex()}
              disabled={!text.trim()}
              className="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded flex items-center gap-2 transition-colors"
              title="Estrai elementi per il Codex dal testo"
            >
              <Sparkles className="w-4 h-4" />
              Aggiorna Codex
            </button>

            {/* Pulsante Salva */}
            <button
              onClick={saveProject}
              disabled={!text.trim()}
              className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded flex items-center gap-2 transition-colors"
            >
              <Save className="w-4 h-4" />
              Salva Testo
            </button>
          </div>
        )}
      </div>

      {/* Area di testo */}
      <div className="border border-gray-300 rounded-lg">
        {showCodexLinks && textReferences.length > 0 ? (
          // Modalità visualizzazione con link Codex
          <div className="relative">
            <TextWithCodexLinks
              text={text}
              references={textReferences}
              onElementClick={handleCodexElementClick}
              className="w-full h-96 p-4 overflow-y-auto whitespace-pre-wrap font-mono text-sm leading-relaxed"
            />
            <div className="absolute top-2 right-2 bg-purple-100 text-purple-700 text-xs px-2 py-1 rounded">
              {textReferences.length} riferimenti trovati
            </div>
          </div>
        ) : (
          // Modalità editing normale
          <textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder={currentProject
              ? "Inizia a scrivere il tuo testo qui..."
              : "Seleziona un progetto dalla tab 'Progetti' per iniziare a scrivere"
            }
            disabled={!currentProject}
            className="w-full h-96 p-4 border-0 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
          />
        )}
      </div>

      {/* Info statistiche */}
      <div className="flex justify-between items-center text-sm text-gray-500">
        <div className="flex items-center gap-4">
          <span>Caratteri: {text.length}</span>
          <span>Parole: {text.trim() ? text.trim().split(/\s+/).length : 0}</span>
          <span>Righe: {text.split('\n').length}</span>
        </div>
        
        {!currentProject && (
          <div className="flex items-center gap-2 text-amber-600">
            <FileText className="w-4 h-4" />
            <span>Nessun progetto selezionato</span>
          </div>
        )}
      </div>

      {/* Messaggio se non c'è progetto */}
      {!currentProject && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-blue-800">
            <FileText className="w-5 h-5" />
            <h3 className="font-medium">Come iniziare</h3>
          </div>
          <p className="text-blue-700 mt-2">
            Per iniziare a scrivere, vai alla tab <strong>"Progetti"</strong> e:
          </p>
          <ul className="text-blue-700 mt-2 ml-4 list-disc">
            <li>Crea un nuovo progetto, oppure</li>
            <li>Carica un progetto esistente</li>
          </ul>
        </div>
      )}

      {/* Modale Codex Element */}
      <CodexElementModal
        element={selectedCodexElement}
        isOpen={showCodexModal}
        onClose={() => setShowCodexModal(false)}
        onEdit={(element) => {
          // TODO: Implementare modifica
          console.log('Edit element:', element);
        }}
        onDelete={(element) => {
          // TODO: Implementare eliminazione
          console.log('Delete element:', element);
        }}
      />
    </div>
  );
};

export default InputTabSimple;
