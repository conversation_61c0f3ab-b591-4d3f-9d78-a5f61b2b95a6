// Servizio API per comunicazione con il server locale
const API_BASE_URL = 'http://localhost:9000/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Metodo generico per richieste HTTP
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Se la risposta è vuota (204 No Content), restituisci null
      if (response.status === 204) {
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error(`API Error [${endpoint}]:`, error);
      throw error;
    }
  }

  // Metodi di convenienza
  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  }

  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: data,
    });
  }

  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: data,
    });
  }

  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // === PROGETTI ===

  async getProjects() {
    return this.get('/projects');
  }

  async getProject(id) {
    return this.get(`/projects/${id}`);
  }

  async createProject(projectData) {
    return this.post('/projects', projectData);
  }

  async updateProject(id, projectData) {
    return this.put(`/projects/${id}`, projectData);
  }

  async deleteProject(id) {
    return this.delete(`/projects/${id}`);
  }

  async createProjectVersion(projectId, versionData) {
    return this.post(`/projects/${projectId}/versions`, versionData);
  }

  async getProjectVersion(projectId, versionId) {
    return this.get(`/projects/${projectId}/versions/${versionId}`);
  }

  async deleteProjectVersion(projectId, versionId) {
    return this.delete(`/projects/${projectId}/versions/${versionId}`);
  }

  // === CAPITOLI ===

  async getChapters() {
    return this.get('/chapters');
  }

  async createChapter(chapterData) {
    return this.post('/chapters', chapterData);
  }

  async updateChapter(id, chapterData) {
    return this.put(`/chapters/${id}`, chapterData);
  }

  async deleteChapter(id) {
    return this.delete(`/chapters/${id}`);
  }

  async createSubchapter(chapterId, subchapterData) {
    return this.post(`/chapters/${chapterId}/subchapters`, subchapterData);
  }

  async updateSubchapter(chapterId, subchapterId, subchapterData) {
    return this.put(`/chapters/${chapterId}/subchapters/${subchapterId}`, subchapterData);
  }

  async linkVersionToSubchapter(chapterId, subchapterId, linkData) {
    return this.post(`/chapters/${chapterId}/subchapters/${subchapterId}/link`, linkData);
  }

  async unlinkVersionFromSubchapter(chapterId, subchapterId) {
    return this.delete(`/chapters/${chapterId}/subchapters/${subchapterId}/link`);
  }

  // === ANALISI ===

  async analyzeText(textData) {
    return this.post('/analysis/text', textData);
  }

  async analyzeProjectVersion(projectId, versionId, analysisOptions) {
    return this.post(`/analysis/project/${projectId}/version/${versionId}`, analysisOptions);
  }

  async analyzeSubchapter(subchapterId, analysisOptions) {
    return this.post(`/analysis/subchapter/${subchapterId}`, analysisOptions);
  }

  // === EVENTI PLOT ===

  async getPlotEvents(projectId = null) {
    const query = projectId ? `?projectId=${projectId}` : '';
    return this.get(`/plot-events${query}`);
  }

  async createPlotEvent(eventData) {
    return this.post('/plot-events', eventData);
  }

  async updatePlotEvent(id, eventData) {
    return this.put(`/plot-events/${id}`, eventData);
  }

  async deletePlotEvent(id) {
    return this.delete(`/plot-events/${id}`);
  }

  // === PERSONAGGI ===

  async getCharacters(projectId = null, versionId = null) {
    let query = '';
    if (projectId && versionId) {
      query = `?projectId=${projectId}&versionId=${versionId}`;
    } else if (projectId) {
      query = `?projectId=${projectId}`;
    }
    return this.get(`/characters${query}`);
  }

  async createCharacter(characterData) {
    return this.post('/characters', characterData);
  }

  async updateCharacter(id, characterData) {
    return this.put(`/characters/${id}`, characterData);
  }

  async deleteCharacter(id) {
    return this.delete(`/characters/${id}`);
  }

  async deleteCharactersByVersion(projectId, versionId) {
    return this.delete(`/characters/version/${projectId}/${versionId}`);
  }

  // === CODEX ===

  async getCodexElements() {
    return this.get('/codex');
  }

  async createCodexElement(elementData) {
    return this.post('/codex', elementData);
  }

  async updateCodexElement(id, elementData) {
    return this.put(`/codex/${id}`, elementData);
  }

  async deleteCodexElement(id) {
    return this.delete(`/codex/${id}`);
  }

  async extractCodexElements(text) {
    return this.post('/codex/extract', { text });
  }

  // === BACKUP ===

  async exportData() {
    return this.get('/backup/export');
  }

  async importData(data, overwrite = false) {
    return this.post('/backup/import', { data, overwrite });
  }

  async createDatabaseBackup() {
    return this.post('/backup/database');
  }

  // === UTILITÀ ===

  async healthCheck() {
    return this.get('/health');
  }

  // Verifica se il server è raggiungibile
  async isServerAvailable() {
    try {
      await this.healthCheck();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Migrazione da localStorage (per compatibilità)
  async migrateFromLocalStorage() {
    try {
      // Ottieni dati dal localStorage
      const localProjects = JSON.parse(localStorage.getItem('writerTool_projects') || '[]');
      const localChapters = JSON.parse(localStorage.getItem('writerTool_chapters') || '[]');
      const localPlotEvents = JSON.parse(localStorage.getItem('writerTool_plotEvents') || '[]');

      let migrated = {
        projects: 0,
        chapters: 0,
        plotEvents: 0
      };

      // Migra progetti
      for (const project of localProjects) {
        try {
          await this.createProject(project);
          migrated.projects++;
        } catch (error) {
          console.warn('Errore migrazione progetto:', project.title, error);
        }
      }

      // Migra capitoli
      for (const chapter of localChapters) {
        try {
          const newChapter = await this.createChapter({
            title: chapter.title,
            description: chapter.description,
            position: chapter.position || 0
          });

          // Migra sottocapitoli
          if (chapter.subchapters) {
            for (const subchapter of chapter.subchapters) {
              await this.createSubchapter(newChapter.id, {
                title: subchapter.title,
                description: subchapter.description,
                text: subchapter.liveVersion?.text || subchapter.text,
                position: subchapter.position || 0
              });
            }
          }

          migrated.chapters++;
        } catch (error) {
          console.warn('Errore migrazione capitolo:', chapter.title, error);
        }
      }

      // Migra eventi plot
      for (const event of localPlotEvents) {
        try {
          await this.createPlotEvent(event);
          migrated.plotEvents++;
        } catch (error) {
          console.warn('Errore migrazione evento plot:', event.title, error);
        }
      }

      return migrated;
    } catch (error) {
      console.error('Errore migrazione da localStorage:', error);
      throw error;
    }
  }
}

// Singleton instance
const apiService = new ApiService();

export default apiService;
