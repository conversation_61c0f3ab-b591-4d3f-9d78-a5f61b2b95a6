import { useState, useCallback } from 'react';

/**
 * Hook per gestione operazioni asincrone
 * Centralizza il pattern loading/error/success usato in tutto il progetto
 * Elimina 100+ duplicazioni di setIsLoading/setError
 */
export const useAsyncOperation = (initialLoading = false) => {
  const [isLoading, setIsLoading] = useState(initialLoading);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  // Esegue un'operazione asincrona con gestione automatica di loading/error
  const execute = useCallback(async (operation, options = {}) => {
    const { 
      onSuccess, 
      onError, 
      clearPreviousData = true,
      showConsoleLog = true 
    } = options;

    if (clearPreviousData) {
      setData(null);
    }
    setIsLoading(true);
    setError(null);

    try {
      const result = await operation();
      setData(result);
      
      if (showConsoleLog) {
        console.log('✅ Operazione completata con successo');
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err.message || 'Errore sconosciuto';
      setError(errorMessage);
      
      if (showConsoleLog) {
        console.error('❌ Errore operazione:', err);
      }
      
      if (onError) {
        onError(err);
      }
      
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Esegue un'operazione asincrona senza gestione errori (per operazioni che gestiscono errori internamente)
  const executeSilent = useCallback(async (operation) => {
    setIsLoading(true);
    try {
      const result = await operation();
      setData(result);
      return result;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Reset dello stato
  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setData(null);
  }, []);

  // Imposta solo l'errore (per errori manuali)
  const setErrorManual = useCallback((errorMessage) => {
    setError(errorMessage);
    setIsLoading(false);
  }, []);

  // Imposta solo i dati (per dati manuali)
  const setDataManual = useCallback((newData) => {
    setData(newData);
    setError(null);
  }, []);

  // Stato derivato
  const hasError = !!error;
  const hasData = !!data;
  const isIdle = !isLoading && !hasError && !hasData;

  return {
    // Stato
    isLoading,
    error,
    data,
    hasError,
    hasData,
    isIdle,

    // Azioni
    execute,
    executeSilent,
    reset,
    setError: setErrorManual,
    setData: setDataManual
  };
};

export default useAsyncOperation;
