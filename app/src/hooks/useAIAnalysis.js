import { useState, useCallback } from 'react';

/**
 * Hook per analisi AI del testo
 * Gestisce analisi personaggi tramite OpenRouter API
 */
export const useAIAnalysis = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState(null);

  // API key dalle variabili d'ambiente
  const openRouterApiKey = process.env.REACT_APP_OPENROUTER_API_KEY || '';

  // Funzione per analizzare i personaggi con AI (OpenRouter)
  const analyzeCharactersWithAI = useCallback(async (text) => {
    if (!openRouterApiKey) {
      console.warn('OpenRouter API key non configurata');
      return {
        characters: [],
        source: 'no-api',
        totalMentions: 0,
        message: 'Configura la chiave API OpenRouter per l\'analisi personaggi avanzata'
      };
    }

    if (!text || text.trim().length === 0) {
      return {
        characters: [],
        source: 'empty-text',
        totalMentions: 0,
        message: 'Nessun testo da analizzare'
      };
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      console.log('🤖 Avvio analisi AI personaggi...');
      
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'WriterTool - Analisi Personaggi'
        },
        body: JSON.stringify({
          model: 'mistralai/mistral-7b-instruct:free',
          messages: [{
            role: 'user',
            content: `Analizza questo testo letterario e identifica tutti i personaggi principali e secondari. Per ogni personaggio fornisci:
1. Nome completo
2. Descrizione fisica dettagliata (altezza, capelli, occhi, corporatura, abbigliamento, etc. Se non presente nel testo, scrivi "Nessuna descrizione fisica")
3. Tratti della personalità
4. Azioni principali che compie
5. Dialoghi che pronuncia (contenuto delle frasi, senza virgolette)
6. Relazioni con altri personaggi
7. Ruolo nella storia
8. Importanza (1-10)

Testo da analizzare:
${text}

Rispondi SOLO con un JSON valido nel seguente formato:
{
  "characters": [
    {
      "name": "Nome Completo",
      "physicalDescription": "Descrizione fisica dettagliata o 'Nessuna descrizione fisica'",
      "personality": "Tratti della personalità",
      "actions": "Azioni principali",
      "dialogues": "Dialoghi pronunciati",
      "relationships": "Relazioni con altri personaggi",
      "role": "Ruolo nella storia",
      "importance": numero_da_1_a_10
    }
  ]
}`
          }],
          max_tokens: 2000,
          temperature: 0.3
        })
      });

      if (!response.ok) {
        throw new Error(`Errore API OpenRouter: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('🤖 Risposta AI ricevuta:', data);

      if (data.choices && data.choices[0] && data.choices[0].message) {
        const aiResponse = data.choices[0].message.content.trim();
        console.log('🤖 Contenuto risposta AI:', aiResponse);

        try {
          // Estrai JSON dalla risposta (potrebbe avere testo extra)
          const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
            throw new Error('Nessun JSON valido trovato nella risposta AI');
          }

          let jsonString = jsonMatch[0];

          // Correzioni automatiche per JSON malformato dall'AI
          console.log('🔧 JSON originale da correggere:', jsonString);

          // Metodo più semplice: aggiungi virgole mancanti tra proprietà
          // Trova pattern: "testo" seguito da newline e spazi e poi "
          jsonString = jsonString.replace(/("(?:[^"\\]|\\.)*")\s*\n\s*("(?:[^"\\]|\\.)*"\s*:)/g, '$1,\n      $2');

          console.log('🔧 JSON corretto:', jsonString);

          const jsonResponse = JSON.parse(jsonString);
          console.log('🤖 JSON parsato:', jsonResponse);

          if (!jsonResponse.characters || !Array.isArray(jsonResponse.characters)) {
            throw new Error('Formato risposta AI non valido: manca array characters');
          }

          // Adatta i personaggi al formato atteso (compatibile con CharactersTab)
          const adaptedCharacters = jsonResponse.characters.map(char => {
            // Converte stringhe in array per compatibilità con il componente
            const actionsArray = char.actions ?
              (typeof char.actions === 'string' ? [char.actions] : char.actions) :
              ['Azioni non specificate'];

            const dialoguesArray = char.dialogues ?
              (typeof char.dialogues === 'string' ? [char.dialogues] : char.dialogues) :
              ['Nessun dialogo'];

            return {
              name: char.name || 'Personaggio sconosciuto',
              physicalDescription: char.physicalDescription || 'Nessuna descrizione fisica',
              personality: char.personality || 'Personalità non specificata',
              actions: actionsArray, // Array per compatibilità
              dialogues: dialoguesArray, // Array per compatibilità
              relationships: char.relationships || 'Relazioni non specificate',
              role: char.role || 'Personaggio',
              importance: char.importance || 5,
              mentions: char.mentions || 1
            };
          });

          console.log('🔍 Personaggi adattati:', adaptedCharacters);

          return {
            characters: adaptedCharacters,
            source: 'ai',
            model: 'mistral-7b',
            totalMentions: adaptedCharacters.reduce((sum, char) => sum + (char.mentions || 0), 0)
          };
        } catch (parseError) {
          console.error('🔍 Errore parsing JSON:', parseError);
          console.error('🔍 Risposta AI che ha fallito:', aiResponse);
          return {
            characters: [],
            source: 'ai-error',
            totalMentions: 0,
            message: 'Errore nel parsing della risposta AI. Riprova.'
          };
        }
      } else {
        throw new Error('Risposta API non valida');
      }
    } catch (error) {
      console.error('Errore nell\'analisi AI:', error);
      setError(error.message);
      return {
        characters: [],
        source: 'ai-error',
        totalMentions: 0,
        message: 'Errore di connessione AI. Verifica la connessione internet e riprova.'
      };
    } finally {
      setIsAnalyzing(false);
    }
  }, [openRouterApiKey]);

  // Funzione per analizzare eventi di trama con AI (futura espansione)
  const analyzePlotWithAI = useCallback(async (text) => {
    if (!openRouterApiKey) {
      return {
        events: [],
        source: 'no-api',
        message: 'API key non configurata'
      };
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      // Implementazione futura per analisi plot con AI
      console.log('🤖 Analisi plot AI non ancora implementata');
      
      return {
        events: [],
        source: 'not-implemented',
        message: 'Analisi plot AI in sviluppo'
      };
    } catch (error) {
      console.error('Errore analisi plot AI:', error);
      setError(error.message);
      return {
        events: [],
        source: 'ai-error',
        message: error.message
      };
    } finally {
      setIsAnalyzing(false);
    }
  }, [openRouterApiKey]);

  // Funzione per analizzare stile con AI (futura espansione)
  const analyzeStyleWithAI = useCallback(async (text) => {
    if (!openRouterApiKey) {
      return {
        style: {},
        source: 'no-api',
        message: 'API key non configurata'
      };
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      // Implementazione futura per analisi stile con AI
      console.log('🤖 Analisi stile AI non ancora implementata');
      
      return {
        style: {},
        source: 'not-implemented',
        message: 'Analisi stile AI in sviluppo'
      };
    } catch (error) {
      console.error('Errore analisi stile AI:', error);
      setError(error.message);
      return {
        style: {},
        source: 'ai-error',
        message: error.message
      };
    } finally {
      setIsAnalyzing(false);
    }
  }, [openRouterApiKey]);

  // Verifica se l'API è configurata
  const isAPIConfigured = () => {
    return !!openRouterApiKey;
  };

  // Ottieni informazioni sull'API
  const getAPIInfo = () => {
    return {
      isConfigured: isAPIConfigured(),
      model: 'mistralai/mistral-7b-instruct:free',
      provider: 'OpenRouter',
      features: ['Analisi Personaggi', 'Analisi Plot (futuro)', 'Analisi Stile (futuro)']
    };
  };

  return {
    // Stato
    isAnalyzing,
    error,
    setError,

    // Funzioni principali
    analyzeCharactersWithAI,
    analyzePlotWithAI,
    analyzeStyleWithAI,

    // Utilità
    isAPIConfigured,
    getAPIInfo,
    openRouterApiKey: !!openRouterApiKey // Nasconde la chiave effettiva
  };
};

export default useAIAnalysis;
