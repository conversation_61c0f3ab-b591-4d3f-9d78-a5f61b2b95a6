import { useState, useEffect, useCallback } from 'react';
import apiService from '../services/api';

export const useServerConnection = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [error, setError] = useState(null);
  const [serverInfo, setServerInfo] = useState(null);

  // Verifica la connessione al server
  const checkConnection = useCallback(async () => {
    setIsChecking(true);
    setError(null);

    try {
      const health = await apiService.healthCheck();
      setIsConnected(true);
      setServerInfo(health);
      console.log('✅ Server connesso:', health);
    } catch (err) {
      setIsConnected(false);
      setServerInfo(null);
      setError(err.message);
      console.warn('❌ Server non raggiungibile:', err.message);
    } finally {
      setIsChecking(false);
    }
  }, []);

  // Verifica connessione all'avvio
  useEffect(() => {
    checkConnection();
  }, [checkConnection]);

  // Verifica periodica della connessione (ogni 30 secondi)
  useEffect(() => {
    const interval = setInterval(checkConnection, 30000);
    return () => clearInterval(interval);
  }, [checkConnection]);

  // Migrazione da localStorage
  const migrateFromLocalStorage = useCallback(async () => {
    if (!isConnected) {
      throw new Error('Server non connesso');
    }

    try {
      const result = await apiService.migrateFromLocalStorage();
      console.log('✅ Migrazione completata:', result);
      return result;
    } catch (error) {
      console.error('❌ Errore migrazione:', error);
      throw error;
    }
  }, [isConnected]);

  return {
    isConnected,
    isChecking,
    error,
    serverInfo,
    checkConnection,
    migrateFromLocalStorage
  };
};
