import { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import apiService from '../services/api';
import { useServerConnection } from './useServerConnection';

/**
 * Hook unificato per la gestione progetti
 * Gestisce automaticamente localStorage vs server in base alla connessione
 * Combina le funzionalità di useProjectManager e useProjectManagerServer
 */
export const useUnifiedProjectManager = () => {
  // Stati unificati
  const [projects, setProjects] = useState([]);
  const [currentProject, setCurrentProject] = useState(null);
  const [currentVersionId, setCurrentVersionId] = useState(null);
  const [projectName, setProjectName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const { isConnected } = useServerConnection();

  // Carica progetti all'avvio e quando cambia la connessione
  useEffect(() => {
    loadProjects();
  }, [isConnected]);

  // Funzione unificata per caricare progetti
  const loadProjects = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        // Carica dal server
        console.log('📡 Caricamento progetti dal server...');
        const serverProjects = await apiService.getProjects();
        
        // Normalizza la struttura per compatibilità
        const normalizedProjects = serverProjects.map(normalizeProjectStructure);
        setProjects(normalizedProjects);
        
        console.log('✅ Progetti caricati dal server:', normalizedProjects.length);
      } else {
        // Carica da localStorage
        console.log('💾 Caricamento progetti da localStorage...');
        const saved = localStorage.getItem('writerTool_projects');
        if (saved) {
          const localProjects = JSON.parse(saved);
          const normalizedProjects = localProjects.map(normalizeProjectStructure);
          setProjects(normalizedProjects);
          console.log('✅ Progetti caricati da localStorage:', normalizedProjects.length);
        } else {
          setProjects([]);
        }
      }
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore caricamento progetti:', err);
      
      // Fallback a localStorage se il server fallisce
      if (isConnected) {
        console.log('🔄 Fallback a localStorage...');
        try {
          const saved = localStorage.getItem('writerTool_projects');
          if (saved) {
            const localProjects = JSON.parse(saved);
            const normalizedProjects = localProjects.map(normalizeProjectStructure);
            setProjects(normalizedProjects);
          }
        } catch (localErr) {
          console.error('❌ Errore anche nel fallback localStorage:', localErr);
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Normalizza la struttura del progetto per compatibilità
  const normalizeProjectStructure = (project) => {
    // Se è già nel formato server (con title), mantienilo
    if (project.title) {
      return {
        ...project,
        name: project.title, // Aggiungi alias per compatibilità
        id: project.id || uuidv4()
      };
    }
    
    // Se è nel formato localStorage (con name), convertilo
    return {
      ...project,
      title: project.name, // Aggiungi alias per compatibilità
      description: project.description || '',
      id: project.id || uuidv4(),
      createdAt: project.created || new Date().toISOString(),
      updatedAt: project.lastModified || new Date().toISOString()
    };
  };

  // Salva progetto (unificato)
  const saveProject = useCallback(async (text, analysis, title) => {
    if (!text?.trim()) {
      throw new Error('Il testo non può essere vuoto');
    }

    setIsLoading(true);
    setError(null);

    try {
      const projectTitle = title || projectName || `Progetto ${new Date().toLocaleDateString()}`;
      const now = new Date().toISOString();

      if (isConnected) {
        // Salva sul server
        console.log('📡 Salvataggio progetto sul server...');
        const projectData = {
          title: projectTitle,
          description: '',
          text: text.trim(),
          analysis: analysis || null
        };

        const newProject = await apiService.createProject(projectData);
        const normalizedProject = normalizeProjectStructure(newProject);
        
        // Aggiorna stato locale
        setProjects(prev => [normalizedProject, ...prev]);
        setCurrentProject(normalizedProject);
        
        if (normalizedProject.versions && normalizedProject.versions.length > 0) {
          setCurrentVersionId(normalizedProject.versions[0].id);
        }

        // Sincronizza con localStorage per backup
        await syncToLocalStorage([normalizedProject, ...projects]);

        console.log('✅ Progetto salvato sul server:', normalizedProject.title);
        return normalizedProject;

      } else {
        // Salva in localStorage
        console.log('💾 Salvataggio progetto in localStorage...');
        const versionId = `v_${Date.now()}`;
        
        const newVersion = {
          id: versionId,
          text: text.trim(),
          timestamp: now,
          wordCount: text.trim().split(/\s+/).length,
          analysis: analysis || null
        };

        const newProject = {
          id: `proj_${Date.now()}`,
          name: projectTitle,
          title: projectTitle, // Alias per compatibilità
          description: '',
          created: now,
          createdAt: now, // Alias per compatibilità
          lastModified: now,
          updatedAt: now, // Alias per compatibilità
          versions: [newVersion]
        };

        const normalizedProject = normalizeProjectStructure(newProject);
        const updatedProjects = [normalizedProject, ...projects];
        
        setProjects(updatedProjects);
        setCurrentProject(normalizedProject);
        setCurrentVersionId(versionId);
        
        localStorage.setItem('writerTool_projects', JSON.stringify(updatedProjects));

        console.log('✅ Progetto salvato in localStorage:', normalizedProject.title);
        return normalizedProject;
      }
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore salvataggio progetto:', err);
      throw err;
    } finally {
      setIsLoading(false);
      setProjectName(''); // Reset nome progetto
    }
  }, [isConnected, projectName, projects]);

  // Carica progetto specifico
  const loadProject = useCallback(async (project, versionId = null) => {
    setIsLoading(true);
    setError(null);

    try {
      let fullProject = project;

      if (isConnected && typeof project === 'string') {
        // Carica dal server se è solo un ID
        fullProject = await apiService.getProject(project);
      }

      const normalizedProject = normalizeProjectStructure(fullProject);
      setCurrentProject(normalizedProject);
      setProjectName(normalizedProject.title || normalizedProject.name);

      // Imposta versione corrente
      if (versionId && normalizedProject.versions) {
        const version = normalizedProject.versions.find(v => v.id === versionId);
        if (version) {
          setCurrentVersionId(versionId);
        }
      } else if (normalizedProject.versions && normalizedProject.versions.length > 0) {
        // Carica l'ultima versione
        const latestVersion = normalizedProject.versions[normalizedProject.versions.length - 1];
        setCurrentVersionId(latestVersion.id);
      }

      console.log('✅ Progetto caricato:', normalizedProject.title || normalizedProject.name);
      return normalizedProject;

    } catch (err) {
      setError(err.message);
      console.error('❌ Errore caricamento progetto:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Sincronizza con localStorage (per backup)
  const syncToLocalStorage = async (projectsToSync) => {
    try {
      localStorage.setItem('writerTool_projects', JSON.stringify(projectsToSync));
      console.log('💾 Progetti sincronizzati con localStorage');
    } catch (err) {
      console.warn('⚠️ Errore sincronizzazione localStorage:', err);
    }
  };

  // Ottieni versione corrente
  const getCurrentVersion = useCallback(() => {
    if (!currentProject || !currentVersionId) return null;
    return currentProject.versions?.find(v => v.id === currentVersionId) || null;
  }, [currentProject, currentVersionId]);

  // Elimina progetto
  const deleteProject = useCallback(async (projectId) => {
    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        // Elimina dal server
        await apiService.deleteProject(projectId);
      }

      // Aggiorna stato locale
      const updatedProjects = projects.filter(p => p.id !== projectId);
      setProjects(updatedProjects);

      // Aggiorna localStorage
      localStorage.setItem('writerTool_projects', JSON.stringify(updatedProjects));

      // Reset progetto corrente se era quello eliminato
      if (currentProject && currentProject.id === projectId) {
        setCurrentProject(null);
        setCurrentVersionId(null);
        setProjectName('');
      }

      console.log('✅ Progetto eliminato:', projectId);
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore eliminazione progetto:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, projects, currentProject]);

  // Crea nuova versione
  const createVersion = useCallback(async (projectId, versionData) => {
    if (!projectId || !versionData?.text) {
      throw new Error('ID progetto e testo sono richiesti');
    }

    setIsLoading(true);
    setError(null);

    try {
      const now = new Date().toISOString();
      const versionId = `v_${Date.now()}`;

      const newVersion = {
        id: versionId,
        name: versionData.name || `Versione ${Date.now()}`,
        text: versionData.text.trim(),
        timestamp: now,
        wordCount: versionData.text.trim().split(/\s+/).length,
        analysis: versionData.analysis || null
      };

      if (isConnected) {
        // Crea versione sul server
        const serverVersion = await apiService.createProjectVersion(projectId, {
          name: newVersion.name,
          text: newVersion.text,
          analysis: newVersion.analysis
        });

        // Usa la versione dal server
        Object.assign(newVersion, serverVersion);
      }

      // Aggiorna progetto locale
      const updatedProjects = projects.map(p => {
        if (p.id === projectId) {
          const updatedProject = {
            ...p,
            versions: [...(p.versions || []), newVersion],
            lastModified: now,
            updatedAt: now
          };

          // Se è il progetto corrente, aggiornalo
          if (currentProject && currentProject.id === projectId) {
            setCurrentProject(updatedProject);
            setCurrentVersionId(newVersion.id);
          }

          return updatedProject;
        }
        return p;
      });

      setProjects(updatedProjects);
      localStorage.setItem('writerTool_projects', JSON.stringify(updatedProjects));

      console.log('✅ Nuova versione creata:', newVersion.id);
      return newVersion;

    } catch (err) {
      setError(err.message);
      console.error('❌ Errore creazione versione:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, projects, currentProject]);

  // Esporta progetto (unificato)
  const exportProject = useCallback(async (projectOrId) => {
    try {
      let project;

      if (typeof projectOrId === 'string') {
        // È un ID, trova il progetto
        if (isConnected) {
          // Carica dal server
          project = await apiService.getProject(projectOrId);
        } else {
          // Trova in localStorage
          project = projects.find(p => p.id === projectOrId);
          if (!project) {
            throw new Error('Progetto non trovato');
          }
        }
      } else {
        // È già un oggetto progetto
        project = projectOrId;
      }

      // Normalizza la struttura per l'export
      const exportData = {
        id: project.id,
        title: project.title || project.name,
        name: project.title || project.name, // Compatibilità
        description: project.description || '',
        created: project.createdAt || project.created,
        created_at: project.createdAt || project.created, // Compatibilità server
        lastModified: project.updatedAt || project.lastModified,
        updated_at: project.updatedAt || project.lastModified, // Compatibilità server
        versions: project.versions || []
      };

      // Crea il file di download
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const fileName = `${(project.title || project.name).replace(/[^a-z0-9]/gi, '_').toLowerCase()}_export.json`;

      const linkElement = document.createElement('a');
      linkElement.href = url;
      linkElement.download = fileName;
      linkElement.style.display = 'none';
      document.body.appendChild(linkElement);
      linkElement.click();
      document.body.removeChild(linkElement);
      URL.revokeObjectURL(url);

      console.log('✅ Progetto esportato:', project.title || project.name);
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore esportazione progetto:', err);
      throw err;
    }
  }, [isConnected, projects]);

  // Importa progetto (unificato)
  const importProject = useCallback(async (fileOrEvent) => {
    setIsLoading(true);
    setError(null);

    try {
      let file;

      // Gestisce sia File object che event
      if (fileOrEvent instanceof File) {
        file = fileOrEvent;
      } else if (fileOrEvent.target && fileOrEvent.target.files) {
        file = fileOrEvent.target.files[0];
        // Reset input se è un event
        fileOrEvent.target.value = '';
      } else {
        throw new Error('File non valido');
      }

      if (!file) {
        throw new Error('Nessun file selezionato');
      }

      const text = await file.text();
      const projectData = JSON.parse(text);

      // Verifica struttura del progetto
      if (!projectData.id || !projectData.versions) {
        throw new Error('File non valido: struttura progetto non riconosciuta');
      }

      const now = new Date().toISOString();
      const importTitle = `${projectData.title || projectData.name} (Importato)`;

      if (isConnected) {
        // Importa sul server
        console.log('📡 Importazione progetto sul server...');

        const importedProject = await apiService.createProject({
          title: importTitle,
          description: projectData.description || '',
          text: projectData.versions?.[0]?.text || '',
          analysis: projectData.versions?.[0]?.analysis || null
        });

        // Crea versioni aggiuntive se presenti
        if (projectData.versions && projectData.versions.length > 1) {
          for (let i = 1; i < projectData.versions.length; i++) {
            const version = projectData.versions[i];
            await apiService.createProjectVersion(importedProject.id, {
              name: version.name || `Versione ${i + 1}`,
              text: version.text || '',
              analysis: version.analysis || null
            });
          }
        }

        // Ricarica progetti per includere quello importato
        await loadProjects();

        console.log('✅ Progetto importato sul server:', importedProject.title);
        return importedProject;

      } else {
        // Importa in localStorage
        console.log('💾 Importazione progetto in localStorage...');

        const newProject = {
          ...projectData,
          id: `proj_${Date.now()}`, // Nuovo ID per evitare conflitti
          name: importTitle,
          title: importTitle, // Alias per compatibilità
          lastModified: now,
          updatedAt: now // Alias per compatibilità
        };

        const normalizedProject = normalizeProjectStructure(newProject);
        const updatedProjects = [normalizedProject, ...projects];

        setProjects(updatedProjects);
        localStorage.setItem('writerTool_projects', JSON.stringify(updatedProjects));

        console.log('✅ Progetto importato in localStorage:', normalizedProject.title);
        return normalizedProject;
      }

    } catch (err) {
      setError(err.message);
      console.error('❌ Errore importazione progetto:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, projects, loadProjects, normalizeProjectStructure]);

  // Funzioni di compatibilità con useProjectManager
  const loadVersion = useCallback((project, versionId) => {
    return loadProject(project, versionId);
  }, [loadProject]);

  const changeProjectVersion = useCallback((projectId, versionId) => {
    const project = projects.find(p => p.id === projectId || p.id === parseInt(projectId));
    if (project && versionId) {
      loadProject(project, versionId);
    } else if (projectId === '') {
      // Reset quando nessun progetto selezionato
      setCurrentProject(null);
      setCurrentVersionId(null);
      setProjectName('');
    }
  }, [projects, loadProject]);

  return {
    // Stato (compatibile con entrambi gli hooks originali)
    projects,
    savedProjects: projects, // Alias per compatibilità con useProjectManager
    currentProject,
    currentVersionId,
    projectName,
    isLoading,
    error,
    isConnected,

    // Setters
    setProjectName,
    setCurrentProject,
    setCurrentVersionId,

    // Azioni principali
    saveProject,
    loadProject,
    loadProjects,
    loadSavedProjects: loadProjects, // Alias per compatibilità
    deleteProject,
    createVersion,
    exportProject,
    importProject,
    getCurrentVersion,

    // Funzioni di compatibilità con useProjectManager
    loadVersion,
    changeProjectVersion,

    // Utilità
    normalizeProjectStructure
  };
};

export default useUnifiedProjectManager;
