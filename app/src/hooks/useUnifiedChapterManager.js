import { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import apiService from '../services/api';
import { useServerConnection } from './useServerConnection';

/**
 * Hook unificato per la gestione capitoli
 * Gestisce automaticamente localStorage vs server in base alla connessione
 * Combina le funzionalità di useChapterManager e useChapterManagerServer
 */
export const useUnifiedChapterManager = () => {
  // Stati unificati
  const [chapters, setChapters] = useState([]);
  const [currentChapter, setCurrentChapter] = useState(null);
  const [currentSubchapter, setCurrentSubchapter] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const { isConnected } = useServerConnection();

  // Carica capitoli all'avvio e quando cambia la connessione
  useEffect(() => {
    loadChapters();
  }, [isConnected]);

  // Funzione unificata per caricare capitoli
  const loadChapters = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        // Carica dal server
        console.log('📡 Caricamento capitoli dal server...');
        const serverChapters = await apiService.getChapters();
        setChapters(serverChapters);

        // Sincronizza con localStorage per backup
        localStorage.setItem('writerTool_chapters', JSON.stringify(serverChapters));
        console.log('✅ Capitoli caricati dal server:', serverChapters.length);
      } else {
        // Carica da localStorage
        console.log('💾 Caricamento capitoli da localStorage...');
        const saved = localStorage.getItem('writerTool_chapters');
        if (saved) {
          const localChapters = JSON.parse(saved);
          setChapters(localChapters);
          console.log('✅ Capitoli caricati da localStorage:', localChapters.length);
        } else {
          setChapters([]);
        }
      }
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore caricamento capitoli:', err);

      // Fallback a localStorage se il server fallisce
      if (isConnected) {
        console.log('🔄 Fallback a localStorage...');
        try {
          const saved = localStorage.getItem('writerTool_chapters');
          if (saved) {
            const localChapters = JSON.parse(saved);
            setChapters(localChapters);
          }
        } catch (localErr) {
          console.error('❌ Errore anche nel fallback localStorage:', localErr);
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Salva capitoli (helper per localStorage)
  const saveChaptersToLocal = (chaptersData) => {
    localStorage.setItem('writerTool_chapters', JSON.stringify(chaptersData));
    setChapters(chaptersData);
  };

  // Crea capitolo (unificato)
  const createChapter = useCallback(async (title, description = '') => {
    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        // Crea sul server
        console.log('📡 Creazione capitolo sul server...');
        const chapterData = {
          title,
          description,
          position: chapters.length
        };

        const newChapter = await apiService.createChapter(chapterData);
        
        // Aggiorna stato locale
        const updatedChapters = [...chapters, newChapter];
        setChapters(updatedChapters);
        
        // Sincronizza con localStorage
        localStorage.setItem('writerTool_chapters', JSON.stringify(updatedChapters));

        console.log('✅ Capitolo creato sul server:', newChapter.title);
        return newChapter;

      } else {
        // Crea in localStorage
        console.log('💾 Creazione capitolo in localStorage...');
        const now = new Date().toISOString();
        
        const newChapter = {
          id: uuidv4(),
          title,
          description,
          position: chapters.length,
          subchapters: [],
          createdAt: now,
          created_at: now, // Alias per compatibilità server
          updatedAt: now,
          updated_at: now // Alias per compatibilità server
        };

        const updatedChapters = [...chapters, newChapter];
        saveChaptersToLocal(updatedChapters);

        console.log('✅ Capitolo creato in localStorage:', newChapter.title);
        return newChapter;
      }
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore creazione capitolo:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, chapters]);

  // Aggiorna capitolo (unificato)
  const updateChapter = useCallback(async (chapterId, updates) => {
    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        // Aggiorna sul server
        await apiService.updateChapter(chapterId, updates);
        
        // Aggiorna stato locale
        const updatedChapters = chapters.map(chapter => 
          chapter.id === chapterId 
            ? { ...chapter, ...updates, updated_at: new Date().toISOString() }
            : chapter
        );
        setChapters(updatedChapters);
        
        // Sincronizza con localStorage
        localStorage.setItem('writerTool_chapters', JSON.stringify(updatedChapters));

        console.log('✅ Capitolo aggiornato sul server:', chapterId);
      } else {
        // Aggiorna in localStorage
        const updatedChapters = chapters.map(chapter => 
          chapter.id === chapterId 
            ? { ...chapter, ...updates, updatedAt: new Date().toISOString() }
            : chapter
        );
        saveChaptersToLocal(updatedChapters);

        console.log('✅ Capitolo aggiornato in localStorage:', chapterId);
      }
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore aggiornamento capitolo:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, chapters]);

  // Elimina capitolo (unificato)
  const deleteChapter = useCallback(async (chapterId) => {
    if (!window.confirm('Sei sicuro di voler eliminare questo capitolo?')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        // Elimina dal server
        await apiService.deleteChapter(chapterId);
      }
      
      // Aggiorna stato locale
      const updatedChapters = chapters.filter(c => c.id !== chapterId);
      setChapters(updatedChapters);
      
      // Aggiorna localStorage
      localStorage.setItem('writerTool_chapters', JSON.stringify(updatedChapters));
      
      // Reset capitolo corrente se era quello eliminato
      if (currentChapter && currentChapter.id === chapterId) {
        setCurrentChapter(null);
        setCurrentSubchapter(null);
      }

      console.log('✅ Capitolo eliminato:', chapterId);
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore eliminazione capitolo:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, chapters, currentChapter]);

  // Carica un capitolo
  const loadChapter = useCallback((chapterId) => {
    const chapter = chapters.find(c => c.id === chapterId);
    setCurrentChapter(chapter);
    setCurrentSubchapter(null);
    return chapter;
  }, [chapters]);

  // Carica un sottocapitolo
  const loadSubchapter = useCallback((chapterId, subchapterId) => {
    const chapter = chapters.find(c => c.id === chapterId);
    const subchapter = chapter?.subchapters?.find(s => s.id === subchapterId);
    
    setCurrentChapter(chapter);
    setCurrentSubchapter(subchapter);
    
    return subchapter;
  }, [chapters]);

  // Crea sottocapitolo (unificato)
  const createSubchapter = useCallback(async (chapterId, title, description = '') => {
    setIsLoading(true);
    setError(null);

    try {
      const now = new Date().toISOString();
      const newSubchapter = {
        id: uuidv4(),
        title,
        description,
        liveVersion: {
          text: '',
          wordCount: 0,
          analysis: null,
          updatedAt: now
        },
        backupVersions: [],
        createdAt: now,
        updatedAt: now
      };

      if (isConnected) {
        // Crea sul server
        const serverSubchapter = await apiService.createSubchapter(chapterId, {
          title,
          description
        });

        // Usa i dati dal server
        Object.assign(newSubchapter, serverSubchapter);
      }

      // Aggiorna stato locale
      const updatedChapters = chapters.map(chapter => {
        if (chapter.id === chapterId) {
          return {
            ...chapter,
            subchapters: [...(chapter.subchapters || []), newSubchapter],
            updatedAt: now
          };
        }
        return chapter;
      });

      setChapters(updatedChapters);
      localStorage.setItem('writerTool_chapters', JSON.stringify(updatedChapters));

      console.log('✅ Sottocapitolo creato:', newSubchapter.title);
      return newSubchapter;

    } catch (err) {
      setError(err.message);
      console.error('❌ Errore creazione sottocapitolo:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, chapters]);

  // Aggiorna sottocapitolo (unificato)
  const updateSubchapter = useCallback(async (chapterId, subchapterId, updates) => {
    setIsLoading(true);
    setError(null);

    try {
      const now = new Date().toISOString();

      if (isConnected) {
        // Aggiorna sul server
        await apiService.updateSubchapter(chapterId, subchapterId, updates);
      }

      // Aggiorna stato locale
      const updatedChapters = chapters.map(chapter => {
        if (chapter.id === chapterId) {
          const updatedSubchapters = chapter.subchapters?.map(sub => {
            if (sub.id === subchapterId) {
              return {
                ...sub,
                ...updates,
                updatedAt: now
              };
            }
            return sub;
          });

          return {
            ...chapter,
            subchapters: updatedSubchapters,
            updatedAt: now
          };
        }
        return chapter;
      });

      setChapters(updatedChapters);
      localStorage.setItem('writerTool_chapters', JSON.stringify(updatedChapters));

      console.log('✅ Sottocapitolo aggiornato:', subchapterId);

    } catch (err) {
      setError(err.message);
      console.error('❌ Errore aggiornamento sottocapitolo:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, chapters]);

  // Elimina sottocapitolo (unificato)
  const deleteSubchapter = useCallback(async (chapterId, subchapterId) => {
    if (!window.confirm('Sei sicuro di voler eliminare questo sottocapitolo?')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        // Elimina dal server
        await apiService.deleteSubchapter(chapterId, subchapterId);
      }

      // Aggiorna stato locale
      const updatedChapters = chapters.map(chapter => {
        if (chapter.id === chapterId) {
          return {
            ...chapter,
            subchapters: chapter.subchapters?.filter(s => s.id !== subchapterId) || [],
            updatedAt: new Date().toISOString()
          };
        }
        return chapter;
      });

      setChapters(updatedChapters);
      localStorage.setItem('writerTool_chapters', JSON.stringify(updatedChapters));

      // Reset sottocapitolo corrente se era quello eliminato
      if (currentSubchapter && currentSubchapter.id === subchapterId) {
        setCurrentSubchapter(null);
      }

      console.log('✅ Sottocapitolo eliminato:', subchapterId);

    } catch (err) {
      setError(err.message);
      console.error('❌ Errore eliminazione sottocapitolo:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, chapters, currentSubchapter]);

  // Concatena tutti i sottocapitoli di un capitolo
  const getCombinedChapterText = useCallback((chapterId) => {
    const chapter = chapters.find(c => c.id === chapterId);
    if (!chapter) return '';

    return chapter.subchapters
      ?.map(sub => `${sub.title}\n\n${sub.liveVersion?.text || ''}`)
      .join('\n\n---\n\n') || '';
  }, [chapters]);

  // Collega versione a sottocapitolo (unificato)
  const linkVersionToSubchapter = useCallback(async (chapterId, subchapterId, projectId, versionId, versionData) => {
    console.log('🔗 Avvio collegamento versione a sottocapitolo:', {
      chapterId,
      subchapterId,
      projectId,
      versionId,
      versionData,
      isConnected
    });

    if (!isConnected) {
      throw new Error('Server non connesso');
    }

    setIsLoading(true);
    setError(null);

    try {
      const linkData = {
        projectId,
        versionId,
        projectTitle: versionData.projectTitle,
        versionName: versionData.versionName,
        text: versionData.text,
        wordCount: versionData.wordCount,
        analysis: versionData.analysis
      };

      await apiService.linkVersionToSubchapter(chapterId, subchapterId, linkData);

      // Ricarica i capitoli per ottenere i dati aggiornati
      await loadChapters();

      console.log('✅ Versione collegata al sottocapitolo:', subchapterId);
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore collegamento versione:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, loadChapters]);

  // Scollega versione da sottocapitolo (unificato)
  const unlinkVersionFromSubchapter = useCallback(async (chapterId, subchapterId) => {
    if (!isConnected) {
      throw new Error('Server non connesso');
    }

    setIsLoading(true);
    setError(null);

    try {
      await apiService.unlinkVersionFromSubchapter(chapterId, subchapterId);

      // Ricarica i capitoli per ottenere i dati aggiornati
      await loadChapters();

      console.log('✅ Versione scollegata dal sottocapitolo:', subchapterId);
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore scollegamento versione:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, loadChapters]);

  // Ottieni statistiche capitolo
  const getChapterStats = useCallback((chapterId) => {
    const chapter = chapters.find(c => c.id === chapterId);
    if (!chapter || !chapter.subchapters) {
      return {
        totalSubchapters: 0,
        totalWords: 0,
        completedSubchapters: 0,
        lastUpdated: null
      };
    }

    const subchapters = chapter.subchapters;
    const totalWords = subchapters.reduce((sum, sub) =>
      sum + (sub.liveVersion?.wordCount || 0), 0
    );
    const completedSubchapters = subchapters.filter(sub =>
      sub.liveVersion?.text && sub.liveVersion.text.trim().length > 0
    ).length;

    const lastUpdated = subchapters.reduce((latest, sub) => {
      const subUpdated = sub.liveVersion?.updatedAt || sub.updatedAt;
      if (!latest || (subUpdated && new Date(subUpdated) > new Date(latest))) {
        return subUpdated;
      }
      return latest;
    }, null);

    return {
      totalSubchapters: subchapters.length,
      totalWords,
      completedSubchapters,
      lastUpdated
    };
  }, [chapters]);

  return {
    // Stato (compatibile con entrambi gli hooks originali)
    chapters,
    currentChapter,
    currentSubchapter,
    isLoading,
    error,
    isConnected,

    // Setters
    setCurrentChapter,
    setCurrentSubchapter,

    // Azioni principali
    loadChapters,
    createChapter,
    updateChapter,
    deleteChapter,
    createSubchapter,
    updateSubchapter,
    deleteSubchapter,
    loadChapter,
    loadSubchapter,
    linkVersionToSubchapter,
    unlinkVersionFromSubchapter,

    // Utilità
    getCombinedChapterText,
    getChapterStats,
    saveChaptersToLocal
  };
};

export default useUnifiedChapterManager;
