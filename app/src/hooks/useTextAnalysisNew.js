import { useState, useCallback } from 'react';
import { useBasicTextAnalysis } from './useBasicTextAnalysis';
import { useAIAnalysis } from './useAIAnalysis';
import { useAdvancedAnalysis } from './useAdvancedAnalysis';
import { useAnalysisStorage } from './useAnalysisStorage';
import { useScoreUtils } from './useScoreUtils';

/**
 * Hook orchestratore per analisi testo
 * Combina tutti gli hooks modulari per fornire un'API unificata
 * Sostituisce il vecchio useTextAnalysis monolitico
 */
export const useTextAnalysis = (text, currentProject = null, currentVersion = null) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState(null);

  // Hooks modulari
  const basicAnalysis = useBasicTextAnalysis(text);
  const aiAnalysis = useAIAnalysis();
  const advancedAnalysis = useAdvancedAnalysis(text);
  const analysisStorage = useAnalysisStorage();
  const { getScoreColor, getScoreBarColor } = useScoreUtils();

  // Genera suggerimenti intelligenti
  const generateSuggestions = useCallback((avgWordsPerSentence, longSentences, shortSentences, repeatedWords) => {
    const suggestions = [];
    
    if (longSentences.length > 0) {
      suggestions.push({
        type: 'warning',
        message: `Trovate ${longSentences.length} frasi molto lunghe. Considera di dividerle.`,
        details: longSentences.slice(0, 3).map(s => s.text?.substring(0, 100) + '...')
      });
    }

    if (shortSentences.length > 0) {
      suggestions.push({
        type: 'info',
        message: `Trovate ${shortSentences.length} frasi molto brevi. Potresti arricchirle.`,
        details: shortSentences.slice(0, 3).map(s => s.text)
      });
    }

    if (repeatedWords.length > 0) {
      suggestions.push({
        type: 'warning',
        message: 'Alcune parole sono ripetute frequentemente.',
        details: repeatedWords.slice(0, 5).map(w => `"${w.word}" (${w.count} volte)`)
      });
    }

    if (avgWordsPerSentence > 20) {
      suggestions.push({
        type: 'warning',
        message: 'Le frasi sono mediamente troppo lunghe per una lettura fluida.'
      });
    }

    return suggestions;
  }, []);

  // Funzione principale di analisi (orchestratore)
  const analyzeText = useCallback(async (textToAnalyze = null, projectForSaving = null, versionForSaving = null) => {
    const targetText = textToAnalyze || text;
    const targetProject = projectForSaving || currentProject;
    const targetVersion = versionForSaving || currentVersion;
    
    if (!targetText.trim()) {
      console.warn('⚠️ Testo vuoto, analisi saltata');
      return;
    }

    setIsAnalyzing(true);

    try {
      console.log('🔍 Avvio analisi completa del testo...');

      // 1. Analisi base (sempre disponibile)
      console.log('📊 Esecuzione analisi base...');
      const basicResult = basicAnalysis.analyzeBasicText();

      // 2. Analisi avanzate (sempre disponibili)
      console.log('🔬 Esecuzione analisi avanzate...');
      const {
        structureAnalysis,
        advancedStyleAnalysis,
        semanticAnalysis,
        literaryMetrics,
        plotEvents
      } = advancedAnalysis;

      // 3. Analisi AI (solo se API configurata)
      console.log('🤖 Esecuzione analisi AI...');
      console.log('🔑 API configurata:', aiAnalysis.isAPIConfigured());

      const charactersAnalysis = aiAnalysis.isAPIConfigured() ?
        await aiAnalysis.analyzeCharactersWithAI(targetText) :
        {
          characters: [],
          source: 'no-api',
          totalMentions: 0,
          message: 'Configura la chiave API OpenRouter per l\'analisi personaggi avanzata'
        };

      console.log('🤖 Risultato analisi personaggi:', charactersAnalysis);

      // 4. Combina tutti i risultati
      const analysisResult = {
        // Dati base
        stats: basicResult.stats,
        scores: basicResult.scores,
        explanations: basicResult.explanations,
        
        // Analisi avanzate
        structure: structureAnalysis,
        advancedStyle: advancedStyleAnalysis,
        semanticAnalysis,
        literaryMetrics,
        
        // Analisi AI e plot
        characters: charactersAnalysis,
        plotEvents,
        
        // Issues e suggerimenti
        issues: {
          longSentences: basicResult.issues.longSentences,
          shortSentences: basicResult.issues.shortSentences,
          repeatedWords: basicResult.issues.repeatedWords,
          suggestions: generateSuggestions(
            basicResult.stats.avgWordsPerSentence,
            basicResult.issues.longSentences,
            basicResult.issues.shortSentences,
            basicResult.issues.repeatedWords
          )
        }
      };

      // 5. Salva l'analisi nello stato
      setAnalysis(analysisResult);

      // 6. Salva automaticamente nel server (se connesso e progetto disponibile)
      if (analysisStorage.isConnected && targetProject) {
        const versionInfo = targetVersion ? ` versione ${targetVersion.id}` : '';
        console.log(`🔄 Salvando dati analisi nel server per progetto: ${targetProject.title}${versionInfo}`);
        await analysisStorage.saveCompleteAnalysis(analysisResult, targetProject, targetVersion);
        console.log('✅ Dati analisi salvati nel server');
      } else {
        console.log('⚠️ Salvataggio server saltato:', {
          isConnected: analysisStorage.isConnected,
          hasProject: !!targetProject,
          hasVersion: !!targetVersion
        });
      }

      console.log('✅ Analisi completa terminata');
      return analysisResult;

    } catch (error) {
      console.error('❌ Errore durante l\'analisi:', error);
      throw error;
    } finally {
      setIsAnalyzing(false);
    }
  }, [text, currentProject, currentVersion, basicAnalysis, aiAnalysis, advancedAnalysis, analysisStorage, generateSuggestions]);

  // Funzioni di compatibilità con il vecchio hook
  const highlightText = basicAnalysis.highlightText;
  const classifySentence = basicAnalysis.classifySentence;

  // Funzioni AI delegate
  const analyzeCharactersWithAI = aiAnalysis.analyzeCharactersWithAI;

  // Funzioni avanzate delegate
  const analyzeTextStructure = () => advancedAnalysis.structureAnalysis;
  const getAdvancedStyleAnalysis = () => advancedAnalysis.advancedStyleAnalysis;
  const getSemanticAnalysis = () => advancedAnalysis.semanticAnalysis;
  const getLiteraryMetrics = () => advancedAnalysis.literaryMetrics;
  const getStructuralAnalysis = () => advancedAnalysis.structureAnalysis; // Alias per compatibilità

  return {
    // Stato principale
    analysis,
    setAnalysis,
    isAnalyzing,

    // Funzione principale
    analyzeText,

    // Funzioni di compatibilità (delegate ai hooks modulari)
    highlightText,
    classifySentence,
    analyzeCharactersWithAI,
    analyzeTextStructure,
    getAdvancedStyleAnalysis,
    getSemanticAnalysis,
    getLiteraryMetrics,
    getStructuralAnalysis,

    // Funzioni helper
    getScoreColor,
    getScoreBarColor,
    generateSuggestions,

    // Accesso ai hooks modulari (per funzionalità avanzate)
    basicAnalysis,
    aiAnalysis,
    advancedAnalysis,
    analysisStorage
  };
};

export default useTextAnalysis;
