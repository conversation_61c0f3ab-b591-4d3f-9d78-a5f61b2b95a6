import { useMemo } from 'react';

/**
 * Hook per analisi avanzate del testo
 * Gestisce struttura, stile, semantica, metriche letterarie e analisi plot
 */
export const useAdvancedAnalysis = (text) => {

  // Funzione helper per analisi progressione tematica (deve essere definita prima del useMemo)
  const analyzeThematicProgression = (paragraphs) => {
    if (paragraphs.length < 3) return 50;

    const themes = ['azione', 'dialogo', 'descrizione', 'riflessione'];
    const progression = paragraphs.map(paragraph => {
      const text = paragraph.toLowerCase();

      // Punteggi per ogni tema
      const scores = {
        azione: (text.match(/\b(corse|saltò|afferrò|colpì|mosse)\b/g) || []).length,
        dialogo: (text.match(/"/g) || []).length / 2,
        descrizione: (text.match(/\b(bello|grande|colore|aspetto|sembrava)\b/g) || []).length,
        riflessione: (text.match(/\b(pensò|sentì|ricordò|immaginò)\b/g) || []).length
      };

      // Trova il tema dominante
      const dominantTheme = Object.entries(scores).reduce((a, b) => scores[a[0]] > scores[b[0]] ? a : b)[0];
      return dominantTheme;
    });

    // Calcola varietà tematica
    const uniqueThemes = new Set(progression).size;
    const varietyScore = (uniqueThemes / themes.length) * 100;

    return Math.round(varietyScore);
  };

  // Funzioni helper (devono essere definite prima dei useMemo)
  const estimateSyllables = (text) => {
    const words = text.toLowerCase().match(/[a-zA-Zàèéìíîòóùú]+/g) || [];
    return words.reduce((total, word) => {
      const vowels = word.match(/[aeiouàèéìíîòóùú]/g) || [];
      return total + Math.max(1, vowels.length);
    }, 0);
  };

  const getFleschLevel = (score) => {
    if (score >= 90) return 'Molto facile';
    if (score >= 80) return 'Facile';
    if (score >= 70) return 'Abbastanza facile';
    if (score >= 60) return 'Standard';
    if (score >= 50) return 'Abbastanza difficile';
    if (score >= 30) return 'Difficile';
    return 'Molto difficile';
  };

  const getGulpeaseLevel = (score) => {
    if (score >= 80) return 'Molto facile';
    if (score >= 60) return 'Facile';
    if (score >= 40) return 'Difficile';
    return 'Molto difficile';
  };

  // Analisi struttura del testo (memoizzata)
  const structureAnalysis = useMemo(() => {
    if (!text || text.trim().length === 0) {
      return {
        scenes: 1,
        paragraphs: 0,
        avgParagraphLength: 0,
        dialogueRatio: 0,
        rhythm: { avgSentenceLength: 0, variation: 0, score: 0 },
        transitions: { score: 0, count: 0 },
        progression: { score: 50, distribution: 'uniforme' },
        overall: 0
      };
    }

    // 1. Rilevamento scene (separatori comuni)
    const sceneBreaks = [
      /\n\s*\*\s*\*\s*\*\s*\n/g,  // ***
      /\n\s*---+\s*\n/g,          // ---
      /\n\s*###\s*\n/g,           // ###
      /\n\s*•\s*•\s*•\s*\n/g      // • • •
    ];

    let sceneCount = 1; // Almeno una scena
    sceneBreaks.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) sceneCount += matches.length;
    });

    // 2. Analisi paragrafi
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const paragraphLengths = paragraphs.map(p => p.split(/\s+/).length);
    const avgParagraphLength = paragraphLengths.reduce((a, b) => a + b, 0) / paragraphs.length;

    // 3. Rapporto dialoghi
    const dialogueMatches = text.match(/["«»]/g) || [];
    const dialogueRatio = (dialogueMatches.length / 2) / Math.max(1, paragraphs.length);

    // 4. Analisi ritmo (variazione lunghezza frasi)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const sentenceLengths = sentences.map(s => s.split(/\s+/).length);
    const avgSentenceLength = sentenceLengths.reduce((a, b) => a + b, 0) / sentenceLengths.length;
    
    const variance = sentenceLengths.reduce((acc, len) => 
      acc + Math.pow(len - avgSentenceLength, 2), 0) / sentenceLengths.length;
    const sentenceVariation = Math.sqrt(variance);

    // 5. Transizioni tra paragrafi
    const transitionWords = ['prima', 'poi', 'dopo', 'infine', 'inizialmente', 'successivamente'];
    const transitionCount = paragraphs.filter(paragraph =>
      transitionWords.some(trans => paragraph.toLowerCase().trim().startsWith(trans))
    ).length;
    const transitionScore = Math.min(100, (transitionCount / Math.max(1, paragraphs.length - 1)) * 100);

    // 6. Progressione tematica
    const thematicProgression = analyzeThematicProgression(paragraphs);

    // Punteggio struttura generale
    const structureScore = Math.round((
      Math.min(100, sceneCount * 20) +
      Math.min(100, (paragraphs.length / Math.max(1, text.split(/\s+/).length / 100)) * 50) +
      Math.min(100, sentenceVariation * 10) +
      transitionScore
    ) / 4);

    return {
      scenes: sceneCount,
      paragraphs: paragraphs.length,
      avgParagraphLength: Math.round(avgParagraphLength),
      dialogueRatio: Math.round(dialogueRatio * 100),
      rhythm: {
        avgSentenceLength: Math.round(avgSentenceLength * 10) / 10,
        variation: Math.round(sentenceVariation * 10) / 10,
        score: Math.min(100, Math.round(sentenceVariation * 10))
      },
      transitions: { score: Math.round(transitionScore), count: transitionCount },
      progression: { score: Math.round(thematicProgression), distribution: 'uniforme' },
      overall: structureScore
    };
  }, [text]);

  // Analisi stile avanzata (memoizzata)
  const advancedStyleAnalysis = useMemo(() => {
    if (!text || text.trim().length === 0) {
      return {
        rhythm: { score: 0, variance: 0 },
        density: { score: 0, ratio: 0 },
        complexity: { score: 0, subordinates: 0, commas: 0 }
      };
    }

    const words = text.trim().split(/\s+/);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim());

    // Calcolo ritmo narrativo (variazione lunghezza frasi)
    const sentenceLengths = sentences.map(s => s.split(/\s+/).length);
    const avgLength = sentenceLengths.reduce((a, b) => a + b, 0) / sentenceLengths.length;
    const variance = sentenceLengths.reduce((acc, len) => acc + Math.pow(len - avgLength, 2), 0) / sentenceLengths.length;
    const rhythmScore = Math.min(100, variance * 2); // Più variazione = ritmo migliore

    // Densità lessicale (parole di contenuto vs funzionali)
    const functionalWords = ['il', 'la', 'di', 'che', 'e', 'a', 'un', 'per', 'in', 'con', 'da', 'su', 'del', 'al'];
    const contentWords = words.filter(word => 
      !functionalWords.includes(word.toLowerCase()) && word.length > 2
    );
    const densityScore = Math.min(100, (contentWords.length / words.length) * 200);

    // Complessità sintattica (virgole, subordinate)
    const commas = (text.match(/,/g) || []).length;
    const subordinates = (text.match(/\b(che|cui|dove|quando|mentre|perché|poiché|sebbene|benché|affinché)\b/gi) || []).length;
    const complexityScore = Math.min(100, ((commas + subordinates * 2) / sentences.length) * 20);

    return {
      rhythm: { score: Math.round(rhythmScore), variance: Math.round(variance * 10) / 10 },
      density: { score: Math.round(densityScore), ratio: Math.round((contentWords.length / words.length) * 100) },
      complexity: { score: Math.round(complexityScore), subordinates, commas }
    };
  }, [text]);

  // Analisi semantica (memoizzata)
  const semanticAnalysis = useMemo(() => {
    if (!text || text.trim().length === 0) {
      return {
        semanticFields: {},
        sentiment: { score: 50, positive: 0, negative: 0 },
        dominantField: 'neutro'
      };
    }

    const words = text.toLowerCase().split(/\s+/).map(w => w.replace(/[^\w]/g, ''));

    // Campi semantici
    const semanticFields = {
      natura: ['albero', 'fiore', 'mare', 'montagna', 'cielo', 'terra', 'acqua', 'vento', 'sole', 'luna'],
      emozioni: ['amore', 'gioia', 'tristezza', 'paura', 'rabbia', 'speranza', 'dolore', 'felicità'],
      azione: ['correre', 'saltare', 'volare', 'cadere', 'alzare', 'muovere', 'spingere', 'tirare'],
      tempo: ['ieri', 'oggi', 'domani', 'sempre', 'mai', 'presto', 'tardi', 'ora', 'momento']
    };

    const fieldCounts = {};
    Object.keys(semanticFields).forEach(field => {
      fieldCounts[field] = words.filter(word => 
        semanticFields[field].some(fieldWord => word.includes(fieldWord))
      ).length;
    });

    // Sentiment analysis basilare
    const positiveWords = ['bello', 'buono', 'grande', 'felice', 'amore', 'gioia', 'speranza', 'pace', 'luce', 'vita'];
    const negativeWords = ['brutto', 'cattivo', 'piccolo', 'triste', 'odio', 'paura', 'dolore', 'guerra', 'buio', 'morte'];

    const positive = words.filter(word => positiveWords.some(pos => word.includes(pos))).length;
    const negative = words.filter(word => negativeWords.some(neg => word.includes(neg))).length;
    const sentimentScore = positive + negative > 0 ? Math.round((positive / (positive + negative)) * 100) : 50;

    return {
      semanticFields: fieldCounts,
      sentiment: { score: sentimentScore, positive, negative },
      dominantField: Object.keys(fieldCounts).reduce((a, b) => fieldCounts[a] > fieldCounts[b] ? a : b)
    };
  }, [text]);

  // Metriche letterarie (memoizzate)
  const literaryMetrics = useMemo(() => {
    if (!text || text.trim().length === 0) {
      return {
        flesch: { score: 0, level: 'Non valutabile' },
        gulpease: { score: 0, level: 'Non valutabile' },
        ttr: { score: 0, uniqueWords: 0 },
        hapax: { count: 0, ratio: 0 },
        syllables: { total: 0, average: 0 }
      };
    }

    const words = text.trim().split(/\s+/);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim());
    const syllables = estimateSyllables(text);

    // Indice Flesch (leggibilità)
    const avgWordsPerSentence = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    const flesch = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    const fleschClamped = Math.max(0, Math.min(100, flesch));

    // Indice Gulpease (leggibilità italiana)
    const avgLettersPerWord = text.replace(/\s/g, '').length / words.length;
    const gulpease = 89 + (300 * sentences.length - 10 * avgLettersPerWord) / words.length;
    const gulpeaseClamped = Math.max(0, Math.min(100, gulpease));

    // Type-Token Ratio (varietà lessicale)
    const uniqueWords = new Set(words.map(w => w.toLowerCase()));
    const ttrScore = (uniqueWords.size / words.length) * 100;

    // Hapax legomena (parole usate una sola volta)
    const wordFreq = {};
    words.forEach(word => {
      const clean = word.toLowerCase().replace(/[^\w]/g, '');
      wordFreq[clean] = (wordFreq[clean] || 0) + 1;
    });
    const hapaxCount = Object.values(wordFreq).filter(count => count === 1).length;
    const hapaxRatio = (hapaxCount / words.length) * 100;

    return {
      flesch: { score: Math.round(fleschClamped), level: getFleschLevel(fleschClamped) },
      gulpease: { score: Math.round(gulpeaseClamped), level: getGulpeaseLevel(gulpeaseClamped) },
      ttr: { score: Math.round(ttrScore), uniqueWords: uniqueWords.size },
      hapax: { count: hapaxCount, ratio: Math.round(hapaxRatio) },
      syllables: { total: syllables, average: Math.round((syllables / words.length) * 10) / 10 }
    };
  }, [text]);

  // Estrazione eventi di trama (memoizzata)
  const plotEvents = useMemo(() => {
    if (!text || text.trim().length === 0) {
      return [];
    }

    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const events = [];

    sentences.forEach((sentence, index) => {
      const trimmed = sentence.trim().toLowerCase();

      // Pattern per misteri/domande aperte
      const mysteryPatterns = [
        /\b(mistero|enigma|segreto|nascosto|celato)\b/,
        /\b(chi|cosa|dove|quando|perché).*\?/,
        /\b(scomparso|sparito|perduto|mancante)\b/,
        /\b(strano|bizzarro|insolito|curioso|inspiegabile)\b/,
        /\b(non sapeva|non capiva|si chiese|si domandò)\b/
      ];

      // Pattern per scoperte/rivelazioni
      const discoveryPatterns = [
        /\b(scoprì|trovò|realizzò|capì|comprese)\b/,
        /\b(rivelò|svelò|mostrò|dimostrò|confessò)\b/,
        /\b(la verità|il segreto|la risposta|la soluzione)\b/,
        /\b(finalmente|improvvisamente|all'improvviso).*\b(capì|comprese|realizzò)\b/
      ];

      // Pattern per conflitti
      const conflictPatterns = [
        /\b(litigò|combatté|attaccò|difese|oppose)\b/,
        /\b(guerra|battaglia|scontro|conflitto|disputa)\b/,
        /\b(nemico|avversario|rivale|antagonista)\b/,
        /\b(minaccia|pericolo|rischio|sfida)\b/
      ];

      // Pattern per personaggi
      const characterPatterns = [
        /\b(incontrò|conobbe|presentò|salutò)\b/,
        /\b(amico|amica|compagno|alleato)\b/,
        /\b(famiglia|padre|madre|fratello|sorella)\b/,
        /\b(maestro|insegnante|guida|mentore)\b/
      ];

      // Estrai personaggi e luoghi dalla frase
      const extractCharacters = (sentence) => {
        const names = sentence.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
        return names.filter(name => name.length > 2 && !['Il', 'La', 'Un', 'Una'].includes(name));
      };

      const extractLocation = (sentence) => {
        const locationWords = ['casa', 'città', 'paese', 'bosco', 'mare', 'montagna', 'strada', 'piazza'];
        const found = locationWords.find(loc => sentence.toLowerCase().includes(loc));
        return found || null;
      };

      // Controlla pattern e crea eventi
      if (mysteryPatterns.some(pattern => pattern.test(trimmed))) {
        events.push({
          type: 'mystery',
          title: `Mistero: ${sentence.trim().substring(0, 50)}...`,
          description: sentence.trim(),
          importance: Math.floor(Math.random() * 3) + 7, // 7-9
          confidence: Math.floor(Math.random() * 20) + 75, // 75-95%
          characters: extractCharacters(sentence),
          location: extractLocation(sentence),
          sentenceIndex: index,
          resolved: false
        });
      } else if (discoveryPatterns.some(pattern => pattern.test(trimmed))) {
        events.push({
          type: 'discovery',
          title: `Scoperta: ${sentence.trim().substring(0, 50)}...`,
          description: sentence.trim(),
          importance: Math.floor(Math.random() * 3) + 8, // 8-10
          confidence: Math.floor(Math.random() * 15) + 85, // 85-100%
          characters: extractCharacters(sentence),
          location: extractLocation(sentence),
          sentenceIndex: index,
          resolved: true
        });
      } else if (conflictPatterns.some(pattern => pattern.test(trimmed))) {
        events.push({
          type: 'conflict',
          title: `Conflitto: ${sentence.trim().substring(0, 50)}...`,
          description: sentence.trim(),
          importance: Math.floor(Math.random() * 3) + 7, // 7-9
          confidence: Math.floor(Math.random() * 20) + 70, // 70-90%
          characters: extractCharacters(sentence),
          location: extractLocation(sentence),
          sentenceIndex: index,
          resolved: false
        });
      } else if (characterPatterns.some(pattern => pattern.test(trimmed))) {
        const characters = extractCharacters(sentence);
        if (characters.length > 0) {
          events.push({
            type: 'character',
            title: `Personaggio: ${characters[0]}`,
            description: sentence.trim(),
            importance: Math.floor(Math.random() * 3) + 6, // 6-8
            confidence: Math.floor(Math.random() * 15) + 80, // 80-95%
            characters: characters,
            location: extractLocation(sentence),
            sentenceIndex: index,
            resolved: false
          });
        }
      }
    });

    return events.slice(0, 15); // Limita a 15 eventi per evitare spam
  }, [text]);

  return {
    // Analisi principali
    structureAnalysis,
    advancedStyleAnalysis,
    semanticAnalysis,
    literaryMetrics,
    plotEvents,

    // Funzioni helper
    analyzeThematicProgression,
    estimateSyllables,
    getFleschLevel,
    getGulpeaseLevel
  };
};

export default useAdvancedAnalysis;
