import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import apiService from '../services/api';
import { useAsyncOperation } from './useAsyncOperation';
import { useServerConnection } from './useServerConnection';

/**
 * Hook per operazioni sui capitoli con gestione unificata loading/error
 * Centralizza le operazioni CRUD sui capitoli usando useAsyncOperation
 */
export const useChapterOperations = () => {
  const { isConnected } = useServerConnection();
  const { isLoading, error, execute } = useAsyncOperation();

  // Carica capitoli dal server o localStorage
  const loadChapters = useCallback(async () => {
    return await execute(async () => {
      if (isConnected) {
        console.log('📡 Caricamento capitoli dal server...');
        const serverChapters = await apiService.getChapters();
        
        // Backup in localStorage
        localStorage.setItem('writerTool_chapters', JSON.stringify(serverChapters));
        console.log('✅ Capitoli caricati dal server:', serverChapters.length);
        return serverChapters;
      } else {
        console.log('💾 Caricamento capitoli da localStorage...');
        const saved = localStorage.getItem('writerTool_chapters');
        if (saved) {
          const localChapters = JSON.parse(saved);
          console.log('✅ Capitoli caricati da localStorage:', localChapters.length);
          return localChapters;
        }
        return [];
      }
    }, {
      onError: (err) => {
        // Fallback a localStorage se server fallisce
        if (isConnected) {
          console.log('🔄 Fallback a localStorage...');
          try {
            const saved = localStorage.getItem('writerTool_chapters');
            return saved ? JSON.parse(saved) : [];
          } catch (localErr) {
            console.error('❌ Errore anche nel fallback localStorage:', localErr);
            return [];
          }
        }
        return [];
      }
    });
  }, [isConnected, execute]);

  // Crea nuovo capitolo
  const createChapter = useCallback(async (title, description = '') => {
    return await execute(async () => {
      const now = new Date().toISOString();
      const newChapter = {
        id: uuidv4(),
        title: title.trim(),
        description: description.trim(),
        subchapters: [],
        createdAt: now,
        updatedAt: now
      };

      if (isConnected) {
        console.log('📡 Creazione capitolo sul server...');
        const serverChapter = await apiService.createChapter(newChapter);
        console.log('✅ Capitolo creato sul server:', serverChapter.id);
        return serverChapter;
      } else {
        console.log('💾 Creazione capitolo in localStorage...');
        const saved = localStorage.getItem('writerTool_chapters');
        const chapters = saved ? JSON.parse(saved) : [];
        chapters.push(newChapter);
        localStorage.setItem('writerTool_chapters', JSON.stringify(chapters));
        console.log('✅ Capitolo creato in localStorage:', newChapter.id);
        return newChapter;
      }
    });
  }, [isConnected, execute]);

  // Aggiorna capitolo
  const updateChapter = useCallback(async (chapterId, updates) => {
    return await execute(async () => {
      const now = new Date().toISOString();
      const updateData = { ...updates, updatedAt: now };

      if (isConnected) {
        await apiService.updateChapter(chapterId, updateData);
        console.log('✅ Capitolo aggiornato sul server:', chapterId);
      } else {
        const saved = localStorage.getItem('writerTool_chapters');
        if (saved) {
          const chapters = JSON.parse(saved);
          const chapterIndex = chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            chapters[chapterIndex] = { ...chapters[chapterIndex], ...updateData };
            localStorage.setItem('writerTool_chapters', JSON.stringify(chapters));
            console.log('✅ Capitolo aggiornato in localStorage:', chapterId);
          }
        }
      }
      return updateData;
    });
  }, [isConnected, execute]);

  // Elimina capitolo
  const deleteChapter = useCallback(async (chapterId) => {
    return await execute(async () => {
      if (isConnected) {
        await apiService.deleteChapter(chapterId);
        console.log('✅ Capitolo eliminato dal server:', chapterId);
      } else {
        const saved = localStorage.getItem('writerTool_chapters');
        if (saved) {
          const chapters = JSON.parse(saved);
          const filteredChapters = chapters.filter(c => c.id !== chapterId);
          localStorage.setItem('writerTool_chapters', JSON.stringify(filteredChapters));
          console.log('✅ Capitolo eliminato da localStorage:', chapterId);
        }
      }
      return chapterId;
    });
  }, [isConnected, execute]);

  // Crea sottocapitolo
  const createSubchapter = useCallback(async (chapterId, title, content = '', description = '') => {
    return await execute(async () => {
      const now = new Date().toISOString();
      const newSubchapter = {
        id: uuidv4(),
        title: title.trim(),
        content: content,
        description: description.trim(),
        createdAt: now,
        updatedAt: now,
        linkedVersions: []
      };

      if (isConnected) {
        const serverSubchapter = await apiService.createSubchapter(chapterId, newSubchapter);
        console.log('✅ Sottocapitolo creato sul server:', serverSubchapter.id);
        return serverSubchapter;
      } else {
        const saved = localStorage.getItem('writerTool_chapters');
        if (saved) {
          const chapters = JSON.parse(saved);
          const chapterIndex = chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            chapters[chapterIndex].subchapters.push(newSubchapter);
            chapters[chapterIndex].updatedAt = now;
            localStorage.setItem('writerTool_chapters', JSON.stringify(chapters));
            console.log('✅ Sottocapitolo creato in localStorage:', newSubchapter.id);
          }
        }
        return newSubchapter;
      }
    });
  }, [isConnected, execute]);

  return {
    // Stato
    isLoading,
    error,

    // Operazioni
    loadChapters,
    createChapter,
    updateChapter,
    deleteChapter,
    createSubchapter
  };
};

export default useChapterOperations;
