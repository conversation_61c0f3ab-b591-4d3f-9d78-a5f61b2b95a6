import { useState, useCallback } from 'react';

const useSyncManager = () => {
  const [syncHistory, setSyncHistory] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // Ottieni tutti i progetti salvati
  const getAllProjects = useCallback(() => {
    try {
      const projects = localStorage.getItem('writerTool_projects');
      return projects ? JSON.parse(projects) : [];
    } catch (error) {
      console.error('Errore nel caricamento progetti:', error);
      return [];
    }
  }, []);

  // Ottieni tutti i capitoli salvati
  const getAllChapters = useCallback(() => {
    try {
      const chapters = localStorage.getItem('writerTool_chapters');
      return chapters ? JSON.parse(chapters) : [];
    } catch (error) {
      console.error('Errore nel caricamento capitoli:', error);
      return [];
    }
  }, []);

  // Trova un sottocapitolo specifico in un progetto/versione
  const findSubchapterInProject = useCallback((projectId, versionId, subchapterId) => {
    const projects = getAllProjects();
    const project = projects.find(p => p.id === projectId);
    
    if (!project) return null;
    
    const version = project.versions.find(v => v.id === versionId);
    if (!version) return null;

    // Cerca il sottocapitolo in tutti i capitoli della versione
    for (const chapter of version.chapters || []) {
      const subchapter = chapter.subchapters?.find(s => s.id === subchapterId);
      if (subchapter) {
        return {
          project,
          version,
          chapter,
          subchapter
        };
      }
    }
    
    return null;
  }, [getAllProjects]);

  // Trova un sottocapitolo nei capitoli live
  const findSubchapterInChapters = useCallback((chapterId, subchapterId) => {
    const chapters = getAllChapters();
    const chapter = chapters.find(c => c.id === chapterId);
    
    if (!chapter) return null;
    
    const subchapter = chapter.subchapters?.find(s => s.id === subchapterId);
    if (!subchapter) return null;
    
    return { chapter, subchapter };
  }, [getAllChapters]);

  // Aggiorna sottocapitolo live da versione progetto
  const updateSubchapterFromVersion = useCallback(async (
    targetChapterId, 
    targetSubchapterId, 
    sourceProjectId, 
    sourceVersionId, 
    sourceSubchapterId,
    mode = 'replace'
  ) => {
    setIsProcessing(true);
    
    try {
      // Trova il sottocapitolo sorgente
      const sourceData = findSubchapterInProject(sourceProjectId, sourceVersionId, sourceSubchapterId);
      if (!sourceData) {
        throw new Error('Sottocapitolo sorgente non trovato');
      }

      // Trova il sottocapitolo destinazione
      const targetData = findSubchapterInChapters(targetChapterId, targetSubchapterId);
      if (!targetData) {
        throw new Error('Sottocapitolo destinazione non trovato');
      }

      const sourceSubchapter = sourceData.subchapter;
      const targetSubchapter = targetData.subchapter;

      // Applica l'aggiornamento in base alla modalità
      let updatedText = targetSubchapter.liveVersion?.text || '';

      switch (mode) {
        case 'replace':
          updatedText = sourceSubchapter.text || sourceSubchapter.liveVersion?.text || '';
          break;
        case 'append':
          updatedText = (targetSubchapter.liveVersion?.text || '') + '\n\n' + (sourceSubchapter.text || sourceSubchapter.liveVersion?.text || '');
          break;
        case 'prepend':
          updatedText = (sourceSubchapter.text || sourceSubchapter.liveVersion?.text || '') + '\n\n' + (targetSubchapter.liveVersion?.text || '');
          break;
        default:
          updatedText = sourceSubchapter.text || sourceSubchapter.liveVersion?.text || '';
      }

      // Aggiorna il sottocapitolo
      const chapters = getAllChapters();
      const updatedChapters = chapters.map(chapter => {
        if (chapter.id === targetChapterId) {
          return {
            ...chapter,
            subchapters: chapter.subchapters.map(sub => {
              if (sub.id === targetSubchapterId) {
                return {
                  ...sub,
                  liveVersion: {
                    ...sub.liveVersion,
                    text: updatedText,
                    wordCount: updatedText.split(/\s+/).filter(word => word.length > 0).length,
                    updatedAt: new Date().toISOString(),
                    // Metadati di sincronizzazione
                    contentOrigin: {
                      sourceProject: sourceProjectId,
                      sourceProjectTitle: sourceData.project.title,
                      sourceVersion: sourceVersionId,
                      sourceVersionName: sourceData.version.name,
                      sourceSubchapter: sourceSubchapterId,
                      syncMode: mode,
                      syncedAt: new Date().toISOString()
                    }
                  },
                  updatedAt: new Date().toISOString()
                };
              }
              return sub;
            }),
            updatedAt: new Date().toISOString()
          };
        }
        return chapter;
      });

      // Salva i capitoli aggiornati
      localStorage.setItem('writerTool_chapters', JSON.stringify(updatedChapters));

      // Registra nella cronologia di sincronizzazione
      const syncRecord = {
        id: Date.now().toString(),
        action: 'updateFromVersion',
        timestamp: new Date().toISOString(),
        source: {
          projectId: sourceProjectId,
          projectTitle: sourceData.project.title,
          versionId: sourceVersionId,
          versionName: sourceData.version.name,
          subchapterId: sourceSubchapterId,
          subchapterTitle: sourceSubchapter.title
        },
        target: {
          chapterId: targetChapterId,
          chapterTitle: targetData.chapter.title,
          subchapterId: targetSubchapterId,
          subchapterTitle: targetSubchapter.title
        },
        mode,
        success: true
      };

      setSyncHistory(prev => [syncRecord, ...prev.slice(0, 49)]); // Mantieni ultime 50

      return {
        success: true,
        updatedSubchapter: updatedChapters
          .find(c => c.id === targetChapterId)
          ?.subchapters.find(s => s.id === targetSubchapterId)
      };

    } catch (error) {
      console.error('Errore nella sincronizzazione:', error);
      
      const errorRecord = {
        id: Date.now().toString(),
        action: 'updateFromVersion',
        timestamp: new Date().toISOString(),
        error: error.message,
        success: false
      };
      
      setSyncHistory(prev => [errorRecord, ...prev.slice(0, 49)]);
      
      return {
        success: false,
        error: error.message
      };
    } finally {
      setIsProcessing(false);
    }
  }, [findSubchapterInProject, findSubchapterInChapters, getAllChapters]);

  // Esporta sottocapitolo live verso versione progetto
  const exportSubchapterToVersion = useCallback(async (
    sourceChapterId,
    sourceSubchapterId,
    targetProjectId,
    targetVersionId
  ) => {
    setIsProcessing(true);
    
    try {
      // Trova il sottocapitolo sorgente nei capitoli live
      const sourceData = findSubchapterInChapters(sourceChapterId, sourceSubchapterId);
      if (!sourceData) {
        throw new Error('Sottocapitolo sorgente non trovato');
      }

      // Carica i progetti
      const projects = getAllProjects();
      const projectIndex = projects.findIndex(p => p.id === targetProjectId);
      if (projectIndex === -1) {
        throw new Error('Progetto destinazione non trovato');
      }

      const versionIndex = projects[projectIndex].versions.findIndex(v => v.id === targetVersionId);
      if (versionIndex === -1) {
        throw new Error('Versione destinazione non trovata');
      }

      // Aggiorna la versione del progetto
      const sourceSubchapter = sourceData.subchapter;
      
      // Trova o crea il capitolo nella versione
      let targetChapter = projects[projectIndex].versions[versionIndex].chapters?.find(
        c => c.title === sourceData.chapter.title
      );
      
      if (!targetChapter) {
        // Crea nuovo capitolo nella versione
        targetChapter = {
          id: Date.now().toString(),
          title: sourceData.chapter.title,
          description: sourceData.chapter.description || '',
          subchapters: []
        };
        
        if (!projects[projectIndex].versions[versionIndex].chapters) {
          projects[projectIndex].versions[versionIndex].chapters = [];
        }
        projects[projectIndex].versions[versionIndex].chapters.push(targetChapter);
      }

      // Trova o crea il sottocapitolo nella versione
      const subchapterIndex = targetChapter.subchapters.findIndex(
        s => s.title === sourceSubchapter.title
      );
      
      if (subchapterIndex !== -1) {
        // Aggiorna sottocapitolo esistente
        targetChapter.subchapters[subchapterIndex] = {
          ...targetChapter.subchapters[subchapterIndex],
          text: sourceSubchapter.text,
          wordCount: sourceSubchapter.wordCount,
          updatedAt: new Date().toISOString()
        };
      } else {
        // Crea nuovo sottocapitolo
        targetChapter.subchapters.push({
          ...sourceSubchapter,
          id: Date.now().toString(),
          updatedAt: new Date().toISOString()
        });
      }

      // Salva i progetti aggiornati
      localStorage.setItem('writerTool_projects', JSON.stringify(projects));

      const syncRecord = {
        id: Date.now().toString(),
        action: 'exportToVersion',
        timestamp: new Date().toISOString(),
        source: {
          chapterId: sourceChapterId,
          chapterTitle: sourceData.chapter.title,
          subchapterId: sourceSubchapterId,
          subchapterTitle: sourceSubchapter.title
        },
        target: {
          projectId: targetProjectId,
          projectTitle: projects[projectIndex].title,
          versionId: targetVersionId,
          versionName: projects[projectIndex].versions[versionIndex].name
        },
        success: true
      };

      setSyncHistory(prev => [syncRecord, ...prev.slice(0, 49)]);

      return { success: true };

    } catch (error) {
      console.error('Errore nell\'esportazione:', error);
      
      const errorRecord = {
        id: Date.now().toString(),
        action: 'exportToVersion',
        timestamp: new Date().toISOString(),
        error: error.message,
        success: false
      };
      
      setSyncHistory(prev => [errorRecord, ...prev.slice(0, 49)]);
      
      return {
        success: false,
        error: error.message
      };
    } finally {
      setIsProcessing(false);
    }
  }, [findSubchapterInChapters, getAllProjects]);

  // Ottieni opzioni di sincronizzazione per un sottocapitolo
  const getSyncOptions = useCallback((chapterId, subchapterId) => {
    const projects = getAllProjects();
    
    const options = {
      availableVersions: [],
      availableProjects: [],
      currentSubchapter: null
    };

    // Trova il sottocapitolo corrente
    const currentData = findSubchapterInChapters(chapterId, subchapterId);
    if (currentData) {
      options.currentSubchapter = currentData.subchapter;
    }

    // Raccogli tutte le versioni disponibili
    projects.forEach(project => {
      project.versions.forEach(version => {
        version.chapters?.forEach(chapter => {
          chapter.subchapters?.forEach(subchapter => {
            options.availableVersions.push({
              projectId: project.id,
              projectTitle: project.title,
              versionId: version.id,
              versionName: version.name,
              chapterId: chapter.id,
              chapterTitle: chapter.title,
              subchapterId: subchapter.id,
              subchapterTitle: subchapter.title,
              wordCount: subchapter.wordCount || 0,
              updatedAt: subchapter.updatedAt
            });
          });
        });
      });
    });

    // Raccogli tutti i progetti per esportazione
    projects.forEach(project => {
      project.versions.forEach(version => {
        options.availableProjects.push({
          projectId: project.id,
          projectTitle: project.title,
          versionId: version.id,
          versionName: version.name,
          updatedAt: version.updatedAt || project.updatedAt
        });
      });
    });

    return options;
  }, [getAllProjects, getAllChapters, findSubchapterInChapters]);

  return {
    // Stato
    isProcessing,
    syncHistory,
    
    // Funzioni principali
    updateSubchapterFromVersion,
    exportSubchapterToVersion,
    getSyncOptions,
    
    // Utility
    getAllProjects,
    getAllChapters,
    findSubchapterInProject,
    findSubchapterInChapters
  };
};

export default useSyncManager;
