import { useState, useEffect } from 'react';

/**
 * Hook per debounce di valori
 * Ritarda l'aggiornamento del valore fino a quando non passa un certo tempo senza cambiamenti
 * 
 * @param {any} value - Il valore da debounceare
 * @param {number} delay - Il ritardo in millisecondi (default: 500ms)
 * @returns {any} Il valore debouncato
 */
export const useDebounce = (value, delay = 500) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    // Imposta un timer per aggiornare il valore debouncato
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Cleanup: cancella il timer se il valore cambia prima che scada
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Hook per debounce di funzioni
 * Ritarda l'esecuzione di una funzione fino a quando non passa un certo tempo senza chiamate
 * 
 * @param {Function} func - La funzione da debounceare
 * @param {number} delay - Il ritardo in millisecondi (default: 500ms)
 * @returns {Function} La funzione debouncata
 */
export const useDebouncedCallback = (func, delay = 500) => {
  const [debounceTimer, setDebounceTimer] = useState(null);

  const debouncedFunction = (...args) => {
    // Cancella il timer precedente se esiste
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Imposta un nuovo timer
    const newTimer = setTimeout(() => {
      func(...args);
    }, delay);

    setDebounceTimer(newTimer);
  };

  // Cleanup quando il componente viene smontato
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return debouncedFunction;
};

export default useDebounce;
