import { useState, useCallback } from 'react';
import apiService from '../services/api';
import { useServerConnection } from './useServerConnection';

/**
 * Hook per gestione salvataggio analisi
 * Gestisce salvataggio personaggi ed eventi plot sul server
 */
export const useAnalysisStorage = () => {
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(null);

  const { isConnected } = useServerConnection();

  // Salva personaggi nel server con progetto e versione specifici
  const saveCharactersToServerWithProject = useCallback(async (charactersAnalysis, targetProject, targetVersion = null) => {
    if (!isConnected || !targetProject) {
      console.log('⚠️ Salvataggio personaggi saltato: server non connesso o progetto mancante');
      return;
    }

    if (!charactersAnalysis?.characters || charactersAnalysis.characters.length === 0) {
      console.log('⚠️ Nessun personaggio da salvare');
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      const versionInfo = targetVersion ? ` versione ${targetVersion.id}` : '';
      console.log(`💾 Salvando personaggi nel server per progetto: ${targetProject.title}${versionInfo}`);

      for (const character of charactersAnalysis.characters) {
        const characterData = {
          projectId: targetProject.id,
          versionId: targetVersion?.id || null, // Collega alla versione specifica
          name: character.name,
          physicalDescription: character.physicalDescription || 'Nessuna descrizione fisica',
          personality: character.personality || 'Personalità non specificata',
          actions: Array.isArray(character.actions) ? character.actions.join('; ') : (character.actions || 'Azioni non specificate'),
          dialogues: Array.isArray(character.dialogues) ? character.dialogues.join('; ') : (character.dialogues || 'Nessun dialogo'),
          relationships: character.relationships || 'Relazioni non specificate',
          role: character.role || 'Personaggio',
          importance: character.importance || 5,
          mentions: character.mentions || 1,
          source: charactersAnalysis.source || 'analysis',
          automatic: true
        };

        await apiService.createCharacter(characterData);
        console.log('✅ Personaggio salvato:', character.name);
      }

      console.log('✅ Tutti i personaggi salvati nel server');
    } catch (error) {
      console.error('❌ Errore salvataggio personaggi:', error);
      setSaveError(`Errore salvataggio personaggi: ${error.message}`);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [isConnected]);

  // Salva eventi plot nel server con progetto specifico
  const savePlotEventsToServerWithProject = useCallback(async (plotEvents, targetProject) => {
    if (!isConnected || !targetProject) {
      console.log('⚠️ Salvataggio eventi saltato: server non connesso o progetto mancante');
      return;
    }

    if (!plotEvents || plotEvents.length === 0) {
      console.log('⚠️ Nessun evento plot da salvare');
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      console.log('💾 Salvando eventi plot nel server per progetto:', targetProject.title);
      
      for (const event of plotEvents) {
        const eventData = {
          projectId: targetProject.id,
          title: event.title,
          description: event.description,
          type: event.type || 'general',
          importance: event.importance || 5,
          chapterReference: null, // Potrebbe essere aggiunto in futuro
          automatic: true
        };

        await apiService.createPlotEvent(eventData);
        console.log('✅ Evento plot salvato:', event.title);
      }

      console.log('✅ Tutti gli eventi plot salvati nel server');
    } catch (error) {
      console.error('❌ Errore salvataggio eventi plot:', error);
      setSaveError(`Errore salvataggio eventi: ${error.message}`);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [isConnected]);

  // Salva analisi completa (personaggi + eventi)
  const saveCompleteAnalysis = useCallback(async (analysisResult, targetProject, targetVersion = null) => {
    if (!isConnected || !targetProject) {
      console.log('⚠️ Salvataggio analisi saltato: server non connesso o progetto mancante');
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      const versionInfo = targetVersion ? ` versione ${targetVersion.id}` : '';
      console.log(`🔄 Salvando analisi completa nel server per progetto: ${targetProject.title}${versionInfo}`);

      // Pulisci personaggi precedenti per questa versione specifica
      if (targetVersion) {
        console.log('🧹 Pulizia personaggi precedenti per questa versione...');
        await apiService.deleteCharactersByVersion(targetProject.id, targetVersion.id);
      }

      // Salva personaggi se presenti (collegati alla versione)
      if (analysisResult.characters) {
        await saveCharactersToServerWithProject(analysisResult.characters, targetProject, targetVersion);
      }

      // Salva eventi plot se presenti (rimangono a livello progetto)
      if (analysisResult.plotEvents) {
        await savePlotEventsToServerWithProject(analysisResult.plotEvents, targetProject);
      }

      console.log('✅ Analisi completa salvata nel server');
    } catch (error) {
      console.error('❌ Errore salvataggio analisi completa:', error);
      setSaveError(`Errore salvataggio analisi: ${error.message}`);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [isConnected, saveCharactersToServerWithProject, savePlotEventsToServerWithProject]);

  // Carica personaggi dal server per un progetto/versione
  const loadCharactersFromServer = useCallback(async (projectId, versionId = null) => {
    if (!isConnected || !projectId) {
      console.log('⚠️ Caricamento personaggi saltato: server non connesso o progetto mancante');
      return [];
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      const versionInfo = versionId ? ` versione ${versionId}` : '';
      console.log(`📡 Caricando personaggi dal server per progetto: ${projectId}${versionInfo}`);

      const characters = await apiService.getCharacters(projectId, versionId);

      console.log('✅ Personaggi caricati dal server:', characters.length);
      return characters;
    } catch (error) {
      console.error('❌ Errore caricamento personaggi:', error);
      setSaveError(`Errore caricamento personaggi: ${error.message}`);
      return [];
    } finally {
      setIsSaving(false);
    }
  }, [isConnected]);

  // Carica eventi plot dal server per un progetto
  const loadPlotEventsFromServer = useCallback(async (projectId) => {
    if (!isConnected || !projectId) {
      console.log('⚠️ Caricamento eventi saltato: server non connesso o progetto mancante');
      return [];
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      console.log('📡 Caricando eventi plot dal server per progetto:', projectId);
      
      const events = await apiService.getPlotEvents(projectId);
      
      console.log('✅ Eventi plot caricati dal server:', events.length);
      return events;
    } catch (error) {
      console.error('❌ Errore caricamento eventi plot:', error);
      setSaveError(`Errore caricamento eventi: ${error.message}`);
      return [];
    } finally {
      setIsSaving(false);
    }
  }, [isConnected]);

  // Elimina tutti i dati di analisi per un progetto
  const clearAnalysisForProject = useCallback(async (projectId) => {
    if (!isConnected || !projectId) {
      console.log('⚠️ Pulizia analisi saltata: server non connesso o progetto mancante');
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      console.log('🗑️ Eliminando dati analisi per progetto:', projectId);
      
      // Elimina personaggi
      const characters = await apiService.getCharacters(projectId);
      for (const character of characters) {
        await apiService.deleteCharacter(character.id);
      }

      // Elimina eventi plot
      const events = await apiService.getPlotEvents(projectId);
      for (const event of events) {
        await apiService.deletePlotEvent(event.id);
      }

      console.log('✅ Dati analisi eliminati per progetto:', projectId);
    } catch (error) {
      console.error('❌ Errore eliminazione dati analisi:', error);
      setSaveError(`Errore eliminazione dati: ${error.message}`);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [isConnected]);

  // Verifica se il salvataggio è disponibile
  const isSaveAvailable = () => {
    return isConnected;
  };

  // Ottieni statistiche di salvataggio
  const getSaveStats = useCallback(async (projectId) => {
    if (!isConnected || !projectId) {
      return {
        charactersCount: 0,
        eventsCount: 0,
        lastSaved: null
      };
    }

    try {
      const [characters, events] = await Promise.all([
        apiService.getCharacters(projectId),
        apiService.getPlotEvents(projectId)
      ]);

      const lastSaved = [...characters, ...events]
        .map(item => item.created_at || item.createdAt)
        .filter(date => date)
        .sort()
        .pop();

      return {
        charactersCount: characters.length,
        eventsCount: events.length,
        lastSaved
      };
    } catch (error) {
      console.error('❌ Errore recupero statistiche:', error);
      return {
        charactersCount: 0,
        eventsCount: 0,
        lastSaved: null
      };
    }
  }, [isConnected]);

  return {
    // Stato
    isSaving,
    saveError,
    setSaveError,
    isConnected,

    // Funzioni di salvataggio
    saveCharactersToServerWithProject,
    savePlotEventsToServerWithProject,
    saveCompleteAnalysis,

    // Funzioni di caricamento
    loadCharactersFromServer,
    loadPlotEventsFromServer,

    // Funzioni di gestione
    clearAnalysisForProject,
    getSaveStats,

    // Utilità
    isSaveAvailable
  };
};

export default useAnalysisStorage;
