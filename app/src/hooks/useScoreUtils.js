/**
 * Hook per utilità di punteggi e colori
 * Centralizza le funzioni getScoreColor e getScoreBarColor usate in tutto il progetto
 * Elimina 43+ duplicazioni in 7+ file
 */
export const useScoreUtils = () => {
  
  // Colore del testo basato sul punteggio
  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Colore della barra di progresso basato sul punteggio
  const getScoreBarColor = (score) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Colore di sfondo basato sul punteggio
  const getScoreBgColor = (score) => {
    if (score >= 80) return 'bg-green-50 border-green-200';
    if (score >= 60) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  // Etichetta testuale del punteggio
  const getScoreLabel = (score) => {
    if (score >= 90) return 'Eccellente';
    if (score >= 80) return 'Ottimo';
    if (score >= 70) return 'Buono';
    if (score >= 60) return 'Sufficiente';
    if (score >= 50) return 'Migliorabile';
    if (score >= 30) return 'Scarso';
    return 'Molto scarso';
  };

  // Icona basata sul punteggio
  const getScoreIcon = (score) => {
    if (score >= 80) return '🟢';
    if (score >= 60) return '🟡';
    return '🔴';
  };

  // Funzione combinata per ottenere tutti gli stili di un punteggio
  const getScoreStyles = (score) => {
    return {
      textColor: getScoreColor(score),
      barColor: getScoreBarColor(score),
      bgColor: getScoreBgColor(score),
      label: getScoreLabel(score),
      icon: getScoreIcon(score),
      score: score
    };
  };

  return {
    getScoreColor,
    getScoreBarColor,
    getScoreBgColor,
    getScoreLabel,
    getScoreIcon,
    getScoreStyles
  };
};

export default useScoreUtils;
