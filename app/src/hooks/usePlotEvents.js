import { useState, useEffect, useCallback } from 'react';
import apiService from '../services/api';
import { useServerConnection } from './useServerConnection';

export const usePlotEvents = () => {
  const [plotEvents, setPlotEvents] = useState([]);
  const [showEventForm, setShowEventForm] = useState(false);
  const [editingEvent, setEditingEvent] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const { isConnected } = useServerConnection();

  // Carica eventi dal server
  const loadPlotEvents = useCallback(async (projectId = null) => {
    if (!isConnected) {
      // Fallback: carica da cache locale se server non disponibile
      const cached = localStorage.getItem('plotEvents_cache');
      if (cached) {
        try {
          const events = JSON.parse(cached);
          setPlotEvents(events);
        } catch (error) {
          console.error('Errore nel caricamento cache eventi:', error);
        }
      }
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const serverEvents = await apiService.getPlotEvents(projectId);
      setPlotEvents(serverEvents);

      // Aggiorna cache locale
      localStorage.setItem('plotEvents_cache', JSON.stringify(serverEvents));
      console.log('✅ Eventi plot caricati dal server:', serverEvents.length);
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore caricamento eventi plot:', err);

      // Fallback: usa cache se disponibile
      const cached = localStorage.getItem('plotEvents_cache');
      if (cached) {
        try {
          const events = JSON.parse(cached);
          setPlotEvents(events);
        } catch (cacheError) {
          console.error('Errore cache eventi:', cacheError);
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Carica eventi all'avvio e quando si connette al server
  useEffect(() => {
    loadPlotEvents();
  }, [loadPlotEvents]);

  // Aggiungi evento
  const addPlotEvent = async (eventData) => {
    if (!isConnected) {
      console.warn('Server non connesso, evento non salvato');
      return;
    }

    try {
      if (editingEvent) {
        // Modifica evento esistente
        const updatedEvent = await apiService.updatePlotEvent(editingEvent.id, {
          ...editingEvent,
          ...eventData,
          updatedAt: new Date().toISOString()
        });

        // Aggiorna stato locale
        const updatedEvents = plotEvents.map(event =>
          event.id === editingEvent.id ? updatedEvent : event
        );
        setPlotEvents(updatedEvents);

        // Aggiorna cache
        localStorage.setItem('plotEvents_cache', JSON.stringify(updatedEvents));
        setEditingEvent(null);
        console.log('✅ Evento plot aggiornato');
      } else {
        // Nuovo evento
        const newEvent = await apiService.createPlotEvent({
          projectId: eventData.projectId || null,
          title: eventData.title,
          description: eventData.description,
          type: eventData.type || 'general',
          importance: eventData.importance || 1,
          chapterReference: eventData.chapterReference || null,
          automatic: eventData.autoExtracted || false
        });

        // Aggiorna stato locale
        setPlotEvents(prev => [...prev, newEvent]);

        // Aggiorna cache
        const updatedEvents = [...plotEvents, newEvent];
        localStorage.setItem('plotEvents_cache', JSON.stringify(updatedEvents));
        console.log('✅ Evento plot creato:', newEvent.title);
      }

      setShowEventForm(false);
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore gestione evento plot:', err);
    }
  };

  // Inizia modifica evento
  const startEditingEvent = (event) => {
    setEditingEvent(event);
    setShowEventForm(true);
  };

  // Cancella modifica
  const cancelEditing = () => {
    setEditingEvent(null);
    setShowEventForm(false);
  };

  // Aggiorna evento esistente
  const updatePlotEvent = async (eventId, updates) => {
    if (!isConnected) {
      console.warn('Server non connesso, evento non aggiornato');
      return;
    }

    try {
      const updatedEvent = await apiService.updatePlotEvent(eventId, {
        ...updates,
        updatedAt: new Date().toISOString()
      });

      // Aggiorna stato locale
      const updatedEvents = plotEvents.map(event =>
        event.id === eventId ? updatedEvent : event
      );
      setPlotEvents(updatedEvents);

      // Aggiorna cache
      localStorage.setItem('plotEvents_cache', JSON.stringify(updatedEvents));
      console.log('✅ Evento plot aggiornato');
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore aggiornamento evento plot:', err);
    }
  };

  // Elimina evento
  const deletePlotEvent = async (eventId) => {
    if (!window.confirm('Sei sicuro di voler eliminare questo evento?')) {
      return;
    }

    if (!isConnected) {
      console.warn('Server non connesso, evento non eliminato');
      return;
    }

    try {
      await apiService.deletePlotEvent(eventId);

      // Aggiorna stato locale
      const updatedEvents = plotEvents.filter(event => event.id !== eventId);
      setPlotEvents(updatedEvents);

      // Aggiorna cache
      localStorage.setItem('plotEvents_cache', JSON.stringify(updatedEvents));
      console.log('✅ Evento plot eliminato');
    } catch (err) {
      setError(err.message);
      console.error('❌ Errore eliminazione evento plot:', err);
    }
  };

  // Risolvi mistero/seed
  const resolveMysterySeed = async (eventId, resolution) => {
    await updatePlotEvent(eventId, {
      resolved: true,
      resolution: resolution,
      resolvedAt: new Date().toISOString()
    });
  };

  // Ottieni eventi per tipo
  const getEventsByType = (type) => {
    return plotEvents.filter(event => event.type === type);
  };

  // Ottieni eventi non risolti
  const getUnresolvedEvents = () => {
    return plotEvents.filter(event => !event.resolved);
  };

  // Ottieni statistiche eventi
  const getEventStats = () => {
    const total = plotEvents.length;
    const resolved = plotEvents.filter(e => e.resolved).length;
    const byType = plotEvents.reduce((acc, event) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {});

    return {
      total,
      resolved,
      unresolved: total - resolved,
      byType
    };
  };

  // Funzione helper per estrarre personaggi da una frase
  const extractCharactersFromSentence = (sentence) => {
    const characters = [];

    // Pattern per nomi propri
    const nameMatches = sentence.match(/\b[A-Z][a-z]+\b/g) || [];
    nameMatches.forEach(name => {
      if (name.length > 2 && !['Il', 'La', 'Un', 'Una', 'Quando', 'Dove', 'Come', 'Perché'].includes(name)) {
        characters.push(name);
      }
    });

    // Pattern per pronomi e ruoli
    const roleMatches = sentence.match(/\b(lui|lei|egli|ella|il ragazzo|la ragazza|l'uomo|la donna|il bambino|la bambina)\b/gi) || [];
    roleMatches.forEach(role => {
      if (!characters.includes(role.toLowerCase())) {
        characters.push(role.toLowerCase());
      }
    });

    return [...new Set(characters)].slice(0, 3); // Massimo 3 personaggi per evento
  };

  // Funzione helper per estrarre luoghi da una frase
  const extractLocationFromSentence = (sentence) => {
    const locationPatterns = [
      /\b(a|in|presso|verso|da)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\b/g,
      /\b(casa|scuola|ufficio|parco|biblioteca|ospedale|chiesa|negozio)\b/gi,
      /\b(cucina|salotto|camera|bagno|giardino|cortile)\b/gi
    ];

    for (const pattern of locationPatterns) {
      const matches = sentence.match(pattern);
      if (matches && matches.length > 0) {
        return matches[0].replace(/^(a|in|presso|verso|da)\s+/i, '');
      }
    }

    return '';
  };

  // Estrai eventi automaticamente dal testo (versione semplificata)
  const extractEventsFromText = (text) => {
    const events = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    sentences.forEach((sentence, index) => {
      const trimmed = sentence.trim().toLowerCase();
      
      // Pattern per misteri
      const mysteryPatterns = [
        /\b(mistero|enigma|segreto|nascosto|celato)\b/,
        /\b(chi|cosa|dove|quando|perché).*\?/,
        /\b(scomparso|sparito|perduto|mancante)\b/,
        /\b(strano|bizzarro|insolito|curioso)\b/
      ];

      // Pattern per scoperte
      const discoveryPatterns = [
        /\b(scoprì|trovò|realizzò|capì|comprese)\b/,
        /\b(rivelò|svelò|mostrò|dimostrò)\b/,
        /\b(la verità|il segreto|la risposta)\b/
      ];

      // Pattern per conflitti
      const conflictPatterns = [
        /\b(litigò|combattè|lottò|si scontrò)\b/,
        /\b(nemico|avversario|rivale|antagonista)\b/,
        /\b(guerra|battaglia|conflitto|scontro)\b/
      ];

      // Controlla pattern
      if (mysteryPatterns.some(pattern => pattern.test(trimmed))) {
        events.push({
          type: 'mystery',
          title: `Mistero identificato`,
          description: sentence.trim(),
          importance: Math.floor(Math.random() * 5) + 5,
          confidence: Math.floor(Math.random() * 30) + 70,
          autoExtracted: true
        });
      } else if (discoveryPatterns.some(pattern => pattern.test(trimmed))) {
        events.push({
          type: 'discovery',
          title: `Scoperta importante`,
          description: sentence.trim(),
          importance: Math.floor(Math.random() * 4) + 6,
          confidence: Math.floor(Math.random() * 25) + 75,
          autoExtracted: true
        });
      } else if (conflictPatterns.some(pattern => pattern.test(trimmed))) {
        events.push({
          type: 'conflict',
          title: `Conflitto rilevato`,
          description: sentence.trim(),
          importance: Math.floor(Math.random() * 3) + 7,
          confidence: Math.floor(Math.random() * 20) + 80,
          autoExtracted: true
        });
      }
    });

    return events.slice(0, 10); // Limita a 10 eventi per evitare spam
  };

  return {
    plotEvents,
    showEventForm,
    setShowEventForm,
    editingEvent,
    isLoading,
    error,
    addPlotEvent,
    deletePlotEvent,
    updatePlotEvent,
    resolveMysterySeed,
    startEditingEvent,
    cancelEditing,
    loadPlotEvents,
    getEventsByType,
    getUnresolvedEvents,
    getEventStats,
    extractEventsFromText,
    extractCharactersFromSentence,
    extractLocationFromSentence
  };
};
