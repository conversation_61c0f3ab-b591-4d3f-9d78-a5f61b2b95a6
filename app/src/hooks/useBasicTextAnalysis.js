import { useState, useMemo } from 'react';

/**
 * Hook per analisi base del testo
 * Gestisce statistiche, classificazione frasi, evidenziazione e punteggi base
 */
export const useBasicTextAnalysis = (text) => {
  const [analysis, setAnalysis] = useState(null);

  // Statistiche base del testo (memoizzate per performance)
  const basicStats = useMemo(() => {
    if (!text || text.trim().length === 0) {
      return {
        words: 0,
        characters: 0,
        charactersNoSpaces: 0,
        sentences: 0,
        paragraphs: 0,
        avgWordsPerSentence: 0,
        avgSentencesPerParagraph: 0,
        readingTime: 0
      };
    }

    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    
    const avgWordsPerSentence = words.length / Math.max(sentences.length, 1);
    const avgSentencesPerParagraph = sentences.length / Math.max(paragraphs.length, 1);
    const readingTime = Math.ceil(words.length / 200); // 200 parole al minuto

    return {
      words: words.length,
      characters: text.length,
      charactersNoSpaces: text.replace(/\s/g, '').length,
      sentences: sentences.length,
      paragraphs: paragraphs.length,
      avgWordsPerSentence: Math.round(avgWordsPerSentence * 10) / 10,
      avgSentencesPerParagraph: Math.round(avgSentencesPerParagraph * 10) / 10,
      readingTime
    };
  }, [text]);

  // Analisi frasi problematiche (memoizzata)
  const sentenceIssues = useMemo(() => {
    if (!text || text.trim().length === 0) {
      return {
        longSentences: [],
        shortSentences: [],
        repeatedWords: []
      };
    }

    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.toLowerCase().split(/\s+/).filter(word => word.length > 2);

    // Frasi troppo lunghe (>25 parole)
    const longSentences = sentences
      .map((sentence, index) => ({
        text: sentence.trim(),
        wordCount: sentence.trim().split(/\s+/).length,
        index
      }))
      .filter(sentence => sentence.wordCount > 25);

    // Frasi troppo corte (<5 parole, escludendo dialoghi)
    const shortSentences = sentences
      .map((sentence, index) => ({
        text: sentence.trim(),
        wordCount: sentence.trim().split(/\s+/).length,
        index
      }))
      .filter(sentence => 
        sentence.wordCount < 5 && 
        !sentence.text.includes('"') && 
        !sentence.text.includes('«')
      );

    // Parole ripetute frequentemente
    const wordCount = {};
    words.forEach(word => {
      const clean = word.replace(/[^\w]/g, '');
      if (clean.length > 3) {
        wordCount[clean] = (wordCount[clean] || 0) + 1;
      }
    });

    const repeatedWords = Object.entries(wordCount)
      .filter(([word, count]) => count > 3)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word, count]) => ({ word, count }));

    return {
      longSentences,
      shortSentences,
      repeatedWords
    };
  }, [text]);

  // Classificazione tipo di frase
  const classifySentence = (sentence) => {
    const trimmed = sentence.trim().toLowerCase();
    
    // Controllo dialogo (presenza di virgolette)
    if (sentence.includes('"') || sentence.includes('«') || sentence.includes('»')) {
      return 'dialogue';
    }

    // Pattern per descrizioni
    const descriptivePatterns = [
      /\b(era|erano|sembrava|appariva|mostrava)\b.*\b(bello|grande|piccolo|alto|basso|lungo|corto|largo|stretto)\b/,
      /\b(colore|aspetto|forma|dimensione|superficie|texture)\b/,
      /\b(paesaggio|panorama|vista|scenario|ambiente|atmosfera)\b/,
      /\b(profumo|odore|sapore|suono|rumore|silenzio)\b/,
      /\b(luce|ombra|buio|luminoso|scuro|brillante|opaco)\b/
    ];

    if (descriptivePatterns.some(pattern => pattern.test(trimmed))) {
      return 'descriptive';
    }

    // Pattern per azioni/narrative
    const narrativePatterns = [
      /\b(andò|venne|arrivò|partì|corse|camminò|saltò|cadde)\b/,
      /\b(prese|diede|mise|tolse|aprì|chiuse|accese|spense)\b/,
      /\b(guardò|vide|sentì|udì|toccò|afferrò|lasciò)\b/,
      /\b(disse|parlò|gridò|sussurrò|rispose|chiese)\b/,
      /\b(pensò|ricordò|dimenticò|sognò|immaginò)\b/,
      /\b(capì|comprese|realizzò|si rese conto|intuì)\b/
    ];

    if (narrativePatterns.some(pattern => pattern.test(trimmed))) {
      return 'narrative';
    }

    return 'narrative'; // Fallback
  };

  // Evidenziazione testo per tipo di frase
  const highlightText = (textToHighlight) => {
    if (!textToHighlight) return '';

    const sentences = textToHighlight.split(/([.!?]+)/);
    
    return sentences.map((part, index) => {
      if (part.match(/[.!?]+/)) {
        return part; // Punteggiatura
      }
      
      if (part.trim().length === 0) {
        return part; // Spazi vuoti
      }

      const type = classifySentence(part);
      const className = type === 'dialogue' ? 'dialogue-highlight' :
                       type === 'descriptive' ? 'descriptive-highlight' :
                       'narrative-highlight';
      
      return `<span class="${className}">${part}</span>`;
    }).join('');
  };

  // Calcolo punteggi base
  const calculateBasicScores = () => {
    if (!text || text.trim().length === 0) {
      return {
        overall: 0,
        readability: 0,
        variety: 0,
        structure: 0,
        grammar: 0
      };
    }

    const words = text.trim().split(/\s+/);
    const uniqueWords = new Set(words.map(w => w.toLowerCase()));

    // Punteggio leggibilità (basato su lunghezza media frasi)
    const readabilityScore = Math.min(100, Math.max(0, 100 - (basicStats.avgWordsPerSentence - 15) * 2));
    
    // Punteggio varietà lessicale
    const varietyScore = Math.min(100, (uniqueWords.size / words.length) * 100);
    
    // Punteggio struttura (rapporto paragrafi/parole)
    const structureScore = Math.min(100, (basicStats.paragraphs / Math.max(1, basicStats.words / 100)) * 50);
    
    // Punteggio grammatica (placeholder)
    const grammarScore = 85;

    const overall = Math.round((readabilityScore + varietyScore + structureScore + grammarScore) / 4);

    return {
      overall,
      readability: Math.round(readabilityScore),
      variety: Math.round(varietyScore),
      structure: Math.round(structureScore),
      grammar: grammarScore
    };
  };

  // Generazione spiegazioni per i punteggi
  const generateExplanations = (scores) => {
    return {
      readability: scores.readability >= 70 ? 'Testo ben leggibile' : 'Frasi troppo lunghe o complesse',
      variety: scores.variety >= 70 ? 'Buona varietà lessicale' : 'Ripetizioni eccessive',
      structure: scores.structure >= 70 ? 'Struttura ben organizzata' : 'Paragrafi troppo lunghi o corti',
      grammar: 'Analisi grammaticale di base'
    };
  };

  // Generazione suggerimenti
  const generateSuggestions = () => {
    const suggestions = [];
    
    if (sentenceIssues.longSentences.length > 0) {
      suggestions.push({
        type: 'warning',
        message: `Trovate ${sentenceIssues.longSentences.length} frasi molto lunghe. Considera di dividerle.`,
        details: sentenceIssues.longSentences.slice(0, 3).map(s => s.text.substring(0, 100) + '...')
      });
    }

    if (sentenceIssues.shortSentences.length > 0) {
      suggestions.push({
        type: 'info',
        message: `Trovate ${sentenceIssues.shortSentences.length} frasi molto brevi. Potresti arricchirle.`,
        details: sentenceIssues.shortSentences.slice(0, 3).map(s => s.text)
      });
    }

    if (sentenceIssues.repeatedWords.length > 0) {
      suggestions.push({
        type: 'warning',
        message: 'Alcune parole sono ripetute frequentemente.',
        details: sentenceIssues.repeatedWords.slice(0, 5).map(w => `"${w.word}" (${w.count} volte)`)
      });
    }

    if (basicStats.avgWordsPerSentence > 20) {
      suggestions.push({
        type: 'warning',
        message: 'Le frasi sono mediamente troppo lunghe per una lettura fluida.'
      });
    }

    if (basicStats.paragraphs < basicStats.words / 150) {
      suggestions.push({
        type: 'info',
        message: 'Considera di dividere il testo in più paragrafi per migliorare la leggibilità.'
      });
    }

    return suggestions;
  };

  // Funzione principale di analisi base
  const analyzeBasicText = () => {
    const scores = calculateBasicScores();
    const explanations = generateExplanations(scores);
    const suggestions = generateSuggestions();

    const result = {
      stats: basicStats,
      scores,
      explanations,
      issues: {
        longSentences: sentenceIssues.longSentences,
        shortSentences: sentenceIssues.shortSentences,
        repeatedWords: sentenceIssues.repeatedWords,
        suggestions
      }
    };

    setAnalysis(result);
    return result;
  };

  return {
    // Stato
    analysis,
    setAnalysis,

    // Dati calcolati
    basicStats,
    sentenceIssues,

    // Funzioni
    classifySentence,
    highlightText,
    calculateBasicScores,
    generateSuggestions,
    analyzeBasicText
  };
};

export default useBasicTextAnalysis;
