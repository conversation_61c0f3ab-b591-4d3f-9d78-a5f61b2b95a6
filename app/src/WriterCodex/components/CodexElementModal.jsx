import React from 'react';
import { X, User, MapPin, Package, Lightbulb, Edit, Trash2, Quote } from 'lucide-react';

const CodexElementModal = ({
  element,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  reference = null,
  citations = [] // Array di citazioni calcolate in tempo reale
}) => {
  if (!isOpen || !element) return null;

  // Ottieni l'icona per tipo di elemento
  const getElementIcon = (elementType) => {
    const icons = {
      character: User,
      location: MapPin,
      object: Package,
      concept: Lightbulb
    };
    return icons[elementType] || User;
  };

  // Ottieni il colore per tipo di elemento
  const getElementColor = (elementType) => {
    const colors = {
      character: 'text-green-600 bg-green-50 border-green-200',
      location: 'text-purple-600 bg-purple-50 border-purple-200',
      object: 'text-orange-600 bg-orange-50 border-orange-200',
      concept: 'text-blue-600 bg-blue-50 border-blue-200'
    };
    return colors[elementType] || 'text-gray-600 bg-gray-50 border-gray-200';
  };

  // Ottieni i campi specifici per tipo
  const getTypeSpecificFields = () => {
    switch (element.type) {
      case 'character':
        return [
          { label: 'Aspetto', value: element.appearance },
          { label: 'Personalità', value: element.personality },
          { label: 'Background', value: element.background },
          { label: 'Importanza', value: element.importance },
          { label: 'Prima apparizione', value: element.firstAppearance }
        ];
      case 'location':
        return [
          { label: 'Geografia', value: element.geography },
          { label: 'Atmosfera', value: element.atmosphere },
          { label: 'Significato', value: element.significance },
          { label: 'Categoria', value: element.category },
          { label: 'Prima menzione', value: element.firstMention }
        ];
      case 'object':
        return [
          { label: 'Aspetto', value: element.appearance },
          { label: 'Funzione', value: element.function },
          { label: 'Origine', value: element.origin },
          { label: 'Proprietario', value: element.owner },
          { label: 'Categoria', value: element.category }
        ];
      case 'concept':
        return [
          { label: 'Spiegazione', value: element.explanation },
          { label: 'Regole', value: element.rules },
          { label: 'Esempi', value: element.examples },
          { label: 'Categoria', value: element.category }
        ];
      default:
        return [];
    }
  };

  const Icon = getElementIcon(element.type);
  const colorClass = getElementColor(element.type);
  const typeSpecificFields = getTypeSpecificFields();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`${colorClass} p-6 border-b`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Icon className="w-6 h-6" />
              <div>
                <h2 className="text-xl font-bold">{element.name}</h2>
                <p className="text-sm opacity-75 capitalize">{element.type}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {onEdit && (
                <button
                  onClick={() => onEdit(element)}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                  title="Modifica elemento"
                >
                  <Edit className="w-4 h-4" />
                </button>
              )}
              
              {onDelete && (
                <button
                  onClick={() => onDelete(element)}
                  className="p-2 hover:bg-red-500 hover:bg-opacity-20 rounded transition-colors"
                  title="Elimina elemento"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              )}
              
              <button
                onClick={onClose}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Descrizione principale */}
          {element.description && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Descrizione</h3>
              <p className="text-gray-700 leading-relaxed">{element.description}</p>
            </div>
          )}

          {/* Alias */}
          {element.aliases && element.aliases.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Nomi alternativi</h3>
              <div className="flex flex-wrap gap-2">
                {element.aliases.map((alias, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm"
                  >
                    {alias}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Campi specifici per tipo */}
          {typeSpecificFields.map((field, index) => (
            field.value && (
              <div key={index}>
                <h3 className="font-semibold text-gray-900 mb-2">{field.label}</h3>
                <p className="text-gray-700 leading-relaxed">{field.value}</p>
              </div>
            )
          ))}

          {/* Tags */}
          {element.tags && element.tags.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Tag</h3>
              <div className="flex flex-wrap gap-2">
                {element.tags.map((tag, index) => (
                  <span
                    key={index}
                    className={`px-2 py-1 rounded text-sm ${colorClass}`}
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Note */}
          {element.notes && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Note</h3>
              <p className="text-gray-700 leading-relaxed">{element.notes}</p>
            </div>
          )}

          {/* Citazioni dal testo */}
          {citations && citations.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Quote className="w-4 h-4" />
                Citazioni nel testo ({citations.length})
              </h3>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {citations.map((citation, index) => (
                  <div key={index} className="bg-gray-50 p-3 rounded border-l-4 border-blue-300">
                    <p className="text-gray-700 text-sm leading-relaxed mb-2">
                      "{citation.sentence}"
                    </p>
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span className="font-medium">
                        {citation.chapterTitle}
                        {citation.subchapterTitle && ` - ${citation.subchapterTitle}`}
                      </span>
                      {citation.timestamp && (
                        <span>
                          {new Date(citation.timestamp).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    {/* Contesto (frasi precedente e successiva) */}
                    {(citation.context?.previous || citation.context?.next) && (
                      <div className="mt-2 pt-2 border-t border-gray-200">
                        <p className="text-xs text-gray-400 italic">
                          {citation.context.previous && `...${citation.context.previous} `}
                          <span className="font-medium text-gray-600">"{citation.sentence}"</span>
                          {citation.context.next && ` ${citation.context.next}...`}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Contesto di riferimento (per compatibilità) */}
          {reference && reference.context && !citations?.length && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Contesto nel testo</h3>
              <div className="bg-gray-50 p-3 rounded border-l-4 border-gray-300">
                <p className="text-gray-700 text-sm italic">
                  "...{reference.context}..."
                </p>
              </div>
            </div>
          )}

          {/* Metadati */}
          <div className="border-t pt-4 text-xs text-gray-500">
            <div className="flex justify-between">
              <span>Creato: {new Date(element.createdAt).toLocaleDateString()}</span>
              {element.updatedAt !== element.createdAt && (
                <span>Modificato: {new Date(element.updatedAt).toLocaleDateString()}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodexElementModal;
