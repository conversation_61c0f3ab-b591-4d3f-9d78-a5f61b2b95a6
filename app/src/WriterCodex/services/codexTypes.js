// Tipi di elementi del Codex
export const CODEX_TYPES = {
  CHARACTER: 'character',
  LOCATION: 'location',
  OBJECT: 'object',
  CONCEPT: 'concept'
};

// Schema per Personaggio
export const CHARACTER_SCHEMA = {
  id: null,
  name: '',
  type: CODEX_TYPES.CHARACTER,
  aliases: [], // nomi alternativi (es. "<PERSON>", "Dott. Rossi")
  description: '',
  appearance: '',
  personality: '',
  background: '',
  relationships: [], // relazioni con altri personaggi
  firstAppearance: '', // dove appare per la prima volta
  importance: 'main', // main, secondary, minor
  notes: '',
  tags: [],
  createdAt: null,
  updatedAt: null
};

// Schema per Luogo
export const LOCATION_SCHEMA = {
  id: null,
  name: '',
  type: CODEX_TYPES.LOCATION,
  aliases: [],
  description: '',
  geography: '', // descrizione geografica
  atmosphere: '', // atmosfera del luogo
  significance: '', // importanza nella trama
  connectedLocations: [], // luoghi collegati
  firstMention: '',
  category: 'setting', // setting, landmark, building
  notes: '',
  tags: [],
  createdAt: null,
  updatedAt: null
};

// Schema per Oggetto
export const OBJECT_SCHEMA = {
  id: null,
  name: '',
  type: CODEX_TYPES.OBJECT,
  aliases: [],
  description: '',
  appearance: '',
  function: '', // a cosa serve
  origin: '', // da dove viene
  significance: '', // importanza nella trama
  owner: '', // chi lo possiede
  firstMention: '',
  category: 'item', // item, weapon, artifact, document
  notes: '',
  tags: [],
  createdAt: null,
  updatedAt: null
};

// Schema per Concetto
export const CONCEPT_SCHEMA = {
  id: null,
  name: '',
  type: CODEX_TYPES.CONCEPT,
  aliases: [],
  description: '',
  explanation: '', // spiegazione dettagliata
  rules: '', // regole o meccaniche
  examples: '', // esempi di utilizzo
  relatedConcepts: [], // concetti correlati
  firstMention: '',
  category: 'theme', // theme, magic, technology, philosophy
  notes: '',
  tags: [],
  createdAt: null,
  updatedAt: null
};

// Funzione per creare un nuovo elemento
export const createCodexElement = (type, data = {}) => {
  const schemas = {
    [CODEX_TYPES.CHARACTER]: CHARACTER_SCHEMA,
    [CODEX_TYPES.LOCATION]: LOCATION_SCHEMA,
    [CODEX_TYPES.OBJECT]: OBJECT_SCHEMA,
    [CODEX_TYPES.CONCEPT]: CONCEPT_SCHEMA
  };

  const schema = schemas[type];
  if (!schema) {
    throw new Error(`Tipo di elemento non valido: ${type}`);
  }

  return {
    ...schema,
    ...data,
    id: data.id || `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    createdAt: data.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
};

// Funzione per validare un elemento
export const validateCodexElement = (element) => {
  if (!element.name || element.name.trim() === '') {
    return { valid: false, error: 'Il nome è obbligatorio' };
  }

  if (!Object.values(CODEX_TYPES).includes(element.type)) {
    return { valid: false, error: 'Tipo di elemento non valido' };
  }



  return { valid: true };
};

// Funzione per cercare elementi per nome o alias
export const findElementsByName = (elements, searchName) => {
  const name = searchName.toLowerCase().trim();
  return elements.filter(element => {
    const mainName = element.name.toLowerCase();
    const aliases = element.aliases.map(alias => alias.toLowerCase());
    
    return mainName.includes(name) || aliases.some(alias => alias.includes(name));
  });
};

// Funzione per ottenere tutti i nomi e alias di un elemento
export const getAllNames = (element) => {
  // Gestisce il caso in cui aliases sia una stringa JSON o un array
  let aliases = [];
  if (element.aliases) {
    if (typeof element.aliases === 'string') {
      try {
        aliases = JSON.parse(element.aliases);
      } catch (e) {
        aliases = [];
      }
    } else if (Array.isArray(element.aliases)) {
      aliases = element.aliases;
    }
  }

  return [element.name, ...aliases].filter(name => name && name.trim() !== '');
};
