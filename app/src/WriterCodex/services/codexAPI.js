const API_BASE_URL = 'http://localhost:9000/api';

// Servizio API per il Codex
export class CodexAPI {
  
  // Ottenere tutti gli elementi del codex
  static async getCodexElements() {
    try {
      const response = await fetch(`${API_BASE_URL}/codex`);
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Errore nel caricamento elementi codex:', error);
      throw error;
    }
  }

  // Creare un nuovo elemento del codex
  static async createElement(element) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(element)
      });
      
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Errore nella creazione elemento codex:', error);
      throw error;
    }
  }

  // Aggiornare un elemento del codex
  static async updateElement(elementId, updates) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/${elementId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          updatedAt: new Date().toISOString()
        })
      });
      
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Errore nell\'aggiornamento elemento codex:', error);
      throw error;
    }
  }

  // Eliminare un elemento del codex
  static async deleteElement(elementId) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/${elementId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      console.error('Errore nell\'eliminazione elemento codex:', error);
      throw error;
    }
  }

  // Cercare elementi per nome
  static async searchElements(searchTerm) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/search?q=${encodeURIComponent(searchTerm)}`);
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Errore nella ricerca elementi codex:', error);
      throw error;
    }
  }

  // Ottenere elementi per tipo
  static async getElementsByType(type) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/type/${type}`);
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Errore nel caricamento elementi per tipo:', error);
      throw error;
    }
  }

  // Analizzare il testo per trovare riferimenti agli elementi del codex
  static async analyzeTextReferences(text) {
    try {
      const response = await fetch(`${API_BASE_URL}/codex/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text })
      });

      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Errore nell\'analisi riferimenti testo:', error);
      throw error;
    }
  }
}

// Servizio localStorage come fallback
export class CodexLocalStorage {
  static getStorageKey() {
    return 'writertool_codex_global';
  }

  static getCodexElements() {
    try {
      const data = localStorage.getItem(this.getStorageKey());
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Errore nel caricamento da localStorage:', error);
      return [];
    }
  }

  static saveCodexElements(elements) {
    try {
      localStorage.setItem(this.getStorageKey(), JSON.stringify(elements));
      return true;
    } catch (error) {
      console.error('Errore nel salvataggio su localStorage:', error);
      return false;
    }
  }

  static createElement(element) {
    const elements = this.getCodexElements();
    elements.push(element);
    this.saveCodexElements(elements);
    return element;
  }

  static updateElement(elementId, updates) {
    const elements = this.getCodexElements();
    const index = elements.findIndex(el => el.id === elementId);

    if (index !== -1) {
      elements[index] = { ...elements[index], ...updates };
      this.saveCodexElements(elements);
      return elements[index];
    }

    throw new Error('Elemento non trovato');
  }

  static deleteElement(elementId) {
    const elements = this.getCodexElements();
    const filteredElements = elements.filter(el => el.id !== elementId);
    this.saveCodexElements(filteredElements);
    return true;
  }

  static searchElements(searchTerm) {
    const elements = this.getCodexElements();
    const term = searchTerm.toLowerCase();

    return elements.filter(element => {
      const searchableText = [
        element.name,
        ...element.aliases,
        element.description,
        ...(element.tags || [])
      ].join(' ').toLowerCase();

      return searchableText.includes(term);
    });
  }

  static getElementsByType(type) {
    const elements = this.getCodexElements();
    return elements.filter(element => element.type === type);
  }
}
