import { useState, useEffect, useCallback } from 'react';
import { CodexAPI, CodexLocalStorage } from '../services/codexAPI';
import { createCodexElement, validateCodexElement, findElementsByName, getAllNames } from '../services/codexTypes';

export const useCodex = () => {
  const [elements, setElements] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isConnected, setIsConnected] = useState(true);

  // Carica elementi del codex
  const loadElements = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      let data;
      if (isConnected) {
        data = await CodexAPI.getCodexElements();
      } else {
        data = CodexLocalStorage.getCodexElements();
      }
      setElements(data || []);
    } catch (err) {
      console.error('Errore nel caricamento elementi codex:', err);
      setError(err.message);

      // Fallback a localStorage se API fallisce
      if (isConnected) {
        setIsConnected(false);
        try {
          const data = CodexLocalStorage.getCodexElements();
          setElements(data || []);
        } catch (localErr) {
          console.error('Errore anche con localStorage:', localErr);
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Crea nuovo elemento
  const createElement = useCallback(async (type, data) => {
    const element = createCodexElement(type, data);
    const validation = validateCodexElement(element);

    if (!validation.valid) {
      throw new Error(validation.error);
    }

    setIsLoading(true);
    setError(null);

    try {
      let createdElement;
      if (isConnected) {
        createdElement = await CodexAPI.createElement(element);
      } else {
        createdElement = CodexLocalStorage.createElement(element);
      }

      setElements(prev => [...prev, createdElement]);
      return createdElement;
    } catch (err) {
      console.error('Errore nella creazione elemento:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Aggiorna elemento
  const updateElement = useCallback(async (elementId, updates) => {
    setIsLoading(true);
    setError(null);

    try {
      let updatedElement;
      if (isConnected) {
        updatedElement = await CodexAPI.updateElement(elementId, updates);
      } else {
        updatedElement = CodexLocalStorage.updateElement(elementId, updates);
      }

      setElements(prev => 
        prev.map(el => el.id === elementId ? updatedElement : el)
      );
      return updatedElement;
    } catch (err) {
      console.error('Errore nell\'aggiornamento elemento:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Elimina elemento
  const deleteElement = useCallback(async (elementId) => {
    setIsLoading(true);
    setError(null);

    try {
      if (isConnected) {
        await CodexAPI.deleteElement(elementId);
      } else {
        CodexLocalStorage.deleteElement(elementId);
      }

      setElements(prev => prev.filter(el => el.id !== elementId));
      return true;
    } catch (err) {
      console.error('Errore nell\'eliminazione elemento:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Cerca elementi
  const searchElements = useCallback(async (searchTerm) => {
    if (!searchTerm) return [];

    try {
      if (isConnected) {
        return await CodexAPI.searchElements(searchTerm);
      } else {
        return CodexLocalStorage.searchElements(searchTerm);
      }
    } catch (err) {
      console.error('Errore nella ricerca:', err);
      return findElementsByName(elements, searchTerm);
    }
  }, [isConnected, elements]);

  // Ottieni elementi per tipo
  const getElementsByType = useCallback((type) => {
    return elements.filter(element => element.type === type);
  }, [elements]);

  // Analizza testo per trovare riferimenti
  const analyzeTextReferences = useCallback((text) => {
    if (!text || !elements.length) return [];

    const references = [];

    elements.forEach(element => {
      const names = getAllNames(element);

      names.forEach(name => {
        if (name && name.trim()) {
          const nameLower = name.toLowerCase();

          // Usa regex con word boundaries per una ricerca più robusta
          const regex = new RegExp(`\\b${nameLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
          let match;

          while ((match = regex.exec(text)) !== null) {
            const reference = {
              element,
              name,
              startIndex: match.index,
              endIndex: match.index + match[0].length,
              context: text.substring(Math.max(0, match.index - 50), Math.min(text.length, match.index + match[0].length + 50))
            };
            references.push(reference);
          }
        }
      });
    });

    // Ordina per posizione nel testo
    return references.sort((a, b) => a.startIndex - b.startIndex);
  }, [elements]);

  // Estrae frasi contenenti citazioni di personaggi
  const extractCharacterQuotes = useCallback((text, chapterTitle = '', subchapterTitle = '') => {
    if (!text || !elements.length) return {};

    const characterQuotes = {};

    // Dividi il testo in frasi (gestisce punteggiatura italiana)
    const sentences = text.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 0);

    elements.forEach(element => {
      if (element.type === 'character') {
        const names = getAllNames(element);
        const quotes = [];

        sentences.forEach((sentence, index) => {
          // Verifica se la frase contiene il personaggio
          const containsCharacter = names.some(name => {
            const regex = new RegExp(`\\b${name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
            return regex.test(sentence);
          });

          if (containsCharacter) {
            quotes.push({
              sentence: sentence.trim(),
              sentenceIndex: index,
              chapterTitle,
              subchapterTitle,
              timestamp: new Date().toISOString(),
              context: {
                previous: index > 0 ? sentences[index - 1]?.trim() : null,
                next: index < sentences.length - 1 ? sentences[index + 1]?.trim() : null
              }
            });
          }
        });

        if (quotes.length > 0) {
          characterQuotes[element.id] = {
            character: element,
            quotes: quotes
          };
        }
      }
    });

    return characterQuotes;
  }, [elements]);



  // Carica elementi all'avvio
  useEffect(() => {
    loadElements();
  }, [loadElements]);

  return {
    elements,
    isLoading,
    error,
    isConnected,
    loadElements,
    createElement,
    updateElement,
    deleteElement,
    searchElements,
    getElementsByType,
    analyzeTextReferences,
    extractCharacterQuotes
  };
};
