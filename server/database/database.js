const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Crea la directory del database se non esiste
const dbDir = path.join(__dirname, '../data');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const dbPath = path.join(dbDir, 'writer_tool.db');

class Database {
  constructor() {
    this.db = null;
  }

  // Inizializza la connessione al database
  async init() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('Errore apertura database:', err);
          reject(err);
        } else {
          console.log('✅ Database SQLite connesso:', dbPath);
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  // Crea le tabelle se non esistono
  async createTables() {
    const tables = [
      // <PERSON>bella progetti
      `CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabella versioni dei progetti
      `CREATE TABLE IF NOT EXISTS project_versions (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        name TEXT,
        text TEXT,
        word_count INTEGER DEFAULT 0,
        analysis TEXT, -- JSON dell'analisi
        scores TEXT,   -- JSON dei punteggi
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
      )`,

      // Tabella capitoli
      `CREATE TABLE IF NOT EXISTS chapters (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        position INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabella sottocapitoli
      `CREATE TABLE IF NOT EXISTS subchapters (
        id TEXT PRIMARY KEY,
        chapter_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        position INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chapter_id) REFERENCES chapters (id) ON DELETE CASCADE
      )`,

      // Tabella versioni live dei sottocapitoli
      `CREATE TABLE IF NOT EXISTS subchapter_live_versions (
        subchapter_id TEXT PRIMARY KEY,
        text TEXT,
        word_count INTEGER DEFAULT 0,
        analysis TEXT, -- JSON dell'analisi
        source_project_id TEXT,
        source_version_id TEXT,
        source_project_title TEXT,
        source_version_name TEXT,
        linked_at DATETIME,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (subchapter_id) REFERENCES subchapters (id) ON DELETE CASCADE
      )`,

      // Tabella versioni backup dei sottocapitoli
      `CREATE TABLE IF NOT EXISTS subchapter_backup_versions (
        id TEXT PRIMARY KEY,
        subchapter_id TEXT NOT NULL,
        name TEXT NOT NULL,
        text TEXT,
        word_count INTEGER DEFAULT 0,
        analysis TEXT, -- JSON dell'analisi
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (subchapter_id) REFERENCES subchapters (id) ON DELETE CASCADE
      )`,

      // Tabella eventi del plot
      `CREATE TABLE IF NOT EXISTS plot_events (
        id TEXT PRIMARY KEY,
        project_id TEXT,
        title TEXT NOT NULL,
        description TEXT,
        type TEXT, -- 'setup', 'conflict', 'resolution', etc.
        importance INTEGER DEFAULT 1, -- 1-5
        chapter_reference TEXT,
        automatic BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabella personaggi
      `CREATE TABLE IF NOT EXISTS characters (
        id TEXT PRIMARY KEY,
        project_id TEXT,
        version_id TEXT, -- Collegamento alla versione specifica
        name TEXT NOT NULL,
        description TEXT,
        physical_traits TEXT, -- JSON
        personality_traits TEXT, -- JSON
        plot_impact TEXT, -- JSON
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabella elementi del Codex (globale, non legata ai progetti)
      `CREATE TABLE IF NOT EXISTS codex_elements (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        aliases TEXT DEFAULT '[]',
        description TEXT,
        appearance TEXT,
        personality TEXT,
        background TEXT,
        geography TEXT,
        atmosphere TEXT,
        significance TEXT,
        function TEXT,
        origin TEXT,
        owner TEXT,
        explanation TEXT,
        rules TEXT,
        examples TEXT,
        first_appearance TEXT,
        first_mention TEXT,
        importance TEXT,
        category TEXT,
        relationships TEXT DEFAULT '[]',
        connected_locations TEXT DEFAULT '[]',
        related_concepts TEXT DEFAULT '[]',
        notes TEXT,
        tags TEXT DEFAULT '[]',

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Indici per performance
      `CREATE INDEX IF NOT EXISTS idx_project_versions_project_id ON project_versions (project_id)`,
      `CREATE INDEX IF NOT EXISTS idx_subchapters_chapter_id ON subchapters (chapter_id)`,
      `CREATE INDEX IF NOT EXISTS idx_backup_versions_subchapter_id ON subchapter_backup_versions (subchapter_id)`,
      `CREATE INDEX IF NOT EXISTS idx_plot_events_project_id ON plot_events (project_id)`,
      `CREATE INDEX IF NOT EXISTS idx_characters_project_id ON characters (project_id)`,
      `CREATE INDEX IF NOT EXISTS idx_codex_elements_type ON codex_elements (type)`,
      `CREATE INDEX IF NOT EXISTS idx_codex_elements_name ON codex_elements (name)`
    ];

    for (const sql of tables) {
      await this.run(sql);
    }

    // Migrazione: aggiungi version_id alla tabella characters se non esiste
    await this.migrateCharactersTable();

    // Migrazione: rimuovi project_id dalla tabella codex_elements
    await this.migrateCodexElementsTable();



    console.log('✅ Tabelle database create/verificate');
  }

  // Migrazione per aggiungere version_id alla tabella characters
  async migrateCharactersTable() {
    try {
      // Controlla se la colonna version_id esiste già
      const tableInfo = await Database.all("PRAGMA table_info(characters)");
      const hasVersionId = tableInfo.some(column => column.name === 'version_id');

      if (!hasVersionId) {
        console.log('🔄 Aggiungendo colonna version_id alla tabella characters...');
        await Database.run('ALTER TABLE characters ADD COLUMN version_id TEXT');
        console.log('✅ Colonna version_id aggiunta alla tabella characters');
      }
    } catch (error) {
      console.error('❌ Errore migrazione tabella characters:', error);
    }
  }

  // Migrazione per rimuovere project_id dalla tabella codex_elements
  async migrateCodexElementsTable() {
    try {
      // Controlla se la tabella esiste e ha la colonna project_id
      const tableInfo = await Database.all("PRAGMA table_info(codex_elements)");
      const hasProjectId = tableInfo.some(column => column.name === 'project_id');

      if (hasProjectId) {
        console.log('🔄 Migrazione: Rimozione colonna project_id dalla tabella codex_elements');

        // Salva i dati esistenti
        const existingData = await Database.all('SELECT * FROM codex_elements');

        // Rimuovi la tabella esistente
        await Database.run('DROP TABLE IF EXISTS codex_elements');

        // Ricrea la tabella senza project_id
        await Database.run(`
          CREATE TABLE codex_elements (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            aliases TEXT DEFAULT '[]',
            description TEXT,
            appearance TEXT,
            personality TEXT,
            background TEXT,
            geography TEXT,
            atmosphere TEXT,
            significance TEXT,
            function TEXT,
            origin TEXT,
            owner TEXT,
            explanation TEXT,
            rules TEXT,
            examples TEXT,
            first_appearance TEXT,
            first_mention TEXT,
            importance TEXT,
            category TEXT,
            relationships TEXT DEFAULT '[]',
            connected_locations TEXT DEFAULT '[]',
            related_concepts TEXT DEFAULT '[]',
            notes TEXT,
            tags TEXT DEFAULT '[]',

            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Ricrea gli indici
        await Database.run('CREATE INDEX IF NOT EXISTS idx_codex_elements_type ON codex_elements (type)');
        await Database.run('CREATE INDEX IF NOT EXISTS idx_codex_elements_name ON codex_elements (name)');

        // Reinserisci i dati esistenti (senza project_id)
        for (const row of existingData) {
          const { project_id, ...dataWithoutProjectId } = row;
          const columns = Object.keys(dataWithoutProjectId).join(', ');
          const placeholders = Object.keys(dataWithoutProjectId).map(() => '?').join(', ');
          const values = Object.values(dataWithoutProjectId);

          await Database.run(
            `INSERT INTO codex_elements (${columns}) VALUES (${placeholders})`,
            values
          );
        }

        console.log(`✅ Migrazione completata: project_id rimossa dalla tabella codex_elements (${existingData.length} elementi migrati)`);
      }
    } catch (error) {
      console.error('❌ Errore nella migrazione tabella codex_elements:', error);
    }
  }



  // Wrapper per query con Promise
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('Errore SQL run:', err);
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          console.error('Errore SQL get:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('Errore SQL all:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Chiudi la connessione
  close() {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            reject(err);
          } else {
            console.log('✅ Database chiuso');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  // Backup del database
  async backup(backupPath) {
    return new Promise((resolve, reject) => {
      const backup = this.db.backup(backupPath);
      backup.step(-1, (err) => {
        if (err) {
          reject(err);
        } else {
          backup.finish((err) => {
            if (err) {
              reject(err);
            } else {
              resolve();
            }
          });
        }
      });
    });
  }
}

// Singleton instance
const database = new Database();

module.exports = database;
