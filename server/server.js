const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');

// Importa le route
const projectsRouter = require('./routes/projects');
const chaptersRouter = require('./routes/chapters');
const analysisRouter = require('./routes/analysis');
const plotEventsRouter = require('./routes/plotEvents');
const charactersRouter = require('./routes/characters');
const backupRouter = require('./routes/backup');
const codexRouter = require('./routes/codex');

const app = express();
const PORT = process.env.PORT || 9000;

// Middleware di sicurezza
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Compressione
app.use(compression());

// Logging
app.use(morgan('combined'));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minuti
  max: 1000, // Limite di 1000 richieste per IP ogni 15 minuti
  message: 'Troppe richieste da questo IP, riprova più tardi.'
});
app.use(limiter);

// CORS - Permetti solo il frontend locale
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Parsing JSON
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Middleware per logging delle richieste
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes API
app.use('/api/projects', projectsRouter);
app.use('/api/chapters', chaptersRouter);
app.use('/api/analysis', analysisRouter);
app.use('/api/plot-events', plotEventsRouter);
app.use('/api/characters', charactersRouter);
app.use('/api/backup', backupRouter);
app.use('/api/codex', codexRouter);

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0'
  });
});

// Debug endpoint - Mostra tutti i dati
app.get('/api/debug', async (req, res) => {
  try {
    const database = require('./database/database');

    const projects = await database.all('SELECT * FROM projects ORDER BY created_at DESC');
    const projectVersions = await database.all('SELECT * FROM project_versions ORDER BY timestamp DESC');
    const chapters = await database.all('SELECT * FROM chapters ORDER BY position ASC');
    const subchapters = await database.all('SELECT * FROM subchapters ORDER BY position ASC');
    const liveVersions = await database.all('SELECT * FROM subchapter_live_versions');
    const backupVersions = await database.all('SELECT * FROM subchapter_backup_versions ORDER BY created_at DESC');
    const plotEvents = await database.all('SELECT * FROM plot_events ORDER BY created_at DESC');
    const characters = await database.all('SELECT * FROM characters ORDER BY created_at DESC');

    res.json({
      summary: {
        projects: projects.length,
        projectVersions: projectVersions.length,
        chapters: chapters.length,
        subchapters: subchapters.length,
        liveVersions: liveVersions.length,
        backupVersions: backupVersions.length,
        plotEvents: plotEvents.length,
        characters: characters.length
      },
      data: {
        projects,
        projectVersions,
        chapters,
        subchapters,
        liveVersions,
        backupVersions,
        plotEvents,
        characters
      }
    });
  } catch (error) {
    console.error('Errore debug endpoint:', error);
    res.status(500).json({ error: 'Errore nel recupero dati debug' });
  }
});

// Debug endpoint - Mostra tutti i dati
app.get('/api/debug', async (req, res) => {
  try {
    const database = require('./database/database');

    const projects = await database.all('SELECT * FROM projects ORDER BY created_at DESC');
    const projectVersions = await database.all('SELECT * FROM project_versions ORDER BY timestamp DESC');
    const chapters = await database.all('SELECT * FROM chapters ORDER BY position ASC');
    const subchapters = await database.all('SELECT * FROM subchapters ORDER BY position ASC');
    const liveVersions = await database.all('SELECT * FROM subchapter_live_versions');
    const backupVersions = await database.all('SELECT * FROM subchapter_backup_versions ORDER BY created_at DESC');
    const plotEvents = await database.all('SELECT * FROM plot_events ORDER BY created_at DESC');
    const characters = await database.all('SELECT * FROM characters ORDER BY created_at DESC');

    res.json({
      summary: {
        projects: projects.length,
        projectVersions: projectVersions.length,
        chapters: chapters.length,
        subchapters: subchapters.length,
        liveVersions: liveVersions.length,
        backupVersions: backupVersions.length,
        plotEvents: plotEvents.length,
        characters: characters.length
      },
      data: {
        projects,
        projectVersions,
        chapters,
        subchapters,
        liveVersions,
        backupVersions,
        plotEvents,
        characters
      }
    });
  } catch (error) {
    console.error('Errore debug endpoint:', error);
    res.status(500).json({ error: 'Errore nel recupero dati debug' });
  }
});

// Serve static files (per eventuali export/backup)
app.use('/static', express.static(path.join(__dirname, 'public')));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Errore server:', err);
  res.status(500).json({
    error: 'Errore interno del server',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Qualcosa è andato storto'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint non trovato',
    path: req.originalUrl
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM ricevuto, chiusura server...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT ricevuto, chiusura server...');
  process.exit(0);
});

// Inizializza il database all'avvio
const database = require('./database/database');

async function startServer() {
  try {
    // Inizializza il database
    await database.init();
    console.log('✅ Database inizializzato');

    // Avvia il server
    app.listen(PORT, () => {
      console.log(`
🚀 Writer Tool Server avviato!
📍 URL: http://localhost:${PORT}
🕒 Timestamp: ${new Date().toISOString()}
🔧 Ambiente: ${process.env.NODE_ENV || 'development'}
📊 Health check: http://localhost:${PORT}/api/health
      `);
    });
  } catch (error) {
    console.error('❌ Errore avvio server:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
