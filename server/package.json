{"name": "writer-tool-server", "version": "1.0.0", "description": "Server locale per Writer Tool - Gestione professionale dati di scrittura", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js", "reset-db": "node scripts/reset-database.js"}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "react-hot-toast": "^2.5.2", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["writing", "editor", "local-server", "sqlite", "api"], "author": "Writer <PERSON><PERSON>", "license": "MIT"}