const database = require('../database/database');

async function initDatabase() {
  try {
    console.log('🔧 Inizializzazione database...');
    
    await database.init();
    
    console.log('✅ Database inizializzato con successo!');
    console.log('📊 <PERSON><PERSON><PERSON> create:');
    console.log('   - projects');
    console.log('   - project_versions');
    console.log('   - chapters');
    console.log('   - subchapters');
    console.log('   - subchapter_live_versions');
    console.log('   - subchapter_backup_versions');
    console.log('   - plot_events');
    console.log('   - characters');
    
    await database.close();
    process.exit(0);
  } catch (error) {
    console.error('❌ Errore inizializzazione database:', error);
    process.exit(1);
  }
}

initDatabase();
