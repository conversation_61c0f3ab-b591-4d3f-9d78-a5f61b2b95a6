# Writer Tool Server

Server locale per Writer Tool - Gestione professionale dei dati di scrittura con database SQLite.

## 🚀 Avvio Rapido

### Installazione
```bash
cd server
npm install
```

### Inizializzazione Database
```bash
npm run init-db
```

### Avvio Server
```bash
# Sviluppo (con auto-reload)
npm run dev

# Produzione
npm start
```

Il server sarà disponibile su: `http://localhost:3001`

## 📊 API Endpoints

### Progetti
- `GET /api/projects` - Lista tutti i progetti
- `POST /api/projects` - Crea nuovo progetto
- `GET /api/projects/:id` - Ottieni progetto specifico
- `PUT /api/projects/:id` - Aggiorna progetto
- `DELETE /api/projects/:id` - Elimina progetto
- `POST /api/projects/:id/versions` - Crea nuova versione
- `GET /api/projects/:id/versions/:versionId` - Ottieni versione specifica
- `DELETE /api/projects/:id/versions/:versionId` - Elimina versione

### Capitoli
- `GET /api/chapters` - Lista tutti i capitoli
- `POST /api/chapters` - Crea nuovo capitolo
- `PUT /api/chapters/:id` - Aggiorna capitolo
- `DELETE /api/chapters/:id` - Elimina capitolo
- `POST /api/chapters/:id/subchapters` - Crea nuovo sottocapitolo
- `PUT /api/chapters/:chapterId/subchapters/:subchapterId` - Aggiorna sottocapitolo
- `POST /api/chapters/:chapterId/subchapters/:subchapterId/link` - Collega versione
- `DELETE /api/chapters/:chapterId/subchapters/:subchapterId/link` - Scollega versione

### Analisi
- `POST /api/analysis/text` - Analizza testo diretto
- `POST /api/analysis/project/:projectId/version/:versionId` - Analizza versione progetto
- `POST /api/analysis/subchapter/:subchapterId` - Analizza sottocapitolo

### Eventi Plot
- `GET /api/plot-events` - Lista eventi plot
- `POST /api/plot-events` - Crea evento plot
- `PUT /api/plot-events/:id` - Aggiorna evento plot
- `DELETE /api/plot-events/:id` - Elimina evento plot

### Personaggi
- `GET /api/characters` - Lista personaggi
- `POST /api/characters` - Crea personaggio
- `PUT /api/characters/:id` - Aggiorna personaggio
- `DELETE /api/characters/:id` - Elimina personaggio

### Backup
- `GET /api/backup/export` - Esporta tutti i dati
- `POST /api/backup/import` - Importa dati da backup
- `POST /api/backup/database` - Crea backup database

### Utilità
- `GET /api/health` - Health check del server

## 🗄️ Struttura Database

### Tabelle Principali

#### projects
- `id` (TEXT PRIMARY KEY)
- `title` (TEXT NOT NULL)
- `description` (TEXT)
- `created_at` (DATETIME)
- `updated_at` (DATETIME)

#### project_versions
- `id` (TEXT PRIMARY KEY)
- `project_id` (TEXT, FK)
- `name` (TEXT)
- `text` (TEXT)
- `word_count` (INTEGER)
- `analysis` (TEXT, JSON)
- `scores` (TEXT, JSON)
- `timestamp` (DATETIME)

#### chapters
- `id` (TEXT PRIMARY KEY)
- `title` (TEXT NOT NULL)
- `description` (TEXT)
- `position` (INTEGER)
- `created_at` (DATETIME)
- `updated_at` (DATETIME)

#### subchapters
- `id` (TEXT PRIMARY KEY)
- `chapter_id` (TEXT, FK)
- `title` (TEXT NOT NULL)
- `description` (TEXT)
- `position` (INTEGER)
- `created_at` (DATETIME)
- `updated_at` (DATETIME)

#### subchapter_live_versions
- `subchapter_id` (TEXT PRIMARY KEY, FK)
- `text` (TEXT)
- `word_count` (INTEGER)
- `analysis` (TEXT, JSON)
- `source_project_id` (TEXT)
- `source_version_id` (TEXT)
- `source_project_title` (TEXT)
- `source_version_name` (TEXT)
- `linked_at` (DATETIME)
- `updated_at` (DATETIME)

#### subchapter_backup_versions
- `id` (TEXT PRIMARY KEY)
- `subchapter_id` (TEXT, FK)
- `name` (TEXT NOT NULL)
- `text` (TEXT)
- `word_count` (INTEGER)
- `analysis` (TEXT, JSON)
- `created_at` (DATETIME)

## 🔧 Configurazione

### Variabili d'Ambiente
- `PORT` - Porta del server (default: 3001)
- `NODE_ENV` - Ambiente (development/production)

### Sicurezza
- Rate limiting: 1000 richieste per IP ogni 15 minuti
- CORS configurato per localhost:3000
- Helmet per headers di sicurezza
- Validazione input su tutti gli endpoint

## 📁 Struttura File

```
server/
├── data/                    # Database SQLite
├── database/
│   └── database.js         # Manager database
├── routes/
│   ├── projects.js         # API progetti
│   ├── chapters.js         # API capitoli
│   ├── analysis.js         # API analisi
│   ├── plotEvents.js       # API eventi plot
│   ├── characters.js       # API personaggi
│   └── backup.js           # API backup
├── scripts/
│   └── init-database.js    # Inizializzazione DB
├── public/                 # File statici
├── backups/               # Backup database
├── package.json
├── server.js              # Server principale
└── README.md
```

## 🔄 Backup e Ripristino

### Export Dati
```bash
curl http://localhost:3001/api/backup/export > backup.json
```

### Import Dati
```bash
curl -X POST http://localhost:3001/api/backup/import \
  -H "Content-Type: application/json" \
  -d @backup.json
```

### Backup Database
```bash
curl -X POST http://localhost:3001/api/backup/database
```

## 🚨 Troubleshooting

### Database Locked
Se il database risulta bloccato:
```bash
# Ferma il server
# Elimina il file di lock se presente
rm server/data/writer_tool.db-wal
rm server/data/writer_tool.db-shm
# Riavvia il server
```

### Reset Database
```bash
npm run reset-db
npm run init-db
```

### Logs
I log del server includono:
- Tutte le richieste HTTP
- Errori del database
- Operazioni di backup/restore

## 📈 Performance

- Indici ottimizzati per query frequenti
- Compressione gzip per risposte
- Connection pooling SQLite
- Rate limiting per prevenire abusi

## 🔐 Sicurezza

- Validazione input rigorosa
- Sanitizzazione dati JSON
- Headers di sicurezza
- CORS restrittivo
- Rate limiting

## 📝 Note di Sviluppo

- Usa UUID per tutti gli ID
- JSON per dati strutturati (analisi, traits, etc.)
- Timestamp ISO 8601
- Soft delete non implementato (hard delete)
- Transazioni per operazioni multiple
