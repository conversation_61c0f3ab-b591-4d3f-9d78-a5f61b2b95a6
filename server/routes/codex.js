const express = require('express');
const router = express.Router();
const Database = require('../database/database');

// GET /api/codex - Ottieni tutti gli elementi del codex
router.get('/', async (req, res) => {
  try {
    const elements = await Database.all(
      'SELECT * FROM codex_elements ORDER BY created_at DESC'
    );

    // Converti i campi JSON da stringa a oggetto
    const processedElements = elements.map(element => ({
      ...element,
      aliases: JSON.parse(element.aliases || '[]'),
      relationships: JSON.parse(element.relationships || '[]'),
      connected_locations: JSON.parse(element.connected_locations || '[]'),
      related_concepts: JSON.parse(element.related_concepts || '[]'),
      tags: JSON.parse(element.tags || '[]')
    }));

    res.json(processedElements);
  } catch (error) {
    console.error('Errore nel recupero elementi codex:', error);
    res.status(500).json({ error: 'Errore interno del server' });
  }
});

// POST /api/codex - Crea un nuovo elemento del codex
router.post('/', async (req, res) => {
  try {
    const element = req.body;
    
    // Valida i campi obbligatori
    if (!element.name || !element.type) {
      return res.status(400).json({
        error: 'Nome e tipo sono obbligatori'
      });
    }

    // Converti gli array in JSON string per il database
    const elementData = {
      ...element,
      aliases: JSON.stringify(element.aliases || []),
      relationships: JSON.stringify(element.relationships || []),
      connected_locations: JSON.stringify(element.connected_locations || []),
      related_concepts: JSON.stringify(element.related_concepts || []),
      tags: JSON.stringify(element.tags || [])
    };

    await Database.run(`
      INSERT INTO codex_elements (
        id, name, type, aliases, description, appearance,
        personality, background, geography, atmosphere, significance,
        function, origin, owner, explanation, rules, examples,
        first_appearance, first_mention, importance, category,
        relationships, connected_locations, related_concepts,
        notes, tags, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      elementData.id,
      elementData.name,
      elementData.type,
      elementData.aliases,
      elementData.description || null,
      elementData.appearance || null,
      elementData.personality || null,
      elementData.background || null,
      elementData.geography || null,
      elementData.atmosphere || null,
      elementData.significance || null,
      elementData.function || null,
      elementData.origin || null,
      elementData.owner || null,
      elementData.explanation || null,
      elementData.rules || null,
      elementData.examples || null,
      elementData.first_appearance || null,
      elementData.first_mention || null,
      elementData.importance || null,
      elementData.category || null,
      elementData.relationships,
      elementData.connected_locations,
      elementData.related_concepts,
      elementData.notes || null,
      elementData.tags,
      elementData.createdAt,
      elementData.updatedAt
    ]);

    // Recupera l'elemento creato
    const createdElement = await Database.get(
      'SELECT * FROM codex_elements WHERE id = ?',
      [elementData.id]
    );

    // Processa i campi JSON
    const processedElement = {
      ...createdElement,
      aliases: JSON.parse(createdElement.aliases || '[]'),
      relationships: JSON.parse(createdElement.relationships || '[]'),
      connected_locations: JSON.parse(createdElement.connected_locations || '[]'),
      related_concepts: JSON.parse(createdElement.related_concepts || '[]'),
      tags: JSON.parse(createdElement.tags || '[]')
    };

    res.status(201).json(processedElement);
  } catch (error) {
    console.error('Errore nella creazione elemento codex:', error);
    res.status(500).json({ error: 'Errore interno del server' });
  }
});

// PUT /api/codex/:elementId - Aggiorna un elemento del codex
router.put('/:elementId', async (req, res) => {
  try {
    const { elementId } = req.params;
    const updates = req.body;

    // Converti gli array in JSON string
    const updateData = {
      ...updates,
      aliases: JSON.stringify(updates.aliases || []),
      relationships: JSON.stringify(updates.relationships || []),
      connected_locations: JSON.stringify(updates.connected_locations || []),
      related_concepts: JSON.stringify(updates.related_concepts || []),
      tags: JSON.stringify(updates.tags || []),
      updated_at: new Date().toISOString()
    };

    // Costruisci la query di update dinamicamente
    const fields = Object.keys(updateData).filter(key => key !== 'id');
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => updateData[field]);
    values.push(elementId);

    await Database.run(
      `UPDATE codex_elements SET ${setClause} WHERE id = ?`,
      values
    );

    // Recupera l'elemento aggiornato
    const updatedElement = await Database.get(
      'SELECT * FROM codex_elements WHERE id = ?',
      [elementId]
    );

    if (!updatedElement) {
      return res.status(404).json({ error: 'Elemento non trovato' });
    }

    // Processa i campi JSON
    const processedElement = {
      ...updatedElement,
      aliases: JSON.parse(updatedElement.aliases || '[]'),
      relationships: JSON.parse(updatedElement.relationships || '[]'),
      connected_locations: JSON.parse(updatedElement.connected_locations || '[]'),
      related_concepts: JSON.parse(updatedElement.related_concepts || '[]'),
      tags: JSON.parse(updatedElement.tags || '[]')
    };

    res.json(processedElement);
  } catch (error) {
    console.error('Errore nell\'aggiornamento elemento codex:', error);
    res.status(500).json({ error: 'Errore interno del server' });
  }
});

// DELETE /api/codex/:elementId - Elimina un elemento del codex
router.delete('/:elementId', async (req, res) => {
  try {
    const { elementId } = req.params;

    const result = await Database.run(
      'DELETE FROM codex_elements WHERE id = ?',
      [elementId]
    );

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Elemento non trovato' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Errore nell\'eliminazione elemento codex:', error);
    res.status(500).json({ error: 'Errore interno del server' });
  }
});

// GET /api/codex/search - Cerca elementi nel codex
router.get('/search', async (req, res) => {
  try {
    const { q } = req.query;

    if (!q) {
      return res.status(400).json({ error: 'Query di ricerca richiesta' });
    }

    const elements = await Database.all(`
      SELECT * FROM codex_elements
      WHERE (
        name LIKE ? OR
        description LIKE ? OR
        aliases LIKE ? OR
        notes LIKE ?
      )
      ORDER BY created_at DESC
    `, [`%${q}%`, `%${q}%`, `%${q}%`, `%${q}%`]);

    // Processa i campi JSON
    const processedElements = elements.map(element => ({
      ...element,
      aliases: JSON.parse(element.aliases || '[]'),
      relationships: JSON.parse(element.relationships || '[]'),
      connected_locations: JSON.parse(element.connected_locations || '[]'),
      related_concepts: JSON.parse(element.related_concepts || '[]'),
      tags: JSON.parse(element.tags || '[]')
    }));

    res.json(processedElements);
  } catch (error) {
    console.error('Errore nella ricerca elementi codex:', error);
    res.status(500).json({ error: 'Errore interno del server' });
  }
});

// GET /api/codex/type/:type - Ottieni elementi per tipo
router.get('/type/:type', async (req, res) => {
  try {
    const { type } = req.params;

    const elements = await Database.all(
      'SELECT * FROM codex_elements WHERE type = ? ORDER BY created_at DESC',
      [type]
    );

    // Processa i campi JSON
    const processedElements = elements.map(element => ({
      ...element,
      aliases: JSON.parse(element.aliases || '[]'),
      relationships: JSON.parse(element.relationships || '[]'),
      connected_locations: JSON.parse(element.connected_locations || '[]'),
      related_concepts: JSON.parse(element.related_concepts || '[]'),
      tags: JSON.parse(element.tags || '[]')
    }));

    res.json(processedElements);
  } catch (error) {
    console.error('Errore nel recupero elementi per tipo:', error);
    res.status(500).json({ error: 'Errore interno del server' });
  }
});

// POST /api/codex/extract - Estrai elementi automaticamente dal testo
router.post('/extract', async (req, res) => {
  try {
    const { text } = req.body;

    if (!text || !text.trim()) {
      return res.status(400).json({ error: 'Testo richiesto per l\'estrazione' });
    }

    // Ottieni elementi esistenti per evitare duplicati
    const existingElements = await Database.all('SELECT name, type FROM codex_elements');
    const existingNames = new Set(existingElements.map(el => el.name.toLowerCase()));

    const extractedElements = extractElementsFromText(text, existingNames);

    res.json({
      elements: extractedElements,
      totalFound: extractedElements.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Errore nell\'estrazione elementi:', error);
    res.status(500).json({ error: 'Errore interno del server' });
  }
});

// Funzione per estrarre elementi dal testo
function extractElementsFromText(text, existingNames = new Set()) {
  const elements = [];
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

  sentences.forEach((sentence, index) => {
    const trimmed = sentence.trim();

    // Estrai personaggi (nomi propri)
    const characterMatches = trimmed.match(/\b[A-Z][a-z]{2,}\b/g) || [];
    characterMatches.forEach(name => {
      if (!existingNames.has(name.toLowerCase()) &&
          !['Il', 'La', 'Un', 'Una', 'Quando', 'Dove', 'Come', 'Perché', 'Questo', 'Quella'].includes(name)) {

        // Verifica se sembra un personaggio (non solo un nome casuale)
        const isCharacter = /\b(disse|parlò|rispose|gridò|sussurrò|pensò|andò|venne|arrivò)\b/i.test(sentence) ||
                           /\b(lui|lei|egli|ella)\b/i.test(sentence);

        if (isCharacter) {
          elements.push({
            name,
            type: 'character',
            confidence: 0.8,
            context: trimmed.substring(0, 100),
            sentenceIndex: index,
            suggestedDescription: `Personaggio estratto automaticamente dal testo.`,
            suggestedImportance: 'secondary'
          });
          existingNames.add(name.toLowerCase());
        }
      }
    });

    // Estrai luoghi
    const locationPatterns = [
      /\b(a|in|presso|verso|da)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)\b/g,
      /\b(casa|scuola|ufficio|parco|biblioteca|ospedale|chiesa|negozio|città|paese|villaggio|bosco|foresta|mare|montagna|strada|piazza|palazzo|castello|torre)\b/gi
    ];

    locationPatterns.forEach(pattern => {
      const matches = trimmed.match(pattern) || [];
      matches.forEach(match => {
        const location = match.replace(/^(a|in|presso|verso|da)\s+/i, '').toLowerCase();
        if (!existingNames.has(location)) {
          elements.push({
            name: location,
            type: 'location',
            confidence: 0.7,
            context: trimmed.substring(0, 100),
            sentenceIndex: index,
            suggestedDescription: `Luogo estratto automaticamente dal testo.`,
            suggestedCategory: 'setting'
          });
          existingNames.add(location);
        }
      });
    });

    // Estrai oggetti (sostantivi importanti)
    const objectPatterns = [
      /\b(spada|libro|anello|chiave|corona|gemma|cristallo|pozione|pergamena|mappa|tesoro|arma|scudo|armatura|bastone|bacchetta)\b/gi
    ];

    objectPatterns.forEach(pattern => {
      const matches = trimmed.match(pattern) || [];
      matches.forEach(match => {
        const object = match.toLowerCase();
        if (!existingNames.has(object)) {
          elements.push({
            name: object,
            type: 'object',
            confidence: 0.6,
            context: trimmed.substring(0, 100),
            sentenceIndex: index,
            suggestedDescription: `Oggetto estratto automaticamente dal testo.`,
            suggestedFunction: 'Oggetto di trama'
          });
          existingNames.add(object);
        }
      });
    });
  });

  // Rimuovi duplicati e ordina per confidenza
  const uniqueElements = elements.filter((element, index, self) =>
    index === self.findIndex(e => e.name.toLowerCase() === element.name.toLowerCase() && e.type === element.type)
  );

  return uniqueElements.sort((a, b) => b.confidence - a.confidence).slice(0, 20); // Massimo 20 elementi
}

module.exports = router;
