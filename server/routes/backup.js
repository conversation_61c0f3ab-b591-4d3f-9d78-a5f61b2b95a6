const express = require('express');
const fs = require('fs');
const path = require('path');
const database = require('../database/database');

const router = express.Router();

// GET /api/backup/export - Esporta tutti i dati
router.get('/export', async (req, res) => {
  try {
    // Ottieni tutti i dati
    const projects = await database.all('SELECT * FROM projects ORDER BY created_at');
    const projectVersions = await database.all('SELECT * FROM project_versions ORDER BY timestamp');
    const chapters = await database.all('SELECT * FROM chapters ORDER BY position, created_at');
    const subchapters = await database.all('SELECT * FROM subchapters ORDER BY position, created_at');
    const liveVersions = await database.all('SELECT * FROM subchapter_live_versions');
    const backupVersions = await database.all('SELECT * FROM subchapter_backup_versions ORDER BY created_at');
    const plotEvents = await database.all('SELECT * FROM plot_events ORDER BY created_at');
    const characters = await database.all('SELECT * FROM characters ORDER BY created_at');

    // Struttura i dati per l'export
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0.0',
        totalProjects: projects.length,
        totalChapters: chapters.length,
        totalSubchapters: subchapters.length
      },
      projects: projects.map(project => ({
        ...project,
        versions: projectVersions
          .filter(v => v.project_id === project.id)
          .map(v => ({
            ...v,
            analysis: v.analysis ? JSON.parse(v.analysis) : null,
            scores: v.scores ? JSON.parse(v.scores) : null
          }))
      })),
      chapters: chapters.map(chapter => ({
        ...chapter,
        subchapters: subchapters
          .filter(s => s.chapter_id === chapter.id)
          .map(sub => {
            const liveVersion = liveVersions.find(lv => lv.subchapter_id === sub.id);
            const backups = backupVersions
              .filter(bv => bv.subchapter_id === sub.id)
              .map(bv => ({
                ...bv,
                analysis: bv.analysis ? JSON.parse(bv.analysis) : null
              }));

            return {
              ...sub,
              liveVersion: liveVersion ? {
                ...liveVersion,
                analysis: liveVersion.analysis ? JSON.parse(liveVersion.analysis) : null
              } : null,
              backupVersions: backups
            };
          })
      })),
      plotEvents: plotEvents,
      characters: characters.map(char => ({
        ...char,
        physical_traits: char.physical_traits ? JSON.parse(char.physical_traits) : {},
        personality_traits: char.personality_traits ? JSON.parse(char.personality_traits) : {},
        plot_impact: char.plot_impact ? JSON.parse(char.plot_impact) : {}
      }))
    };

    // Imposta headers per download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="writer-tool-backup-${new Date().toISOString().split('T')[0]}.json"`);
    
    res.json(exportData);
  } catch (error) {
    console.error('Errore export:', error);
    res.status(500).json({ error: 'Errore nell\'export dei dati' });
  }
});

// POST /api/backup/import - Importa dati da backup
router.post('/import', async (req, res) => {
  try {
    const { data, overwrite = false } = req.body;

    if (!data || !data.metadata) {
      return res.status(400).json({ error: 'Dati di backup non validi' });
    }

    let importedCounts = {
      projects: 0,
      chapters: 0,
      subchapters: 0,
      plotEvents: 0,
      characters: 0
    };

    // Se overwrite è true, pulisci il database
    if (overwrite) {
      await database.run('DELETE FROM characters');
      await database.run('DELETE FROM plot_events');
      await database.run('DELETE FROM subchapter_backup_versions');
      await database.run('DELETE FROM subchapter_live_versions');
      await database.run('DELETE FROM subchapters');
      await database.run('DELETE FROM chapters');
      await database.run('DELETE FROM project_versions');
      await database.run('DELETE FROM projects');
    }

    // Importa progetti
    if (data.projects) {
      for (const project of data.projects) {
        // Controlla se il progetto esiste già
        const existing = await database.get('SELECT id FROM projects WHERE id = ?', [project.id]);
        
        if (!existing || overwrite) {
          await database.run(`
            INSERT OR REPLACE INTO projects (id, title, description, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
          `, [project.id, project.title, project.description, project.created_at, project.updated_at]);

          // Importa versioni del progetto
          if (project.versions) {
            for (const version of project.versions) {
              await database.run(`
                INSERT OR REPLACE INTO project_versions 
                (id, project_id, name, text, word_count, analysis, scores, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                version.id,
                version.project_id,
                version.name,
                version.text,
                version.word_count,
                version.analysis ? JSON.stringify(version.analysis) : null,
                version.scores ? JSON.stringify(version.scores) : null,
                version.timestamp
              ]);
            }
          }

          importedCounts.projects++;
        }
      }
    }

    // Importa capitoli
    if (data.chapters) {
      for (const chapter of data.chapters) {
        const existing = await database.get('SELECT id FROM chapters WHERE id = ?', [chapter.id]);
        
        if (!existing || overwrite) {
          await database.run(`
            INSERT OR REPLACE INTO chapters (id, title, description, position, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [chapter.id, chapter.title, chapter.description, chapter.position, chapter.created_at, chapter.updated_at]);

          // Importa sottocapitoli
          if (chapter.subchapters) {
            for (const subchapter of chapter.subchapters) {
              await database.run(`
                INSERT OR REPLACE INTO subchapters (id, chapter_id, title, description, position, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
              `, [
                subchapter.id,
                subchapter.chapter_id,
                subchapter.title,
                subchapter.description,
                subchapter.position,
                subchapter.created_at,
                subchapter.updated_at
              ]);

              // Importa versione live
              if (subchapter.liveVersion) {
                const lv = subchapter.liveVersion;
                await database.run(`
                  INSERT OR REPLACE INTO subchapter_live_versions 
                  (subchapter_id, text, word_count, analysis, source_project_id, source_version_id, 
                   source_project_title, source_version_name, linked_at, updated_at)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                  subchapter.id,
                  lv.text,
                  lv.word_count,
                  lv.analysis ? JSON.stringify(lv.analysis) : null,
                  lv.source_project_id,
                  lv.source_version_id,
                  lv.source_project_title,
                  lv.source_version_name,
                  lv.linked_at,
                  lv.updated_at
                ]);
              }

              // Importa versioni backup
              if (subchapter.backupVersions) {
                for (const backup of subchapter.backupVersions) {
                  await database.run(`
                    INSERT OR REPLACE INTO subchapter_backup_versions 
                    (id, subchapter_id, name, text, word_count, analysis, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                  `, [
                    backup.id,
                    backup.subchapter_id,
                    backup.name,
                    backup.text,
                    backup.word_count,
                    backup.analysis ? JSON.stringify(backup.analysis) : null,
                    backup.created_at
                  ]);
                }
              }

              importedCounts.subchapters++;
            }
          }

          importedCounts.chapters++;
        }
      }
    }

    // Importa eventi plot
    if (data.plotEvents) {
      for (const event of data.plotEvents) {
        const existing = await database.get('SELECT id FROM plot_events WHERE id = ?', [event.id]);
        
        if (!existing || overwrite) {
          await database.run(`
            INSERT OR REPLACE INTO plot_events 
            (id, project_id, title, description, type, importance, chapter_reference, automatic, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            event.id,
            event.project_id,
            event.title,
            event.description,
            event.type,
            event.importance,
            event.chapter_reference,
            event.automatic,
            event.created_at,
            event.updated_at
          ]);

          importedCounts.plotEvents++;
        }
      }
    }

    // Importa personaggi
    if (data.characters) {
      for (const character of data.characters) {
        const existing = await database.get('SELECT id FROM characters WHERE id = ?', [character.id]);
        
        if (!existing || overwrite) {
          await database.run(`
            INSERT OR REPLACE INTO characters 
            (id, project_id, name, description, physical_traits, personality_traits, plot_impact, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            character.id,
            character.project_id,
            character.name,
            character.description,
            character.physical_traits ? JSON.stringify(character.physical_traits) : null,
            character.personality_traits ? JSON.stringify(character.personality_traits) : null,
            character.plot_impact ? JSON.stringify(character.plot_impact) : null,
            character.created_at,
            character.updated_at
          ]);

          importedCounts.characters++;
        }
      }
    }

    res.json({
      message: 'Import completato con successo',
      imported: importedCounts,
      overwrite
    });
  } catch (error) {
    console.error('Errore import:', error);
    res.status(500).json({ error: 'Errore nell\'import dei dati' });
  }
});

// POST /api/backup/database - Crea backup del database
router.post('/database', async (req, res) => {
  try {
    const backupDir = path.join(__dirname, '../backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `database-backup-${timestamp}.db`);

    await database.backup(backupPath);

    res.json({
      message: 'Backup database creato con successo',
      backupPath,
      timestamp
    });
  } catch (error) {
    console.error('Errore backup database:', error);
    res.status(500).json({ error: 'Errore nella creazione backup database' });
  }
});

module.exports = router;
