const express = require('express');
const database = require('../database/database');

const router = express.Router();

// POST /api/analysis/text - Analizza un testo (endpoint per analisi diretta)
router.post('/text', async (req, res) => {
  try {
    const { text, useAI = false, openRouterApiKey } = req.body;

    if (!text || !text.trim()) {
      return res.status(400).json({ error: 'Testo richiesto per l\'analisi' });
    }

    // Qui implementeremo la logica di analisi
    // Per ora restituiamo un'analisi di base
    const analysis = await performTextAnalysis(text, useAI, openRouterApiKey);

    res.json({
      analysis,
      wordCount: text.split(/\s+/).filter(word => word.length > 0).length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Errore analisi testo:', error);
    res.status(500).json({ error: 'Errore nell\'analisi del testo' });
  }
});

// POST /api/analysis/project/:projectId/version/:versionId - Analizza una versione specifica
router.post('/project/:projectId/version/:versionId', async (req, res) => {
  try {
    const { projectId, versionId } = req.params;
    const { useAI = false, openRouterApiKey } = req.body;

    // Ottieni la versione
    const version = await database.get(`
      SELECT * FROM project_versions 
      WHERE id = ? AND project_id = ?
    `, [versionId, projectId]);

    if (!version) {
      return res.status(404).json({ error: 'Versione non trovata' });
    }

    if (!version.text) {
      return res.status(400).json({ error: 'Nessun testo da analizzare' });
    }

    // Esegui l'analisi
    const analysis = await performTextAnalysis(version.text, useAI, openRouterApiKey);

    // Aggiorna la versione con l'analisi
    await database.run(`
      UPDATE project_versions 
      SET analysis = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [JSON.stringify(analysis), versionId]);

    res.json({
      analysis,
      versionId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Errore analisi versione:', error);
    res.status(500).json({ error: 'Errore nell\'analisi della versione' });
  }
});

// POST /api/analysis/subchapter/:subchapterId - Analizza un sottocapitolo
router.post('/subchapter/:subchapterId', async (req, res) => {
  try {
    const { subchapterId } = req.params;
    const { useAI = false, openRouterApiKey } = req.body;

    // Ottieni la versione live del sottocapitolo
    const liveVersion = await database.get(`
      SELECT * FROM subchapter_live_versions 
      WHERE subchapter_id = ?
    `, [subchapterId]);

    if (!liveVersion || !liveVersion.text) {
      return res.status(404).json({ error: 'Nessun testo da analizzare nel sottocapitolo' });
    }

    // Esegui l'analisi
    const analysis = await performTextAnalysis(liveVersion.text, useAI, openRouterApiKey);

    // Aggiorna la versione live con l'analisi
    await database.run(`
      UPDATE subchapter_live_versions 
      SET analysis = ?, updated_at = CURRENT_TIMESTAMP
      WHERE subchapter_id = ?
    `, [JSON.stringify(analysis), subchapterId]);

    res.json({
      analysis,
      subchapterId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Errore analisi sottocapitolo:', error);
    res.status(500).json({ error: 'Errore nell\'analisi del sottocapitolo' });
  }
});

// Funzione di analisi del testo (da implementare)
async function performTextAnalysis(text, useAI = false, openRouterApiKey = null) {
  // Analisi di base (algoritmo locale)
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = text.split(/\s+/).filter(word => word.length > 0);
  const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

  // Calcoli di base
  const wordCount = words.length;
  const sentenceCount = sentences.length;
  const paragraphCount = paragraphs.length;
  const avgWordsPerSentence = sentenceCount > 0 ? Math.round(wordCount / sentenceCount) : 0;
  const avgSentencesPerParagraph = paragraphCount > 0 ? Math.round(sentenceCount / paragraphCount) : 0;

  // Analisi leggibilità (Flesch Reading Ease semplificato)
  const avgSyllablesPerWord = 1.5; // Stima approssimativa per l'italiano
  const fleschScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
  const readabilityScore = Math.max(0, Math.min(100, Math.round(fleschScore)));

  // Rilevamento dialoghi (semplificato)
  const dialogueLines = text.split('\n').filter(line => 
    line.trim().includes('"') || line.trim().includes('«') || line.trim().includes('—')
  );
  const dialoguePercentage = Math.round((dialogueLines.length / text.split('\n').length) * 100);

  // Analisi sentimenti di base
  const positiveWords = ['bello', 'buono', 'felice', 'gioia', 'amore', 'sorriso', 'pace'];
  const negativeWords = ['brutto', 'cattivo', 'triste', 'dolore', 'odio', 'pianto', 'guerra'];
  
  const textLower = text.toLowerCase();
  const positiveCount = positiveWords.reduce((count, word) => 
    count + (textLower.match(new RegExp(word, 'g')) || []).length, 0
  );
  const negativeCount = negativeWords.reduce((count, word) => 
    count + (textLower.match(new RegExp(word, 'g')) || []).length, 0
  );

  const sentimentScore = positiveCount - negativeCount;
  const sentimentLabel = sentimentScore > 0 ? 'Positivo' : sentimentScore < 0 ? 'Negativo' : 'Neutro';

  const analysis = {
    // Statistiche di base
    wordCount,
    sentenceCount,
    paragraphCount,
    avgWordsPerSentence,
    avgSentencesPerParagraph,

    // Leggibilità
    readabilityScore,
    readabilityLevel: getReadabilityLevel(readabilityScore),

    // Dialoghi
    dialoguePercentage,
    dialogueLines: dialogueLines.length,

    // Sentimenti
    sentiment: {
      score: sentimentScore,
      label: sentimentLabel,
      positiveWords: positiveCount,
      negativeWords: negativeCount
    },

    // Punteggi complessivi
    scores: {
      overall: Math.round((readabilityScore + (100 - Math.abs(sentimentScore * 10))) / 2),
      readability: readabilityScore,
      engagement: Math.min(100, dialoguePercentage * 2),
      flow: Math.max(0, 100 - Math.abs(avgWordsPerSentence - 15) * 3)
    },

    // Metadati
    analysisType: useAI ? 'AI' : 'local',
    timestamp: new Date().toISOString()
  };

  // Se richiesta analisi AI e disponibile API key
  if (useAI && openRouterApiKey) {
    try {
      const aiAnalysis = await performAIAnalysis(text, openRouterApiKey);
      analysis.aiAnalysis = aiAnalysis;
      analysis.analysisType = 'AI';
    } catch (error) {
      console.error('Errore analisi AI:', error);
      // Continua con analisi locale
    }
  }

  return analysis;
}

// Funzione per determinare il livello di leggibilità
function getReadabilityLevel(score) {
  if (score >= 90) return 'Molto facile';
  if (score >= 80) return 'Facile';
  if (score >= 70) return 'Abbastanza facile';
  if (score >= 60) return 'Standard';
  if (score >= 50) return 'Abbastanza difficile';
  if (score >= 30) return 'Difficile';
  return 'Molto difficile';
}

// Funzione per analisi AI (da implementare con OpenRouter)
async function performAIAnalysis(text, apiKey) {
  // Placeholder per analisi AI
  // Qui implementeremo la chiamata a OpenRouter API
  return {
    characters: [],
    plotEvents: [],
    themes: [],
    style: {},
    suggestions: []
  };
}

module.exports = router;
