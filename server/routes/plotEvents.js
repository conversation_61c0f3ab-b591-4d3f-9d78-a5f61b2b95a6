const express = require('express');
const { v4: uuidv4 } = require('uuid');
const database = require('../database/database');

const router = express.Router();

// GET /api/plot-events - Ottieni tutti gli eventi del plot
router.get('/', async (req, res) => {
  try {
    const { projectId } = req.query;
    
    let sql = 'SELECT * FROM plot_events';
    let params = [];
    
    if (projectId) {
      sql += ' WHERE project_id = ?';
      params.push(projectId);
    }
    
    sql += ' ORDER BY created_at DESC';
    
    const events = await database.all(sql, params);
    res.json(events);
  } catch (error) {
    console.error('Errore GET eventi plot:', error);
    res.status(500).json({ error: 'Errore nel recupero eventi plot' });
  }
});

// POST /api/plot-events - Crea un nuovo evento del plot
router.post('/', async (req, res) => {
  try {
    const { projectId, title, description, type, importance, chapterReference, automatic } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Titolo evento richiesto' });
    }

    const eventId = uuidv4();

    await database.run(`
      INSERT INTO plot_events (id, project_id, title, description, type, importance, chapter_reference, automatic)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [eventId, projectId, title, description, type, importance || 1, chapterReference, automatic || false]);

    const newEvent = await database.get(`
      SELECT * FROM plot_events WHERE id = ?
    `, [eventId]);

    res.status(201).json(newEvent);
  } catch (error) {
    console.error('Errore POST evento plot:', error);
    res.status(500).json({ error: 'Errore nella creazione evento plot' });
  }
});

// PUT /api/plot-events/:id - Aggiorna un evento del plot
router.put('/:id', async (req, res) => {
  try {
    const { title, description, type, importance, chapterReference } = req.body;

    await database.run(`
      UPDATE plot_events
      SET title = ?, description = ?, type = ?, importance = ?, chapter_reference = ?, updated_at = ?
      WHERE id = ?
    `, [title, description, type, importance, chapterReference, new Date().toISOString(), req.params.id]);

    const updatedEvent = await database.get(`
      SELECT * FROM plot_events WHERE id = ?
    `, [req.params.id]);

    if (!updatedEvent) {
      return res.status(404).json({ error: 'Evento non trovato' });
    }

    res.json(updatedEvent);
  } catch (error) {
    console.error('Errore PUT evento plot:', error);
    res.status(500).json({ error: 'Errore nell\'aggiornamento evento plot' });
  }
});

// DELETE /api/plot-events/:id - Elimina un evento del plot
router.delete('/:id', async (req, res) => {
  try {
    const result = await database.run(`
      DELETE FROM plot_events WHERE id = ?
    `, [req.params.id]);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Evento non trovato' });
    }

    res.json({ message: 'Evento eliminato con successo' });
  } catch (error) {
    console.error('Errore DELETE evento plot:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione evento plot' });
  }
});

module.exports = router;
