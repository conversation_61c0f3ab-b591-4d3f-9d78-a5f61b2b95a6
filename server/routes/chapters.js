const express = require('express');
const { v4: uuidv4 } = require('uuid');
const database = require('../database/database');

const router = express.Router();

// Funzione helper per creare progetti automaticamente per i sottocapitoli
async function createProjectForSubchapter(subchapter, subchapterData) {
  try {
    console.log(`🚀 Creando progetto per sottocapitolo: ${subchapter.title}`);
    console.log(`📝 Contenuto sottocapitolo:`, {
      hasContent: !!subchapterData.content,
      contentLength: subchapterData.content ? subchapterData.content.length : 0,
      contentPreview: subchapterData.content ? subchapterData.content.substring(0, 50) + '...' : 'VUOTO'
    });

    const projectTitle = subchapter.title; // Es: "1.1", "2.3", etc.

    // Verifica se esiste già un progetto con questo titolo
    const existingProject = await database.get(`
      SELECT * FROM projects WHERE title = ?
    `, [projectTitle]);

    console.log(`🔍 Progetto esistente per "${projectTitle}":`, existingProject ? 'TROVATO' : 'NON TROVATO');

    let projectId;

    if (existingProject) {
      // Progetto esiste già, usa quello esistente
      projectId = existingProject.id;
      console.log(`📁 Progetto esistente trovato: ${projectTitle}`);
    } else {
      // Crea nuovo progetto
      projectId = uuidv4();
      const now = new Date().toISOString();

      await database.run(`
        INSERT INTO projects (id, title, description, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `, [
        projectId,
        projectTitle,
        `Progetto creato automaticamente per ${subchapter.title}`,
        now,
        now
      ]);

      console.log(`📁 Nuovo progetto creato: ${projectTitle}`);
    }

    // Crea sempre una nuova versione
    const versionId = uuidv4();
    const text = subchapterData.content || '';
    const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
    const now = new Date().toISOString();

    await database.run(`
      INSERT INTO project_versions (id, project_id, name, text, word_count, analysis, scores, timestamp)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      versionId,
      projectId,
      `Versione importata ${now.split('T')[0]}`,
      text,
      wordCount,
      null,
      null,
      now
    ]);

    // Aggiorna timestamp del progetto
    await database.run(`
      UPDATE projects SET updated_at = ? WHERE id = ?
    `, [now, projectId]);

    // Collega il sottocapitolo al progetto/versione tramite subchapter_live_versions
    await database.run(`
      INSERT OR REPLACE INTO subchapter_live_versions
      (subchapter_id, text, word_count, source_project_id, source_version_id,
       source_project_title, source_version_name, linked_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      subchapter.id,
      text,
      wordCount,
      projectId,
      versionId,
      projectTitle,
      `Versione importata ${now.split('T')[0]}`,
      now,
      now
    ]);

    console.log(`🔗 Sottocapitolo ${subchapter.title} collegato a progetto ${projectTitle} versione ${versionId.substring(0, 8)}`);

    return { projectId, versionId };
  } catch (error) {
    console.error(`❌ Errore creazione progetto per sottocapitolo ${subchapter.title}:`, error);
    throw error;
  }
}

// GET /api/chapters - Ottieni tutti i capitoli
router.get('/', async (req, res) => {
  try {
    const chapters = await database.all(`
      SELECT * FROM chapters 
      ORDER BY position ASC, created_at ASC
    `);

    // Per ogni capitolo, ottieni i sottocapitoli
    for (const chapter of chapters) {
      const subchapters = await database.all(`
        SELECT s.*,
               slv.text as live_text,
               slv.word_count as live_word_count,
               slv.analysis as live_analysis,
               slv.source_project_id,
               slv.source_version_id,
               slv.source_project_title,
               slv.source_version_name,
               slv.linked_at,
               slv.updated_at as live_updated_at
        FROM subchapters s
        LEFT JOIN subchapter_live_versions slv ON s.id = slv.subchapter_id
        WHERE s.chapter_id = ?
        ORDER BY s.position ASC, s.created_at ASC
      `, [chapter.id]);

      console.log(`📖 Sottocapitoli per capitolo ${chapter.title}:`,
        subchapters.map(sub => ({
          id: sub.id,
          title: sub.title,
          hasLiveText: !!sub.live_text,
          sourceProjectId: sub.source_project_id,
          sourceVersionId: sub.source_version_id,
          linkedAt: sub.linked_at
        }))
      );

      // Per ogni sottocapitolo, ottieni le versioni backup
      for (const subchapter of subchapters) {
        const backupVersions = await database.all(`
          SELECT * FROM subchapter_backup_versions 
          WHERE subchapter_id = ?
          ORDER BY created_at DESC
        `, [subchapter.id]);

        // Struttura dati compatibile con il frontend
        subchapter.liveVersion = subchapter.live_text ? {
          text: subchapter.live_text,
          wordCount: subchapter.live_word_count || 0,
          analysis: subchapter.live_analysis ? JSON.parse(subchapter.live_analysis) : null,
          updatedAt: subchapter.live_updated_at,
          sourceLink: subchapter.source_project_id ? {
            projectId: subchapter.source_project_id,
            versionId: subchapter.source_version_id,
            projectTitle: subchapter.source_project_title,
            versionName: subchapter.source_version_name,
            linkedAt: subchapter.linked_at
          } : null
        } : null;

        subchapter.backupVersions = backupVersions.map(backup => ({
          ...backup,
          analysis: backup.analysis ? JSON.parse(backup.analysis) : null
        }));

        // Rimuovi i campi temporanei
        delete subchapter.live_text;
        delete subchapter.live_word_count;
        delete subchapter.live_analysis;
        delete subchapter.source_project_id;
        delete subchapter.source_version_id;
        delete subchapter.source_project_title;
        delete subchapter.source_version_name;
        delete subchapter.linked_at;
        delete subchapter.live_updated_at;
      }

      chapter.subchapters = subchapters;
    }

    res.json(chapters);
  } catch (error) {
    console.error('Errore GET capitoli:', error);
    res.status(500).json({ error: 'Errore nel recupero capitoli' });
  }
});

// POST /api/chapters - Crea un nuovo capitolo
router.post('/', async (req, res) => {
  try {
    const { title, description, position } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Titolo capitolo richiesto' });
    }

    const chapterId = uuidv4();

    await database.run(`
      INSERT INTO chapters (id, title, description, position)
      VALUES (?, ?, ?, ?)
    `, [chapterId, title, description, position || 0]);

    const newChapter = await database.get(`
      SELECT * FROM chapters WHERE id = ?
    `, [chapterId]);

    newChapter.subchapters = [];

    res.status(201).json(newChapter);
  } catch (error) {
    console.error('Errore POST capitolo:', error);
    res.status(500).json({ error: 'Errore nella creazione capitolo' });
  }
});

// PUT /api/chapters/:id - Aggiorna un capitolo
router.put('/:id', async (req, res) => {
  try {
    const { title, description, position } = req.body;

    await database.run(`
      UPDATE chapters
      SET title = ?, description = ?, position = ?, updated_at = ?
      WHERE id = ?
    `, [title, description, position, new Date().toISOString(), req.params.id]);

    const updatedChapter = await database.get(`
      SELECT * FROM chapters WHERE id = ?
    `, [req.params.id]);

    if (!updatedChapter) {
      return res.status(404).json({ error: 'Capitolo non trovato' });
    }

    res.json(updatedChapter);
  } catch (error) {
    console.error('Errore PUT capitolo:', error);
    res.status(500).json({ error: 'Errore nell\'aggiornamento capitolo' });
  }
});

// DELETE /api/chapters/:id - Elimina un capitolo
router.delete('/:id', async (req, res) => {
  try {
    const result = await database.run(`
      DELETE FROM chapters WHERE id = ?
    `, [req.params.id]);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Capitolo non trovato' });
    }

    res.json({ message: 'Capitolo eliminato con successo' });
  } catch (error) {
    console.error('Errore DELETE capitolo:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione capitolo' });
  }
});

// POST /api/chapters/:id/subchapters - Crea un nuovo sottocapitolo
router.post('/:id/subchapters', async (req, res) => {
  try {
    const { title, description, text, position } = req.body;
    const chapterId = req.params.id;

    if (!title) {
      return res.status(400).json({ error: 'Titolo sottocapitolo richiesto' });
    }

    // Verifica che il capitolo esista
    const chapter = await database.get(`
      SELECT * FROM chapters WHERE id = ?
    `, [chapterId]);

    if (!chapter) {
      return res.status(404).json({ error: 'Capitolo non trovato' });
    }

    const subchapterId = uuidv4();

    // Crea il sottocapitolo
    await database.run(`
      INSERT INTO subchapters (id, chapter_id, title, description, position)
      VALUES (?, ?, ?, ?, ?)
    `, [subchapterId, chapterId, title, description, position || 0]);

    // Se c'è del testo, crea la versione live
    if (text) {
      const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
      
      await database.run(`
        INSERT INTO subchapter_live_versions (subchapter_id, text, word_count)
        VALUES (?, ?, ?)
      `, [subchapterId, text, wordCount]);
    }

    // Aggiorna timestamp del capitolo
    await database.run(`
      UPDATE chapters SET updated_at = ? WHERE id = ?
    `, [new Date().toISOString(), chapterId]);

    // Restituisci il sottocapitolo creato
    const newSubchapter = await database.get(`
      SELECT s.*, 
             slv.text as live_text,
             slv.word_count as live_word_count,
             slv.analysis as live_analysis,
             slv.updated_at as live_updated_at
      FROM subchapters s
      LEFT JOIN subchapter_live_versions slv ON s.id = slv.subchapter_id
      WHERE s.id = ?
    `, [subchapterId]);

    newSubchapter.liveVersion = newSubchapter.live_text ? {
      text: newSubchapter.live_text,
      wordCount: newSubchapter.live_word_count || 0,
      analysis: newSubchapter.live_analysis ? JSON.parse(newSubchapter.live_analysis) : null,
      updatedAt: newSubchapter.live_updated_at,
      sourceLink: null
    } : null;

    newSubchapter.backupVersions = [];

    // Pulisci i campi temporanei
    delete newSubchapter.live_text;
    delete newSubchapter.live_word_count;
    delete newSubchapter.live_analysis;
    delete newSubchapter.live_updated_at;

    res.status(201).json(newSubchapter);
  } catch (error) {
    console.error('Errore POST sottocapitolo:', error);
    res.status(500).json({ error: 'Errore nella creazione sottocapitolo' });
  }
});

// PUT /api/chapters/:chapterId/subchapters/:subchapterId - Aggiorna un sottocapitolo
router.put('/:chapterId/subchapters/:subchapterId', async (req, res) => {
  try {
    const { title, description, text, saveAsBackup } = req.body;
    const { chapterId, subchapterId } = req.params;

    // Aggiorna i metadati del sottocapitolo
    if (title || description) {
      await database.run(`
        UPDATE subchapters
        SET title = COALESCE(?, title),
            description = COALESCE(?, description),
            updated_at = ?
        WHERE id = ? AND chapter_id = ?
      `, [title, description, new Date().toISOString(), subchapterId, chapterId]);
    }

    // Se c'è del testo da aggiornare
    if (text !== undefined) {
      const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;

      if (saveAsBackup) {
        // Salva la versione corrente come backup prima di aggiornare
        const currentLive = await database.get(`
          SELECT * FROM subchapter_live_versions WHERE subchapter_id = ?
        `, [subchapterId]);

        if (currentLive && currentLive.text) {
          const backupId = uuidv4();
          await database.run(`
            INSERT INTO subchapter_backup_versions (id, subchapter_id, name, text, word_count, analysis)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [
            backupId,
            subchapterId,
            `Backup ${new Date().toLocaleDateString()}`,
            currentLive.text,
            currentLive.word_count,
            currentLive.analysis
          ]);
        }
      }

      // Aggiorna o crea la versione live
      await database.run(`
        INSERT OR REPLACE INTO subchapter_live_versions
        (subchapter_id, text, word_count, updated_at)
        VALUES (?, ?, ?, ?)
      `, [subchapterId, text, wordCount, new Date().toISOString()]);
    }

    res.json({ message: 'Sottocapitolo aggiornato con successo' });
  } catch (error) {
    console.error('Errore PUT sottocapitolo:', error);
    res.status(500).json({ error: 'Errore nell\'aggiornamento sottocapitolo' });
  }
});

// POST /api/chapters/:chapterId/subchapters/:subchapterId/link - Collega una versione
router.post('/:chapterId/subchapters/:subchapterId/link', async (req, res) => {
  try {
    const { projectId, versionId, projectTitle, versionName, text, wordCount, analysis } = req.body;
    const { subchapterId } = req.params;

    console.log('🔗 Collegamento versione al sottocapitolo:', {
      subchapterId,
      projectId,
      versionId,
      projectTitle,
      versionName,
      textLength: text?.length || 0,
      wordCount
    });

    // Aggiorna la versione live con il collegamento
    const now = new Date().toISOString();
    await database.run(`
      INSERT OR REPLACE INTO subchapter_live_versions
      (subchapter_id, text, word_count, analysis, source_project_id, source_version_id,
       source_project_title, source_version_name, linked_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      subchapterId,
      text || '',
      wordCount || 0,
      analysis ? JSON.stringify(analysis) : null,
      projectId,
      versionId,
      projectTitle,
      versionName,
      now,
      now
    ]);

    console.log('✅ Collegamento salvato nel database');

    // Verifica che il collegamento sia stato salvato
    const linkedData = await database.get(`
      SELECT * FROM subchapter_live_versions WHERE subchapter_id = ?
    `, [subchapterId]);

    console.log('🔍 Dati collegati salvati:', {
      subchapterId: linkedData?.subchapter_id,
      sourceProjectId: linkedData?.source_project_id,
      sourceVersionId: linkedData?.source_version_id,
      hasText: !!linkedData?.text,
      linkedAt: linkedData?.linked_at
    });

    res.json({ message: 'Collegamento creato con successo' });
  } catch (error) {
    console.error('Errore collegamento versione:', error);
    res.status(500).json({ error: 'Errore nel collegamento versione' });
  }
});

// DELETE /api/chapters/:chapterId/subchapters/:subchapterId/link - Scollega una versione
router.delete('/:chapterId/subchapters/:subchapterId/link', async (req, res) => {
  try {
    const { subchapterId } = req.params;

    await database.run(`
      UPDATE subchapter_live_versions 
      SET source_project_id = NULL,
          source_version_id = NULL,
          source_project_title = NULL,
          source_version_name = NULL,
          linked_at = NULL,
          updated_at = ?
      WHERE subchapter_id = ?
    `, [new Date().toISOString(), subchapterId]);

    res.json({ message: 'Collegamento rimosso con successo' });
  } catch (error) {
    console.error('Errore rimozione collegamento:', error);
    res.status(500).json({ error: 'Errore nella rimozione collegamento' });
  }
});

// DELETE /api/chapters/:chapterId/subchapters/:subchapterId - Elimina un sottocapitolo
router.delete('/:chapterId/subchapters/:subchapterId', async (req, res) => {
  try {
    const { subchapterId } = req.params;

    // Elimina prima dalla tabella live_versions
    await database.run(`
      DELETE FROM subchapter_live_versions WHERE subchapter_id = ?
    `, [subchapterId]);

    // Elimina dalla tabella principale
    const result = await database.run(`
      DELETE FROM subchapters WHERE id = ?
    `, [subchapterId]);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Sottocapitolo non trovato' });
    }

    res.json({ message: 'Sottocapitolo eliminato con successo' });
  } catch (error) {
    console.error('Errore DELETE sottocapitolo:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione sottocapitolo' });
  }
});

// POST /api/chapters/import - Importa struttura di capitoli e sottocapitoli
router.post('/import', async (req, res) => {
  try {
    const { structure } = req.body;

    if (!structure || !Array.isArray(structure)) {
      return res.status(400).json({ error: 'Struttura non valida' });
    }

    const createdChapters = [];

    for (let i = 0; i < structure.length; i++) {
      const chapterData = structure[i];

      // Crea il capitolo
      const chapterUUID = require('crypto').randomUUID();
      const chapterResult = await database.run(`
        INSERT INTO chapters (id, title, description, position, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        chapterUUID,
        chapterData.title,
        `Importato automaticamente - ${chapterData.number}`,
        i + 1,
        new Date().toISOString(),
        new Date().toISOString()
      ]);

      const chapter = await database.get('SELECT * FROM chapters WHERE id = ?', [chapterUUID]);

      // Se il capitolo ha contenuto diretto (senza sottocapitoli), crea un sottocapitolo
      if (chapterData.content && chapterData.content.trim() && chapterData.subchapters.length === 0) {
        const subchapterUUID = require('crypto').randomUUID();
        const subchapterResult = await database.run(`
          INSERT INTO subchapters (id, chapter_id, title, description, position, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          subchapterUUID,
          chapter.id,
          'Contenuto principale',
          'Contenuto importato automaticamente',
          1,
          new Date().toISOString(),
          new Date().toISOString()
        ]);

        const subchapter = await database.get('SELECT * FROM subchapters WHERE id = ?', [subchapterUUID]);

        // Salva il contenuto come versione live
        const wordCount = chapterData.content.split(/\s+/).filter(word => word.length > 0).length;

        await database.run(`
          INSERT INTO subchapter_live_versions (subchapter_id, text, word_count, updated_at)
          VALUES (?, ?, ?, ?)
        `, [subchapter.id, chapterData.content.trim(), wordCount, new Date().toISOString()]);
      }

      // Crea i sottocapitoli
      for (let j = 0; j < chapterData.subchapters.length; j++) {
        const subchapterData = chapterData.subchapters[j];

        const subchapterUUID = require('crypto').randomUUID();
        const subchapterResult = await database.run(`
          INSERT INTO subchapters (id, chapter_id, title, description, position, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          subchapterUUID,
          chapter.id,
          subchapterData.title,
          `Importato automaticamente - ${subchapterData.number}`,
          j + 1,
          new Date().toISOString(),
          new Date().toISOString()
        ]);

        const subchapter = await database.get('SELECT * FROM subchapters WHERE id = ?', [subchapterUUID]);

        // Crea progetto automaticamente per il sottocapitolo
        // (questo salva già il contenuto come versione live con collegamento)
        await createProjectForSubchapter(subchapter, subchapterData);
      }

      createdChapters.push(chapter);
    }

    res.status(201).json({
      message: 'Struttura importata con successo',
      chapters: createdChapters.length,
      subchapters: structure.reduce((total, ch) => total + Math.max(ch.subchapters.length, ch.content ? 1 : 0), 0)
    });
  } catch (error) {
    console.error('Errore import struttura:', error);
    res.status(500).json({ error: 'Errore nell\'importazione struttura' });
  }
});

module.exports = router;
