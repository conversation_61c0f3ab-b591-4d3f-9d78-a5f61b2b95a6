const express = require('express');
const { v4: uuidv4 } = require('uuid');
const database = require('../database/database');

const router = express.Router();

// GET /api/projects - Ottieni tutti i progetti
router.get('/', async (req, res) => {
  try {
    const projects = await database.all(`
      SELECT * FROM projects 
      ORDER BY updated_at DESC
    `);

    // Per ogni progetto, ottieni le versioni
    for (const project of projects) {
      const versions = await database.all(`
        SELECT * FROM project_versions 
        WHERE project_id = ? 
        ORDER BY timestamp DESC
      `, [project.id]);

      // Parse JSON fields
      project.versions = versions.map(version => ({
        ...version,
        analysis: version.analysis ? JSON.parse(version.analysis) : null,
        scores: version.scores ? JSON.parse(version.scores) : null
      }));
    }

    res.json(projects);
  } catch (error) {
    console.error('Errore GET progetti:', error);
    res.status(500).json({ error: 'Errore nel recupero progetti' });
  }
});

// NOTA: Endpoint di pulizia rimosso per sicurezza dopo aver risolto il problema del loop infinito

// GET /api/projects/:id - Ottieni un progetto specifico
router.get('/:id', async (req, res) => {
  try {
    const project = await database.get(`
      SELECT * FROM projects WHERE id = ?
    `, [req.params.id]);

    if (!project) {
      return res.status(404).json({ error: 'Progetto non trovato' });
    }

    // Ottieni le versioni
    const versions = await database.all(`
      SELECT * FROM project_versions 
      WHERE project_id = ? 
      ORDER BY timestamp DESC
    `, [project.id]);

    project.versions = versions.map(version => ({
      ...version,
      analysis: version.analysis ? JSON.parse(version.analysis) : null,
      scores: version.scores ? JSON.parse(version.scores) : null
    }));

    res.json(project);
  } catch (error) {
    console.error('Errore GET progetto:', error);
    res.status(500).json({ error: 'Errore nel recupero progetto' });
  }
});

// POST /api/projects - Crea un nuovo progetto
router.post('/', async (req, res) => {
  try {
    const { title, description, text, analysis, scores } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Titolo progetto richiesto' });
    }

    const projectId = uuidv4();
    const versionId = uuidv4();

    // Crea il progetto
    const now = new Date().toISOString();
    await database.run(`
      INSERT INTO projects (id, title, description, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?)
    `, [projectId, title, description, now, now]);

    // Crea la prima versione se c'è del testo
    if (text && typeof text === 'string') {
      const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
      
      await database.run(`
        INSERT INTO project_versions (id, project_id, name, text, word_count, analysis, scores, timestamp)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        versionId,
        projectId,
        'Versione iniziale',
        text,
        wordCount,
        analysis ? JSON.stringify(analysis) : null,
        scores ? JSON.stringify(scores) : null,
        now
      ]);
    }

    // Restituisci il progetto creato
    const newProject = await database.get(`
      SELECT * FROM projects WHERE id = ?
    `, [projectId]);

    const versions = await database.all(`
      SELECT * FROM project_versions WHERE project_id = ?
    `, [projectId]);

    newProject.versions = versions.map(version => ({
      ...version,
      analysis: version.analysis ? JSON.parse(version.analysis) : null,
      scores: version.scores ? JSON.parse(version.scores) : null
    }));

    res.status(201).json(newProject);
  } catch (error) {
    console.error('Errore POST progetto:', error);
    res.status(500).json({ error: 'Errore nella creazione progetto' });
  }
});

// PUT /api/projects/:id - Aggiorna un progetto
router.put('/:id', async (req, res) => {
  try {
    const { title, description } = req.body;

    await database.run(`
      UPDATE projects
      SET title = ?, description = ?, updated_at = ?
      WHERE id = ?
    `, [title, description, new Date().toISOString(), req.params.id]);

    // Restituisci il progetto aggiornato
    const updatedProject = await database.get(`
      SELECT * FROM projects WHERE id = ?
    `, [req.params.id]);

    if (!updatedProject) {
      return res.status(404).json({ error: 'Progetto non trovato' });
    }

    res.json(updatedProject);
  } catch (error) {
    console.error('Errore PUT progetto:', error);
    res.status(500).json({ error: 'Errore nell\'aggiornamento progetto' });
  }
});

// DELETE /api/projects/:id - Elimina un progetto
router.delete('/:id', async (req, res) => {
  try {
    const result = await database.run(`
      DELETE FROM projects WHERE id = ?
    `, [req.params.id]);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Progetto non trovato' });
    }

    res.json({ message: 'Progetto eliminato con successo' });
  } catch (error) {
    console.error('Errore DELETE progetto:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione progetto' });
  }
});

// POST /api/projects/:id/versions - Crea una nuova versione
router.post('/:id/versions', async (req, res) => {
  try {
    const { name, text, analysis, scores } = req.body;
    const projectId = req.params.id;

    console.log('📝 Creazione nuova versione:', {
      projectId,
      name,
      textLength: text?.length || 0,
      textPreview: text?.substring(0, 100) || 'NESSUN TESTO',
      hasAnalysis: !!analysis,
      hasScores: !!scores
    });

    // Verifica che il progetto esista
    const project = await database.get(`
      SELECT * FROM projects WHERE id = ?
    `, [projectId]);

    if (!project) {
      return res.status(404).json({ error: 'Progetto non trovato' });
    }

    const versionId = uuidv4();
    const wordCount = text ? text.split(/\s+/).filter(word => word.length > 0).length : 0;

    console.log('🔢 Conteggio parole calcolato:', {
      wordCount,
      textLength: text?.length || 0,
      hasText: !!text
    });

    await database.run(`
      INSERT INTO project_versions (id, project_id, name, text, word_count, analysis, scores, timestamp)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      versionId,
      projectId,
      name || `Versione ${new Date().toLocaleDateString()}`,
      text || '',
      wordCount,
      analysis ? JSON.stringify(analysis) : null,
      scores ? JSON.stringify(scores) : null,
      new Date().toISOString()
    ]);

    console.log('💾 Versione salvata nel database');

    // Aggiorna timestamp del progetto
    await database.run(`
      UPDATE projects SET updated_at = ? WHERE id = ?
    `, [new Date().toISOString(), projectId]);

    // Restituisci la versione creata
    const newVersion = await database.get(`
      SELECT * FROM project_versions WHERE id = ?
    `, [versionId]);

    console.log('✅ Versione creata e recuperata:', {
      id: newVersion.id,
      name: newVersion.name,
      textLength: newVersion.text?.length || 0,
      wordCount: newVersion.word_count,
      hasAnalysis: !!newVersion.analysis,
      timestamp: newVersion.timestamp
    });

    res.status(201).json({
      ...newVersion,
      analysis: newVersion.analysis ? JSON.parse(newVersion.analysis) : null,
      scores: newVersion.scores ? JSON.parse(newVersion.scores) : null
    });
  } catch (error) {
    console.error('Errore POST versione:', error);
    res.status(500).json({ error: 'Errore nella creazione versione' });
  }
});

// GET /api/projects/:id/versions/:versionId - Ottieni una versione specifica
router.get('/:id/versions/:versionId', async (req, res) => {
  try {
    const version = await database.get(`
      SELECT * FROM project_versions 
      WHERE id = ? AND project_id = ?
    `, [req.params.versionId, req.params.id]);

    if (!version) {
      return res.status(404).json({ error: 'Versione non trovata' });
    }

    res.json({
      ...version,
      analysis: version.analysis ? JSON.parse(version.analysis) : null,
      scores: version.scores ? JSON.parse(version.scores) : null
    });
  } catch (error) {
    console.error('Errore GET versione:', error);
    res.status(500).json({ error: 'Errore nel recupero versione' });
  }
});

// DELETE /api/projects/:id/versions/:versionId - Elimina una versione
router.delete('/:id/versions/:versionId', async (req, res) => {
  try {
    const result = await database.run(`
      DELETE FROM project_versions 
      WHERE id = ? AND project_id = ?
    `, [req.params.versionId, req.params.id]);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Versione non trovata' });
    }

    res.json({ message: 'Versione eliminata con successo' });
  } catch (error) {
    console.error('Errore DELETE versione:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione versione' });
  }
});

module.exports = router;
