const express = require('express');
const { v4: uuidv4 } = require('uuid');
const database = require('../database/database');

const router = express.Router();

// GET /api/characters - Ottieni tutti i personaggi
router.get('/', async (req, res) => {
  try {
    const { projectId, versionId } = req.query;

    let sql = 'SELECT * FROM characters';
    let params = [];
    let conditions = [];

    if (projectId) {
      conditions.push('project_id = ?');
      params.push(projectId);
    }

    if (versionId) {
      conditions.push('version_id = ?');
      params.push(versionId);
    }

    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }

    sql += ' ORDER BY created_at DESC';
    
    const characters = await database.all(sql, params);
    
    // Parse JSON fields
    const parsedCharacters = characters.map(character => ({
      ...character,
      physical_traits: character.physical_traits ? JSON.parse(character.physical_traits) : {},
      personality_traits: character.personality_traits ? JSON.parse(character.personality_traits) : {},
      plot_impact: character.plot_impact ? JSON.parse(character.plot_impact) : {}
    }));
    
    res.json(parsedCharacters);
  } catch (error) {
    console.error('Errore GET personaggi:', error);
    res.status(500).json({ error: 'Errore nel recupero personaggi' });
  }
});

// POST /api/characters - Crea un nuovo personaggio
router.post('/', async (req, res) => {
  try {
    // Supporta sia il formato vecchio che quello nuovo
    const {
      projectId,
      versionId,
      name,
      description,
      physicalDescription,
      personality,
      actions,
      dialogues,
      relationships,
      role,
      importance,
      mentions,
      source,
      automatic,
      // Formato vecchio (per compatibilità)
      physicalTraits,
      personalityTraits,
      plotImpact
    } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Nome personaggio richiesto' });
    }

    const characterId = uuidv4();

    // Adatta i dati al formato del database
    const finalDescription = description || physicalDescription || '';
    const finalPhysicalTraits = physicalTraits || (physicalDescription ? { description: physicalDescription } : {});
    const finalPersonalityTraits = personalityTraits || (personality ? { personality } : {});
    const finalPlotImpact = plotImpact || {
      actions: actions || '',
      dialogues: dialogues || '',
      relationships: relationships || '',
      role: role || '',
      importance: importance || 5,
      mentions: mentions || 1,
      source: source || 'manual',
      automatic: automatic || false
    };

    await database.run(`
      INSERT INTO characters (id, project_id, version_id, name, description, physical_traits, personality_traits, plot_impact)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      characterId,
      projectId,
      versionId || null, // Nuovo campo per collegamento versione
      name,
      finalDescription,
      JSON.stringify(finalPhysicalTraits),
      JSON.stringify(finalPersonalityTraits),
      JSON.stringify(finalPlotImpact)
    ]);

    const newCharacter = await database.get(`
      SELECT * FROM characters WHERE id = ?
    `, [characterId]);

    // Parse JSON fields
    newCharacter.physical_traits = newCharacter.physical_traits ? JSON.parse(newCharacter.physical_traits) : {};
    newCharacter.personality_traits = newCharacter.personality_traits ? JSON.parse(newCharacter.personality_traits) : {};
    newCharacter.plot_impact = newCharacter.plot_impact ? JSON.parse(newCharacter.plot_impact) : {};

    res.status(201).json(newCharacter);
  } catch (error) {
    console.error('Errore POST personaggio:', error);
    res.status(500).json({ error: 'Errore nella creazione personaggio' });
  }
});

// PUT /api/characters/:id - Aggiorna un personaggio
router.put('/:id', async (req, res) => {
  try {
    const { name, description, physicalTraits, personalityTraits, plotImpact } = req.body;

    await database.run(`
      UPDATE characters
      SET name = ?, description = ?, physical_traits = ?, personality_traits = ?, plot_impact = ?, updated_at = ?
      WHERE id = ?
    `, [
      name,
      description,
      physicalTraits ? JSON.stringify(physicalTraits) : null,
      personalityTraits ? JSON.stringify(personalityTraits) : null,
      plotImpact ? JSON.stringify(plotImpact) : null,
      new Date().toISOString(),
      req.params.id
    ]);

    const updatedCharacter = await database.get(`
      SELECT * FROM characters WHERE id = ?
    `, [req.params.id]);

    if (!updatedCharacter) {
      return res.status(404).json({ error: 'Personaggio non trovato' });
    }

    // Parse JSON fields
    updatedCharacter.physical_traits = updatedCharacter.physical_traits ? JSON.parse(updatedCharacter.physical_traits) : {};
    updatedCharacter.personality_traits = updatedCharacter.personality_traits ? JSON.parse(updatedCharacter.personality_traits) : {};
    updatedCharacter.plot_impact = updatedCharacter.plot_impact ? JSON.parse(updatedCharacter.plot_impact) : {};

    res.json(updatedCharacter);
  } catch (error) {
    console.error('Errore PUT personaggio:', error);
    res.status(500).json({ error: 'Errore nell\'aggiornamento personaggio' });
  }
});

// DELETE /api/characters/:id - Elimina un personaggio
router.delete('/:id', async (req, res) => {
  try {
    const result = await database.run(`
      DELETE FROM characters WHERE id = ?
    `, [req.params.id]);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Personaggio non trovato' });
    }

    res.json({ message: 'Personaggio eliminato con successo' });
  } catch (error) {
    console.error('Errore DELETE personaggio:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione personaggio' });
  }
});

// DELETE /api/characters/version/:projectId/:versionId - Elimina tutti i personaggi di una versione
router.delete('/version/:projectId/:versionId', async (req, res) => {
  try {
    const { projectId, versionId } = req.params;

    const result = await database.run(`
      DELETE FROM characters
      WHERE project_id = ? AND version_id = ?
    `, [projectId, versionId]);

    console.log(`🗑️ Eliminati ${result.changes} personaggi per versione ${versionId}`);

    res.json({
      message: `Eliminati ${result.changes} personaggi per la versione`,
      deletedCount: result.changes
    });
  } catch (error) {
    console.error('Errore DELETE personaggi versione:', error);
    res.status(500).json({ error: 'Errore nell\'eliminazione personaggi versione' });
  }
});

module.exports = router;
